import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.WILL9700_DB,
});

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const { quantity, type, strike, ExpiryDate, premium, debitCredit } = req.body;

  if (!quantity || !type || !strike || !ExpiryDate || !premium || !debitCredit) {
    return res.status(400).json({ message: 'Missing required fields' });
  }

  try {
    const client = await pool.connect();
    const query = `
      INSERT INTO OPTIONS_TRADES (quantity, type, strike, "ExpiryDate", premium, "debitCredit")
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id;
    `;
    const values = [quantity, type, strike, ExpiryDate, premium, debitCredit];
    const result = await client.query(query, values);
    client.release();

    res.status(201).json({ message: 'Trade saved successfully', id: result.rows[0].id });
  } catch (error) {
    console.error('Error saving trade:', error);
    res.status(500).json({ message: 'Error saving trade', error: error.message });
  }
}