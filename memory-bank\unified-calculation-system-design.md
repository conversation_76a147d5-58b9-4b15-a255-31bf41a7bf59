# Unified P&L Calculation System - Technical Design

## 1. Architecture Overview

### 1.1 Current State Analysis
**Existing Issues:**
- Duplicate calculation logic across `usePnLAtVariousPriceStore.js` and `usePnLAtVariousDateStore.js`
- Tight coupling between calculation logic and Zustand stores
- No volatility dimension analysis capability
- Difficult to extend for new analysis types

**Proposed Solution:**
- Extract pure calculation functions into dedicated utility modules
- Create a unified calculation orchestrator
- Maintain backward compatibility with existing stores
- Enable new multi-dimensional analysis capabilities

### 1.2 New Architecture Flow
```
Input Trades + Parameters
         ↓
   Unified Calculator
         ↓
┌─────────────────────────────────────┐
│  Dimension-Specific Calculators    │
│  ├── Price Calculator              │
│  ├── Time Calculator               │
│  └── Volatility Calculator         │
└─────────────────────────────────────┘
         ↓
   Standardized Output Matrix
         ↓
┌─────────────────────────────────────┐
│     Consumer Components             │
│  ├── Strategy Planner              │
│  ├── Existing Analysis Charts      │
│  └── Future Analysis Tools         │
└─────────────────────────────────────┘
```

## 2. Core Function Signatures

### 2.1 Main Unified Calculator
```javascript
/**
 * Unified P&L calculation function for all analysis dimensions
 * @param {Object} config - Calculation configuration
 * @returns {Object} Multi-dimensional P&L results
 */
function calculateUnifiedPnL({
  trades,           // Array of trade objects
  baseParams,       // Base analysis parameters
  dimensions,       // Which dimensions to calculate
  ranges           // Range configurations for each dimension
}) {
  return {
    priceAnalysis: { /* price dimension results */ },
    timeAnalysis: { /* time dimension results */ },
    volatilityAnalysis: { /* volatility dimension results */ },
    metadata: { /* calculation metadata */ }
  };
}
```

### 2.2 Parameter Interfaces
```typescript
interface BaseAnalysisParams {
  stockPrice: number;
  volatility: number;
  riskFreeRate: number;
  targetDate?: Date;
  daysToVisualize?: number;
}

interface CalculationRanges {
  price?: {
    enabled: boolean;
    rangePercent: number;    // e.g., 0.05 for ±5%
    pointCount?: number;     // Optional override for point count
    customPoints?: number[]; // Custom price points
  };
  time?: {
    enabled: boolean;
    startDate: Date;
    endDate: Date;
    intervalDays?: number;   // Optional custom interval
  };
  volatility?: {
    enabled: boolean;
    minVol: number;          // e.g., 10 for 10%
    maxVol: number;          // e.g., 50 for 50%
    stepSize: number;        // e.g., 5 for 5% increments
  };
}

interface CalculationDimensions {
  price: boolean;
  time: boolean;
  volatility: boolean;
}
```

## 3. Modular Function Design

### 3.1 Core Calculation Utilities
```javascript
// src/utils/pnl-calculations/core-calculator.js

/**
 * Calculate P&L for a single trade at specific parameters
 * @param {Object} trade - Trade object
 * @param {Object} params - Calculation parameters
 * @returns {Object} Trade P&L results
 */
export function calculateTradeAtPoint(trade, { stockPrice, volatility, riskFreeRate, daysToExpiry }) {
  const positionForCalc = {
    ...trade,
    daysToExpiry: Math.max(0, daysToExpiry)
  };

  const premium = calculatePositionPremium(
    positionForCalc,
    stockPrice,
    riskFreeRate,
    volatility
  );

  const marketValue = Math.round(trade.quantity * premium);
  const pnl = marketValue - (trade.debitCredit || 0);

  return {
    premium,
    marketValue,
    pnl,
    intrinsicValue: calculateIntrinsicValue(trade, stockPrice)
  };
}

/**
 * Calculate portfolio P&L at a specific point
 * @param {Array} trades - Array of trades
 * @param {Object} params - Calculation parameters
 * @returns {Object} Portfolio totals
 */
export function calculatePortfolioAtPoint(trades, params) {
  let totalMarketValue = 0;
  let totalPnL = 0;
  const tradeResults = [];

  trades.forEach(trade => {
    const result = calculateTradeAtPoint(trade, params);
    tradeResults.push({ tradeId: trade.id, ...result });
    totalMarketValue += result.marketValue;
    totalPnL += result.pnl;
  });

  return {
    totalMarketValue,
    totalPnL,
    tradeResults,
    params
  };
}
```

### 3.2 Dimension-Specific Calculators
```javascript
// src/utils/pnl-calculations/price-calculator.js

/**
 * Calculate P&L across various stock prices
 * @param {Array} trades - Array of trades
 * @param {Object} baseParams - Base calculation parameters
 * @param {Object} priceRange - Price range configuration
 * @returns {Object} Price analysis results
 */
export function calculatePriceAnalysis(trades, baseParams, priceRange) {
  const pricePoints = generatePricePoints(baseParams.stockPrice, priceRange);
  const results = {
    pricePoints,
    portfolioResults: {},
    tradeResults: {}
  };

  pricePoints.forEach(price => {
    const params = {
      ...baseParams,
      stockPrice: price,
      daysToExpiry: calculateDaysToExpiry(baseParams.targetDate || new Date())
    };

    const portfolioResult = calculatePortfolioAtPoint(trades, params);
    results.portfolioResults[price] = portfolioResult;
    
    // Store individual trade results for detailed analysis
    portfolioResult.tradeResults.forEach(tradeResult => {
      if (!results.tradeResults[tradeResult.tradeId]) {
        results.tradeResults[tradeResult.tradeId] = {};
      }
      results.tradeResults[tradeResult.tradeId][price] = tradeResult;
    });
  });

  return results;
}

// src/utils/pnl-calculations/time-calculator.js

/**
 * Calculate P&L across various future dates
 * @param {Array} trades - Array of trades
 * @param {Object} baseParams - Base calculation parameters
 * @param {Object} timeRange - Time range configuration
 * @returns {Object} Time analysis results
 */
export function calculateTimeAnalysis(trades, baseParams, timeRange) {
  const datePoints = generateDatePoints(timeRange.startDate, timeRange.endDate, timeRange.intervalDays);
  const results = {
    datePoints,
    portfolioResults: {},
    tradeResults: {}
  };

  datePoints.forEach(date => {
    const daysToExpiry = calculateDaysToExpiry(date);
    const params = {
      ...baseParams,
      daysToExpiry
    };

    const portfolioResult = calculatePortfolioAtPoint(trades, params);
    const dateKey = formatDateString(date);
    results.portfolioResults[dateKey] = portfolioResult;
    
    portfolioResult.tradeResults.forEach(tradeResult => {
      if (!results.tradeResults[tradeResult.tradeId]) {
        results.tradeResults[tradeResult.tradeId] = {};
      }
      results.tradeResults[tradeResult.tradeId][dateKey] = tradeResult;
    });
  });

  return results;
}

// src/utils/pnl-calculations/volatility-calculator.js

/**
 * Calculate P&L across various volatility levels
 * @param {Array} trades - Array of trades
 * @param {Object} baseParams - Base calculation parameters
 * @param {Object} volRange - Volatility range configuration
 * @returns {Object} Volatility analysis results
 */
export function calculateVolatilityAnalysis(trades, baseParams, volRange) {
  const volPoints = generateVolatilityPoints(volRange.minVol, volRange.maxVol, volRange.stepSize);
  const results = {
    volatilityPoints: volPoints,
    portfolioResults: {},
    tradeResults: {}
  };

  volPoints.forEach(volatility => {
    const params = {
      ...baseParams,
      volatility,
      daysToExpiry: calculateDaysToExpiry(baseParams.targetDate || new Date())
    };

    const portfolioResult = calculatePortfolioAtPoint(trades, params);
    results.portfolioResults[volatility] = portfolioResult;
    
    portfolioResult.tradeResults.forEach(tradeResult => {
      if (!results.tradeResults[tradeResult.tradeId]) {
        results.tradeResults[tradeResult.tradeId] = {};
      }
      results.tradeResults[tradeResult.tradeId][volatility] = tradeResult;
    });
  });

  return results;
}
```

### 3.3 Point Generation Utilities
```javascript
// src/utils/pnl-calculations/point-generators.js

/**
 * Generate price points for analysis (enhanced from existing logic)
 * @param {number} basePrice - Base stock price
 * @param {Object} config - Price range configuration
 * @returns {Array} Array of price points
 */
export function generatePricePoints(basePrice, config) {
  if (config.customPoints) {
    return [...config.customPoints].sort((a, b) => a - b);
  }

  const range = basePrice * config.rangePercent;
  const minPrice = basePrice - range;
  const maxPrice = basePrice + range;

  // Use existing logic from usePnLAtVariousPriceStore.js
  if (basePrice > 1000) {
    return generateIndexPricePoints(basePrice, minPrice, maxPrice);
  } else {
    return generateStockPricePoints(basePrice, minPrice, maxPrice);
  }
}

/**
 * Generate date points for time analysis
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {number} intervalDays - Days between points
 * @returns {Array} Array of date objects
 */
export function generateDatePoints(startDate, endDate, intervalDays = 1) {
  const points = [];
  const current = new Date(startDate);
  
  while (current <= endDate) {
    points.push(new Date(current));
    current.setDate(current.getDate() + intervalDays);
  }
  
  return points;
}

/**
 * Generate volatility points for analysis
 * @param {number} minVol - Minimum volatility (%)
 * @param {number} maxVol - Maximum volatility (%)
 * @param {number} stepSize - Step size (%)
 * @returns {Array} Array of volatility values
 */
export function generateVolatilityPoints(minVol, maxVol, stepSize) {
  const points = [];
  for (let vol = minVol; vol <= maxVol; vol += stepSize) {
    points.push(Math.round(vol * 100) / 100); // Round to 2 decimal places
  }
  return points;
}
```

## 4. Main Unified Calculator Implementation

```javascript
// src/utils/pnl-calculations/unified-calculator.js

import { calculatePriceAnalysis } from './price-calculator.js';
import { calculateTimeAnalysis } from './time-calculator.js';
import { calculateVolatilityAnalysis } from './volatility-calculator.js';

/**
 * Unified P&L calculation function
 * @param {Object} config - Calculation configuration
 * @returns {Object} Multi-dimensional P&L results
 */
export function calculateUnifiedPnL({
  trades,
  baseParams,
  dimensions = { price: true, time: false, volatility: false },
  ranges = {}
}) {
  // Validate inputs
  if (!Array.isArray(trades) || trades.length === 0) {
    return createEmptyResults();
  }

  const results = {
    metadata: {
      calculatedAt: new Date().toISOString(),
      tradeCount: trades.length,
      baseParams,
      dimensions,
      ranges
    }
  };

  // Calculate each requested dimension
  if (dimensions.price && ranges.price?.enabled) {
    console.log('Calculating price analysis...');
    results.priceAnalysis = calculatePriceAnalysis(trades, baseParams, ranges.price);
  }

  if (dimensions.time && ranges.time?.enabled) {
    console.log('Calculating time analysis...');
    results.timeAnalysis = calculateTimeAnalysis(trades, baseParams, ranges.time);
  }

  if (dimensions.volatility && ranges.volatility?.enabled) {
    console.log('Calculating volatility analysis...');
    results.volatilityAnalysis = calculateVolatilityAnalysis(trades, baseParams, ranges.volatility);
  }

  return results;
}

/**
 * Create empty results structure
 * @returns {Object} Empty results object
 */
function createEmptyResults() {
  return {
    priceAnalysis: null,
    timeAnalysis: null,
    volatilityAnalysis: null,
    metadata: {
      calculatedAt: new Date().toISOString(),
      tradeCount: 0,
      error: 'No trades provided'
    }
  };
}

/**
 * Convenience function for price-only analysis (backward compatibility)
 * @param {Array} trades - Array of trades
 * @param {Object} params - Analysis parameters
 * @returns {Object} Price analysis results in legacy format
 */
export function calculatePnLAtVariousPrices(trades, params) {
  const config = {
    trades,
    baseParams: {
      stockPrice: params.targetStockPrice,
      volatility: params.volatility,
      riskFreeRate: params.riskFreeRate,
      daysToVisualize: params.daysToVisualize
    },
    dimensions: { price: true, time: false, volatility: false },
    ranges: {
      price: {
        enabled: true,
        rangePercent: 0.05 // 5% range
      }
    }
  };

  const results = calculateUnifiedPnL(config);
  
  // Transform to legacy format for backward compatibility
  return transformToLegacyPriceFormat(results.priceAnalysis);
}

/**
 * Convenience function for time-only analysis (backward compatibility)
 * @param {Array} trades - Array of trades
 * @param {Object} params - Analysis parameters
 * @returns {Object} Time analysis results in legacy format
 */
export function calculatePnLAtVariousDates(trades, params) {
  const maxExpiryDate = findMaxExpiryDate(trades);
  
  const config = {
    trades,
    baseParams: {
      stockPrice: params.targetStockPrice,
      volatility: params.volatility,
      riskFreeRate: params.riskFreeRate
    },
    dimensions: { price: false, time: true, volatility: false },
    ranges: {
      time: {
        enabled: true,
        startDate: new Date(),
        endDate: maxExpiryDate,
        intervalDays: 1
      }
    }
  };

  const results = calculateUnifiedPnL(config);
  
  // Transform to legacy format for backward compatibility
  return transformToLegacyTimeFormat(results.timeAnalysis);
}
```

## 5. Retrofitting Strategy for Existing Components

### 5.1 Store Migration Plan
```javascript
// src/store/usePnLAtVariousPriceStore.js (Updated)

import { calculatePnLAtVariousPrices } from '../utils/pnl-calculations/unified-calculator.js';

const usePnLAtVariousPriceStore = create((set, get) => ({
  // ... existing state ...

  // Updated recalculation method using new unified system
  recalculate: () => {
    const currentParams = get()._getCurrentParams();
    const allTrades = useAllTradesStore.getState().allTrades;
    
    const filteredTrades = allTrades.filter(trade => 
      trade.stock?.toUpperCase() === currentParams.symbol?.toUpperCase()
    );

    set({ isCalculating: true });

    try {
      // Use new unified calculator with backward compatibility wrapper
      const pnlData = calculatePnLAtVariousPrices(filteredTrades, currentParams);

      set({
        pnlData,
        _lastTrades: allTrades,
        _lastAnalysisParams: currentParams,
        lastCalculationTime: new Date().toISOString(),
        isCalculating: false
      });
    } catch (error) {
      console.error('Error calculating PnL data:', error);
      set({ isCalculating: false });
    }
  }
}));
```

### 5.2 Component Migration Example
```javascript
// Existing components can gradually migrate to use unified calculator directly

// Before (in TradesPnLChart.jsx):
const { pnlData } = usePnLAtVariousPriceStore();

// After (optional migration):
const unifiedResults = calculateUnifiedPnL({
  trades: filteredTrades,
  baseParams: analysisParams,
  dimensions: { price: true, time: false, volatility: false },
  ranges: { price: { enabled: true, rangePercent: 0.05 } }
});
```

## 6. Strategy Planner Integration

### 6.1 Strategy Store Implementation
```javascript
// src/store/useStrategyStore.js

import { calculateUnifiedPnL } from '../utils/pnl-calculations/unified-calculator.js';

export const createStrategyStore = (strategyId) => create((set, get) => ({
  strategyId,
  trades: [],
  analysisParams: {
    stockPrice: 100,
    volatility: 25,
    riskFreeRate: 2.5,
    targetDate: new Date()
  },
  pnlResults: null,
  isCalculating: false,

  // Calculate all three dimensions for strategy analysis
  calculateAllDimensions: async () => {
    const { trades, analysisParams } = get();
    
    set({ isCalculating: true });

    try {
      const results = calculateUnifiedPnL({
        trades,
        baseParams: analysisParams,
        dimensions: { price: true, time: true, volatility: true },
        ranges: {
          price: { enabled: true, rangePercent: 0.05 },
          time: { 
            enabled: true, 
            startDate: new Date(), 
            endDate: findMaxExpiryDate(trades) 
          },
          volatility: { 
            enabled: true, 
            minVol: 10, 
            maxVol: 50, 
            stepSize: 5 
          }
        }
      });

      set({ pnlResults: results, isCalculating: false });
    } catch (error) {
      console.error('Strategy calculation error:', error);
      set({ isCalculating: false });
    }
  }
}));
```

## 7. Benefits of Unified System

### 7.1 Code Reuse & Maintainability
- **Single Source of Truth**: All P&L calculations use the same core logic
- **Reduced Duplication**: Eliminates duplicate calculation code
- **Easier Testing**: Centralized calculation logic is easier to unit test
- **Consistent Results**: Ensures all components show identical calculations

### 7.2 Extensibility
- **New Dimensions**: Easy to add new analysis dimensions (e.g., interest rate sensitivity)
- **Custom Ranges**: Flexible range configuration for different use cases
- **Performance Optimization**: Centralized caching and memoization opportunities

### 7.3 Backward Compatibility
- **Gradual Migration**: Existing components continue working during transition
- **Legacy Wrappers**: Convenience functions maintain existing APIs
- **No Breaking Changes**: Existing functionality remains intact

## 8. Performance Considerations

### 8.1 Optimization Strategies
```javascript
// Memoization for expensive calculations
const memoizedCalculateTradeAtPoint = memoize(calculateTradeAtPoint, {
  maxSize: 1000,
  keyGenerator: (trade, params) => 
    `${trade.id}-${params.stockPrice}-${params.volatility}-${params.daysToExpiry}`
});

// Batch processing for large trade sets
function calculateBatchPnL(trades, parameterSets) {
  return parameterSets.map(params => 
    calculatePortfolioAtPoint(trades, params)
  );
}

// Web Worker support for heavy calculations
function calculateUnifiedPnLAsync(config) {
  return new Promise((resolve) => {
    const worker = new Worker('/workers/pnl-calculator.js');
    worker.postMessage(config);
    worker.onmessage = (e) => resolve(e.data);
  });
}
```

### 8.2 Memory Management
- **Lazy Calculation**: Only calculate requested dimensions
- **Result Streaming**: Stream results for large datasets
- **Cache Limits**: Implement LRU cache with size limits
- **Cleanup**: Proper cleanup of calculation results when components unmount

## 9. Testing Strategy

### 9.1 Unit Tests
```javascript
// Test individual calculation functions
describe('calculateTradeAtPoint', () => {
  it('should calculate correct premium for call option', () => {
    const trade = { type: 'Call', strike: 100, quantity: 1, debitCredit: 500 };
    const params = { stockPrice: 105, volatility: 25, riskFreeRate: 2.5, daysToExpiry: 30 };
    
    const result = calculateTradeAtPoint(trade, params);
    
    expect(result.premium).toBeCloseTo(expectedPremium, 2);
    expect(result.pnl).toBe(result.marketValue - trade.debitCredit);
  });
});
```

### 9.2 Integration Tests
```javascript
// Test unified calculator with multiple dimensions
describe('calculateUnifiedPnL', () => {
  it('should calculate all three dimensions correctly', () => {
    const config = {
      trades: mockTrades,
      baseParams: mockParams,
      dimensions: { price: true, time: true, volatility: true },
      ranges: mockRanges
    };
    
    const results = calculateUnifiedPnL(config);
    
    expect(results.priceAnalysis).toBeDefined();
    expect(results.timeAnalysis).toBeDefined();
    expect(results.volatilityAnalysis).toBeDefined();
  });
});
```

## 10. Migration Timeline

### Week 1: Core Infrastructure
- Implement core calculation utilities
- Create point generation functions
- Build unified calculator framework

### Week 2: Dimension Calculators
- Implement price analysis calculator
- Implement time analysis calculator
- Implement volatility analysis calculator

### Week 3: Integration & Compatibility
- Create backward compatibility wrappers
- Update existing stores to use new system
- Test existing components with new calculations

### Week 4: Strategy Planner Integration
- Implement strategy-specific stores
- Build multi-dimensional analysis views
- Performance optimization and testing

This unified calculation system provides a solid foundation for both the Strategy Planner feature and improves the existing codebase through better modularity and reusability.