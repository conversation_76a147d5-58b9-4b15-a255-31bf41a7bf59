// Test script to verify the trade parser fix for zero quantity and last @ position
let parseTrades;
try {
  ({ parseTrades } = require('./src/server-utils/trade-parser.js'));
  console.log('Successfully loaded trade parser module');
} catch (error) {
  console.error('Error loading trade parser:', error.message);
  process.exit(1);
}

// Test case from the user's example
const testLine = "HSIN5\t 2025-07\t0@-800.00\t\t2@24780.00\t-2@24800.00\t\t0@-840.00\t24672\t42,000.00 HKD\t24445\t1.0000\t42,000.00 HKD\t50";

console.log('Testing trade parser fix...');
console.log('Input line:', testLine);
console.log('');

let result;
try {
  result = parseTrades(testLine);
  console.log('parseTrades returned:', result);
} catch (error) {
  console.error('Error calling parseTrades:', error.message);
  process.exit(1);
}

if (result && result.length > 0) {
  const trade = result[0];
  console.log('Parsed trade:');
  console.log('- Ticker:', trade.ticker);
  console.log('- Type:', trade.type);
  console.log('- Expiry:', trade.expiryDate);
  console.log('- Strike:', trade.strike);
  console.log('- Quantity:', trade.quantity);
  console.log('- Cost:', trade.cost);
  console.log('- Stock:', trade.stock);
  console.log('');
  
  // Verify the fix
  const expectedQuantity = 0;
  const expectedCost = -840.00;
  
  if (trade.quantity === expectedQuantity) {
    console.log('✅ PASS: Quantity correctly parsed as', expectedQuantity);
  } else {
    console.log('❌ FAIL: Quantity should be', expectedQuantity, 'but got', trade.quantity);
  }
  
  if (Math.abs(trade.cost - expectedCost) < 0.01) {
    console.log('✅ PASS: Cost correctly parsed as', expectedCost, '(last @ position)');
  } else {
    console.log('❌ FAIL: Cost should be', expectedCost, 'but got', trade.cost);
  }
} else {
  console.log('❌ FAIL: No trade parsed from the input line');
}
