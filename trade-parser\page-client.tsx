"use client";

import { useState } from "react";
import type { ParsedTrade } from "@/types/trade";
import { parseTradesAction, uploadTradesAction } from "@/app/actions";
import { useToast } from "@/hooks/use-toast";
import { TradeInputForm } from "./trade-input-form";
import { TradePreviewTable } from "./trade-preview-table";

export function PageClientContent() {
  const [rawText, setRawText] = useState<string>("");
  const [parsedTrades, setParsedTrades] = useState<ParsedTrade[]>([]);
  const [isParsing, setIsParsing] = useState<boolean>(false);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const { toast } = useToast();

  const handleParseTrades = async () => {
    if (!rawText.trim()) {
      toast({
        title: "Input Required",
        description: "Please paste some trade data before parsing.",
        variant: "destructive",
      });
      return;
    }
    setIsParsing(true);
    setParsedTrades([]); // Clear previous results
    try {
      const result = await parseTradesAction(rawText);
      if (result.error) {
        toast({
          title: "Parsing Error",
          description: result.error,
          variant: "destructive",
        });
        setParsedTrades([]);
      } else if (result.data) {
        setParsedTrades(result.data);
        toast({
          title: "Parsing Successful",
          description: `Found ${result.data.length} trades. Please review them below.`,
        });
        if (result.data.length === 0 && rawText.trim().length > 0) {
            toast({
                title: "No Trades Found",
                description: "The parser ran successfully but did not find any trades in the provided text. Please check the format.",
                variant: "default",
            });
        }
      }
    } catch (error) {
      toast({
        title: "Parsing Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred.",
        variant: "destructive",
      });
      setParsedTrades([]);
    } finally {
      setIsParsing(false);
    }
  };

  const handleUploadTrades = async () => {
    if (parsedTrades.length === 0) {
      toast({
        title: "No Trades to Upload",
        description: "Please parse some trades first.",
        variant: "destructive",
      });
      return;
    }
    setIsUploading(true);
    try {
      const result = await uploadTradesAction(parsedTrades);
      if (result.success) {
        toast({
          title: "Upload Successful",
          description: result.message,
        });
        // Optionally clear trades after successful upload
        // setParsedTrades([]);
        // setRawText(""); 
      } else {
        toast({
          title: "Upload Failed",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Upload Error",
        description: error instanceof Error ? error.message : "An unexpected server error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-8">
      <TradeInputForm
        rawText={rawText}
        onRawTextChange={setRawText}
        onParse={handleParseTrades}
        isParsing={isParsing}
      />
      <TradePreviewTable
        trades={parsedTrades}
        onUpload={handleUploadTrades}
        isUploading={isUploading}
      />
    </div>
  );
}
