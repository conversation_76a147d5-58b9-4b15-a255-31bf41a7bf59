Testing @ position parsing...
Input line: HSIN5	 2025-07	0@-800.00		2@24780.00	-2@24800.00		0@-840.00	24672	42,000.00 HKD	24445	1.0000	42,000.00 HKD	50
Parts after splitting by tabs: ["HSIN5","2025-07","0@-800.00","2@24780.00","-2@24800.00","0@-840.00","24672","42,000.00 HKD","24445","1.0000","42,000.00 HKD","50"]
Parts containing @: ["0@-800.00","2@24780.00","-2@24800.00","0@-840.00"]
Last @ part: 0@-840.00
Quantity string: 0
Price string: -840.00
Parsed quantity (parseInt): 0
Is NaN? false
Final quantity: 0
Final price: -840

Expected quantity: 0
Expected price: -840
Quantity correct? true
Price correct? true
