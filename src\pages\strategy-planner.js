import React, { useEffect } from 'react';
import StrategyManagementTable from '../components/strategy-planner/StrategyManagementTable';
import StrategyWindowManager from '../components/strategy-planner/StrategyWindowManager';
import useStrategyManagementStore from '../store/useStrategyManagementStore';
import useStrategyWindowStore from '../store/useStrategyWindowStore';

/**
 * Strategy Planner Page
 * 
 * This is the main page for the Strategy Planner feature. It displays the strategy
 * management table and serves as the entry point for creating and managing strategies.
 */
const StrategyPlannerPage = () => {
  const { loadStrategies, strategies, isLoading, error } = useStrategyManagementStore();
  const { openWindow } = useStrategyWindowStore();

  // Load strategies when component mounts
  useEffect(() => {
    loadStrategies();
  }, [loadStrategies]);

  // Handle URL parameter for auto-opening a strategy
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const openStrategyId = urlParams.get('openStrategy');

    if (openStrategyId && strategies.length > 0) {
      const strategy = strategies.find(s => s.id === openStrategyId);
      if (strategy) {
        console.log('Auto-opening strategy from URL:', openStrategyId);
        const windowId = openWindow(openStrategyId, strategy);
        if (windowId) {
          // Clear the URL parameter after opening
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
        }
      }
    }
  }, [strategies, openWindow]);

  return (
    <>
      <div className="strategy-planner-page">
        {/* Page Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Strategy Planner
          </h1>
          <p className="text-gray-600">
            Create, manage, and analyze multiple options trading strategies.
            Each strategy operates as an independent workspace with its own trades and P&L analysis.
          </p>
        </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error loading strategies
              </h3>
              <div className="mt-2 text-sm text-red-700">
                {error}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="mb-6 flex justify-center">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Loading strategies...</span>
          </div>
        </div>
      )}

      {/* Strategy Management Table */}
      <StrategyManagementTable />

      {/* Help Section */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
        <h3 className="text-lg font-medium text-blue-900 mb-2">
          Getting Started
        </h3>
        <div className="text-sm text-blue-800 space-y-2">
          <p>
            <strong>1. Create a Strategy:</strong> Click "Add Strategy" to create a new strategy workspace.
          </p>
          <p>
            <strong>2. Add Trades:</strong> Click on a Strategy ID to open the strategy window and add trades.
          </p>
          <p>
            <strong>3. Analyze P&L:</strong> Use the P&L Analysis tab to simulate different market scenarios.
          </p>
          <p>
            <strong>4. Compare Strategies:</strong> Open multiple strategy windows to compare performance side-by-side.
          </p>
        </div>
      </div>
      </div>

      {/* Strategy Window Manager */}
      <StrategyWindowManager />
    </>
  );
};

export default StrategyPlannerPage;
