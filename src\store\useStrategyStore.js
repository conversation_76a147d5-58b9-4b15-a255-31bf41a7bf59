import { create } from 'zustand';
import { StrategyTypes } from '../types/strategy';
import { calculatePositionPremium, parseExpiryDate, calculateDaysToExpiry } from '../utils/position-utils';

/**
 * Individual Strategy Store Factory
 * 
 * Creates a Zustand store for managing individual strategy data.
 * Each strategy window gets its own store instance.
 */
const createStrategyStore = (strategyId) => create((set, get) => ({
  // Strategy metadata
  strategyId,
  strategyMetadata: null,
  
  // Strategy data
  trades: [],
  
  // Analysis parameters
  simulationParams: {
    ...StrategyTypes.DEFAULT_SIMULATION_PARAMS
  },
  
  // P&L results
  pnlResults: {
    priceAnalysis: [],
    timeDecayAnalysis: [],
    volatilityAnalysis: [],
    calculatedAt: null,
    parameters: null
  },
  
  // UI state
  activeTab: 'trades', // 'trades' or 'analysis'
  activeAnalysisView: 'price', // 'price', 'time', 'volatility'
  isLoading: false,
  isCalculating: false,
  error: null,

  // Actions
  setLoading: (loading) => set({ isLoading: loading }),
  setCalculating: (calculating) => set({ isCalculating: calculating }),
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),

  // Strategy metadata management
  loadStrategyMetadata: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await fetch(`/api/strategies/${strategyId}`);
      if (!response.ok) {
        throw new Error(`Failed to load strategy: ${response.statusText}`);
      }
      
      const strategy = await response.json();
      
      // Convert date strings to Date objects
      const processedStrategy = {
        ...strategy,
        createdAt: new Date(strategy.createdAt),
        lastModified: new Date(strategy.lastModified)
      };
      
      set({ strategyMetadata: processedStrategy, isLoading: false });
      return processedStrategy;
    } catch (error) {
      console.error('Error loading strategy metadata:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  updateStrategyMetadata: async (updates) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await fetch(`/api/strategies/${strategyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update strategy: ${response.statusText}`);
      }
      
      const updatedStrategy = await response.json();
      
      // Convert date strings to Date objects
      const processedStrategy = {
        ...updatedStrategy,
        createdAt: new Date(updatedStrategy.createdAt),
        lastModified: new Date(updatedStrategy.lastModified)
      };
      
      set({ strategyMetadata: processedStrategy, isLoading: false });
      return processedStrategy;
    } catch (error) {
      console.error('Error updating strategy metadata:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Trades management
  loadTrades: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await fetch(`/api/strategies/${strategyId}/trades`);
      if (!response.ok) {
        throw new Error(`Failed to load trades: ${response.statusText}`);
      }

      const rawTrades = await response.json();

      // Process trades to ensure proper format for P&L calculations
      const processedTrades = rawTrades.map(trade => {
        const expiryDate = parseExpiryDate(trade.ExpiryDate || trade.expiryDate || trade.expirydate);
        const daysToExpiry = calculateDaysToExpiry(expiryDate);

        return {
          ...trade,
          ExpiryDate: expiryDate, // Ensure consistent field name
          daysToExpiry: daysToExpiry,
          premium: parseFloat(trade.premium || trade.cost || 0),
          debitCredit: trade.debitCredit || (parseFloat(trade.premium || trade.cost || 0) * Math.abs(parseInt(trade.quantity || 0)))
        };
      });

      console.log('Loaded and processed trades:', processedTrades);

      set({ trades: processedTrades, isLoading: false });

      // Recalculate P&L if we have simulation parameters
      const { simulationParams } = get();
      if (simulationParams && processedTrades.length > 0) {
        get().calculatePnL();
      }

      return processedTrades;
    } catch (error) {
      console.error('Error loading trades:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  addTrade: async (tradeData) => {
    set({ isLoading: true, error: null });

    try {
      // Prepare trade data for strategy collection
      const tradeForStrategy = {
        strategyId,
        type: tradeData.type || tradeData.call_put,
        strike: parseFloat(tradeData.strike),
        ExpiryDate: tradeData.expiryDate,
        quantity: parseInt(tradeData.quantity),
        premium: parseFloat(tradeData.cost || tradeData.premium),
        debitCredit: parseFloat(tradeData.cost || tradeData.premium) * Math.abs(parseInt(tradeData.quantity)),
        stock: tradeData.stock,
        createdAt: new Date().toISOString(),
        // Additional fields for compatibility
        call_put: tradeData.type || tradeData.call_put,
        expirydate: tradeData.expiryDate,
        cost: parseFloat(tradeData.cost || tradeData.premium)
      };

      console.log('Adding trade to strategy collection:', tradeForStrategy);

      // Save to strategies collection
      const response = await fetch('/api/strategy-trades', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tradeForStrategy),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Trade addition failed:', errorData);
        throw new Error(`Failed to add trade: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Trade addition result:', result);

      // Create a trade object for the UI with proper format
      const newTrade = {
        id: result.tradeId,
        ...tradeForStrategy,
        daysToExpiry: calculateDaysToExpiry(parseExpiryDate(tradeForStrategy.ExpiryDate))
      };

      set(state => ({
        trades: [...state.trades, newTrade],
        isLoading: false
      }));

      // Recalculate P&L
      get().calculatePnL();

      return newTrade;
    } catch (error) {
      console.error('Error adding trade:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  updateTrade: async (tradeId, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      // Implementation would depend on having an update trade API endpoint
      // For now, we'll update locally and recalculate
      set(state => ({
        trades: state.trades.map(trade =>
          trade.id === tradeId ? { ...trade, ...updates } : trade
        ),
        isLoading: false
      }));
      
      // Recalculate P&L
      get().calculatePnL();
    } catch (error) {
      console.error('Error updating trade:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  deleteTrade: async (tradeId) => {
    set({ isLoading: true, error: null });
    
    try {
      // Implementation would depend on having a delete trade API endpoint
      // For now, we'll delete locally and recalculate
      set(state => ({
        trades: state.trades.filter(trade => trade.id !== tradeId),
        isLoading: false
      }));
      
      // Recalculate P&L
      get().calculatePnL();
    } catch (error) {
      console.error('Error deleting trade:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Simulation parameters management
  updateSimulationParams: (newParams) => {
    set(state => ({
      simulationParams: { ...state.simulationParams, ...newParams }
    }));
    
    // Trigger P&L recalculation
    get().calculatePnL();
  },

  resetSimulationParams: () => {
    set({ simulationParams: { ...StrategyTypes.DEFAULT_SIMULATION_PARAMS } });
    get().calculatePnL();
  },

  // P&L calculation
  calculatePnL: async () => {
    const { trades, simulationParams } = get();

    if (trades.length === 0) {
      set({
        pnlResults: {
          priceAnalysis: [],
          timeDecayAnalysis: [],
          volatilityAnalysis: [],
          calculatedAt: null,
          parameters: null
        }
      });
      return;
    }

    set({ isCalculating: true, error: null });

    try {
      console.log('Calculating P&L for strategy trades:', trades.length);
      console.log('Simulation parameters:', simulationParams);

      // Convert trades to the format expected by P&L calculations
      const processedTrades = trades.map(trade => {
        const expiryDate = parseExpiryDate(trade.ExpiryDate || trade.expiryDate || trade.expirydate);
        const daysToExpiry = calculateDaysToExpiry(expiryDate);

        console.log('Processing trade for P&L:', {
          id: trade.id,
          type: trade.type,
          strike: trade.strike,
          quantity: trade.quantity,
          premium: trade.premium,
          debitCredit: trade.debitCredit,
          ExpiryDate: trade.ExpiryDate,
          daysToExpiry
        });

        return {
          id: trade.id,
          type: trade.type || trade.call_put,
          strike: parseFloat(trade.strike),
          quantity: parseInt(trade.quantity),
          premium: parseFloat(trade.premium || trade.cost),
          debitCredit: trade.debitCredit || (parseFloat(trade.premium || trade.cost) * Math.abs(parseInt(trade.quantity))),
          daysToExpiry: daysToExpiry,
          ExpiryDate: expiryDate // Use ExpiryDate format expected by existing functions
        };
      });

      // Calculate price analysis (P&L at various price points)
      const priceAnalysis = calculatePriceAnalysis(processedTrades, simulationParams);

      // Calculate time decay analysis (P&L over time)
      const timeDecayAnalysis = calculateTimeDecayAnalysis(processedTrades, simulationParams);

      // Calculate volatility analysis (P&L at various volatility levels)
      const volatilityAnalysis = calculateVolatilityAnalysis(processedTrades, simulationParams);

      const results = {
        priceAnalysis,
        timeDecayAnalysis,
        volatilityAnalysis,
        calculatedAt: new Date(),
        parameters: { ...simulationParams }
      };

      console.log('P&L calculation completed:', results);

      set({
        pnlResults: results,
        isCalculating: false
      });

      return results;
    } catch (error) {
      console.error('Error calculating P&L:', error);
      set({ error: error.message, isCalculating: false });
      throw error;
    }
  },

  // UI state management
  setActiveTab: (tab) => {
    set({ activeTab: tab });
  },

  setActiveAnalysisView: (view) => {
    set({ activeAnalysisView: view });
  },

  // Utility functions
  getTradesSummary: () => {
    const { trades } = get();
    
    if (trades.length === 0) {
      return {
        totalTrades: 0,
        totalPremium: 0,
        netDelta: 0,
        netGamma: 0,
        netTheta: 0,
        netVega: 0
      };
    }
    
    // Calculate summary statistics
    const summary = trades.reduce((acc, trade) => {
      const quantity = trade.quantity || 0;
      const premium = trade.premium || 0;
      
      return {
        totalTrades: acc.totalTrades + 1,
        totalPremium: acc.totalPremium + (premium * quantity),
        // Greeks would be calculated here if available
        netDelta: acc.netDelta,
        netGamma: acc.netGamma,
        netTheta: acc.netTheta,
        netVega: acc.netVega
      };
    }, {
      totalTrades: 0,
      totalPremium: 0,
      netDelta: 0,
      netGamma: 0,
      netTheta: 0,
      netVega: 0
    });
    
    return summary;
  },

  // Initialize strategy store
  initialize: async () => {
    try {
      await get().loadStrategyMetadata();
      await get().loadTrades();
    } catch (error) {
      console.error('Error initializing strategy store:', error);
    }
  }
}));

// Store instances cache
const storeInstances = new Map();

/**
 * Get or create a strategy store instance
 */
export const useStrategyStore = (strategyId) => {
  if (!storeInstances.has(strategyId)) {
    storeInstances.set(strategyId, createStrategyStore(strategyId));
  }
  return storeInstances.get(strategyId);
};

/**
 * Clean up store instance when strategy window is closed
 */
export const cleanupStrategyStore = (strategyId) => {
  storeInstances.delete(strategyId);
};

// Helper functions for P&L calculations

/**
 * Calculate P&L at various price points (using same logic as usePnLAtVariousPriceStore)
 */
const calculatePriceAnalysis = (trades, params) => {
  const { stockPrice, volatility, riskFreeRate, daysToVisualize = 0 } = params;

  // Generate price points (same as existing store)
  const priceRange = 0.05; // +/- 5% of current price
  const increment = stockPrice * 0.005; // 0.5% increments
  const points = [];

  for (let price = stockPrice * (1 - priceRange); price <= stockPrice * (1 + priceRange); price += increment) {
    points.push(Math.round(price * 100) / 100);
  }

  // Ensure target price is included
  if (!points.some(p => Math.abs(p - stockPrice) < increment / 2)) {
    points.push(stockPrice);
    points.sort((a, b) => a - b);
  }

  const results = [];

  points.forEach(price => {
    let totalPnL = 0;
    let totalMarketValue = 0;

    trades.forEach(trade => {
      // Calculate days to expiry for this trade (adjusted for daysToVisualize)
      let currentDaysToExpiry = trade.daysToExpiry;
      if (trade.ExpiryDate) {
        const parsedExpiryDate = parseExpiryDate(trade.ExpiryDate);
        currentDaysToExpiry = calculateDaysToExpiry(parsedExpiryDate);
      }

      // Adjust for daysToVisualize (target date offset)
      const adjustedDaysToExpiry = Math.max(0, currentDaysToExpiry - daysToVisualize);
      const tradeForCalc = { ...trade, daysToExpiry: adjustedDaysToExpiry };

      // Calculate premium (Black-Scholes) - same as existing store
      const premium = calculatePositionPremium(
        tradeForCalc,
        price,
        riskFreeRate,
        volatility
      );

      // Market value
      const marketValue = Math.round(trade.quantity * premium);
      // PnL = market value - debitCredit
      const pnl = marketValue - (trade.debitCredit || 0);

      totalMarketValue += marketValue;
      totalPnL += pnl;
    });

    results.push({
      stockPrice: price,
      totalPnL: Math.round(totalPnL * 100) / 100,
      totalMarketValue: Math.round(totalMarketValue * 100) / 100,
      breakeven: Math.abs(totalPnL) < 1
    });
  });

  return results;
};

/**
 * Calculate P&L over time (time decay analysis) - using same logic as usePnLAtVariousDateStore
 */
const calculateTimeDecayAnalysis = (trades, params) => {
  const { stockPrice, volatility, riskFreeRate } = params;

  // Find the maximum expiry date from all trades
  const maxExpiryDate = trades.reduce((max, trade) => {
    const parsedExpiryDate = parseExpiryDate(trade.ExpiryDate);
    const expiryDate = new Date(parsedExpiryDate);
    return expiryDate > max ? expiryDate : max;
  }, new Date());

  // Generate date points from today to max expiry
  const datePoints = [];
  const today = new Date();
  const daysDiff = Math.ceil((maxExpiryDate - today) / (1000 * 60 * 60 * 24));
  const steps = Math.min(15, daysDiff + 1);

  for (let i = 0; i < steps; i++) {
    const date = new Date(today.getTime() + (i * daysDiff / (steps - 1)) * 24 * 60 * 60 * 1000);
    datePoints.push(date);
  }

  const results = [];

  datePoints.forEach(date => {
    let totalPnL = 0;
    let totalMarketValue = 0;

    trades.forEach(trade => {
      // Calculate days to expiry using the proper utility function
      const daysToExpiry = calculateDaysToExpiry(trade.ExpiryDate, date);

      // Create a copy of the position with the adjusted days to expiry
      const positionWithDaysToExpiry = {
        ...trade,
        daysToExpiry: daysToExpiry
      };

      // Calculate premium using the correct calculatePositionPremium function
      const premium = calculatePositionPremium(
        positionWithDaysToExpiry,
        stockPrice,
        riskFreeRate,
        volatility
      );

      // Calculate market value
      const marketValue = Math.round(trade.quantity * premium);

      // Calculate PnL as market value minus debit/credit
      const pnl = marketValue - (trade.debitCredit || trade.premium || 0);

      totalMarketValue += marketValue;
      totalPnL += pnl;
    });

    results.push({
      daysToExpiry: Math.max(0, Math.ceil((maxExpiryDate - date) / (1000 * 60 * 60 * 24))),
      totalPnL: Math.round(totalPnL * 100) / 100,
      totalMarketValue: Math.round(totalMarketValue * 100) / 100,
      date: date.toISOString().split('T')[0]
    });
  });

  return results;
};

/**
 * Calculate P&L at various volatility levels
 */
const calculateVolatilityAnalysis = (trades, params) => {
  const { stockPrice, volatility, riskFreeRate, daysToVisualize = 0 } = params;
  const baseVol = volatility;
  const volRange = 0.5; // +/- 50% of current volatility
  const steps = 11; // Number of volatility points

  const minVol = Math.max(1, baseVol * (1 - volRange));
  const maxVol = baseVol * (1 + volRange);
  const volStep = (maxVol - minVol) / (steps - 1);

  const results = [];

  for (let i = 0; i < steps; i++) {
    const currentVol = minVol + (i * volStep);
    let totalPnL = 0;
    let totalMarketValue = 0;

    trades.forEach(trade => {
      // Calculate days to expiry for this trade (adjusted for daysToVisualize)
      let currentDaysToExpiry = trade.daysToExpiry;
      if (trade.ExpiryDate) {
        const parsedExpiryDate = parseExpiryDate(trade.ExpiryDate);
        currentDaysToExpiry = calculateDaysToExpiry(parsedExpiryDate);
      }

      // Adjust for daysToVisualize (target date offset)
      const adjustedDaysToExpiry = Math.max(0, currentDaysToExpiry - daysToVisualize);
      const tradeForCalc = { ...trade, daysToExpiry: adjustedDaysToExpiry };

      const premium = calculatePositionPremium(
        tradeForCalc,
        stockPrice,
        riskFreeRate,
        currentVol
      );

      const marketValue = Math.round(trade.quantity * premium);
      const pnl = marketValue - (trade.debitCredit || 0);

      totalMarketValue += marketValue;
      totalPnL += pnl;
    });

    results.push({
      volatility: Math.round(currentVol * 100) / 100,
      totalPnL: Math.round(totalPnL * 100) / 100,
      totalMarketValue: Math.round(totalMarketValue * 100) / 100
    });
  }

  return results;
};

export default useStrategyStore;
