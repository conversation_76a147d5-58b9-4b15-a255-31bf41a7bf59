/**
 * Test script to verify the timezone fix for holiday parsing
 */

function testTimezoneFix() {
  console.log('🧪 TESTING TIMEZONE FIX FOR HOLIDAY PARSING');
  console.log('=' .repeat(50));
  
  // Test the date parsing logic that was fixed
  console.log('📅 Testing date parsing with timezone fix:');
  
  const testDates = [
    '7/10/2025',  // The problematic date
    '1/1/2025',   // New Year
    '29/10/2025'  // Chung Yeung Festival
  ];
  
  testDates.forEach(dateStr => {
    console.log(`\n🔍 Parsing: ${dateStr}`);
    
    // Old method (problematic)
    try {
      const [day, month, year] = dateStr.split('/');
      const oldDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      console.log(`   Old method: ${oldDate.toISOString()} (UTC)`);
      console.log(`   Old local:  ${oldDate.getFullYear()}-${(oldDate.getMonth() + 1).toString().padStart(2, '0')}-${oldDate.getDate().toString().padStart(2, '0')}`);
    } catch (e) {
      console.log(`   Old method: ERROR - ${e.message}`);
    }
    
    // New method (fixed)
    try {
      const [day, month, year] = dateStr.split('/');
      const newDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      // Set time to noon to avoid timezone issues
      newDate.setHours(12, 0, 0, 0);
      console.log(`   New method: ${newDate.toISOString()} (UTC)`);
      console.log(`   New local:  ${newDate.getFullYear()}-${(newDate.getMonth() + 1).toString().padStart(2, '0')}-${newDate.getDate().toString().padStart(2, '0')}`);
    } catch (e) {
      console.log(`   New method: ERROR - ${e.message}`);
    }
  });
}

function testHolidayDetection() {
  console.log('\n🎯 TESTING HOLIDAY DETECTION WITH FIXES');
  console.log('=' .repeat(40));
  
  try {
    const positionUtils = require('../src/utils/position-utils.js');
    
    // Test the specific problematic date
    const testDate = new Date('2025-10-07');
    testDate.setHours(12, 0, 0, 0); // Noon to avoid timezone issues
    
    console.log(`📅 Testing holiday detection for: ${testDate.toISOString().split('T')[0]}`);
    console.log(`   Day of week: ${testDate.toLocaleDateString('en-US', { weekday: 'long' })}`);
    
    // Load holidays and check
    const holidays = positionUtils.loadExchangeHolidays();
    console.log(`📋 Loaded ${holidays.length} holidays total`);
    
    // Check if 2025-10-07 is detected as holiday
    const isHoliday = holidays.some(holiday => 
      holiday.getFullYear() === testDate.getFullYear() &&
      holiday.getMonth() === testDate.getMonth() &&
      holiday.getDate() === testDate.getDate()
    );
    
    console.log(`🎊 Is 2025-10-07 detected as holiday: ${isHoliday ? '✅ YES' : '❌ NO'}`);
    
    if (isHoliday) {
      console.log('   ✅ Holiday detection is working correctly!');
    } else {
      console.log('   ❌ Holiday detection still has issues');
      
      // Debug: show October 2025 holidays
      console.log('\n🔍 October 2025 holidays found:');
      const oct2025Holidays = holidays.filter(h => 
        h.getFullYear() === 2025 && h.getMonth() === 9 // October is month 9
      );
      
      oct2025Holidays.forEach(holiday => {
        console.log(`   - ${holiday.toISOString().split('T')[0]} (${holiday.toLocaleDateString('en-US', { weekday: 'long' })})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error testing holiday detection:', error.message);
  }
}

function testCalculateDaysToExpiry() {
  console.log('\n⏰ TESTING calculateDaysToExpiry WITH FIXES');
  console.log('=' .repeat(45));
  
  try {
    const positionUtils = require('../src/utils/position-utils.js');
    
    const expiryDate = '2025-10-17';
    const testDates = [
      '2025-10-06', // Monday (trading day)
      '2025-10-07', // Tuesday (HOLIDAY - should be same as previous)
      '2025-10-08'  // Wednesday (trading day)
    ];
    
    console.log(`🎯 Testing days to expiry calculation (expiry: ${expiryDate}):`);
    
    let previousDays = null;
    testDates.forEach(dateStr => {
      try {
        const days = positionUtils.calculateDaysToExpiry(expiryDate, dateStr);
        const difference = previousDays !== null ? (previousDays - days) : 'N/A';
        
        console.log(`   ${dateStr}: ${days} trading days (diff from prev: ${difference})`);
        
        if (dateStr === '2025-10-07') {
          if (difference === 0) {
            console.log('   ✅ 2025-10-07 correctly treated as holiday (same days as previous)');
          } else if (difference === 1) {
            console.log('   ❌ 2025-10-07 incorrectly treated as trading day (decremented days)');
          } else {
            console.log(`   ⚠️ Unexpected difference: ${difference}`);
          }
        }
        
        previousDays = days;
        
      } catch (error) {
        console.log(`   ${dateStr}: ERROR - ${error.message}`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error testing calculateDaysToExpiry:', error.message);
  }
}

function testBlackScholesImpact() {
  console.log('\n⚫ TESTING BLACK-SCHOLES IMPACT');
  console.log('=' .repeat(35));
  
  try {
    const blackScholes = require('../src/utils/black-scholes-robust.js');
    const positionUtils = require('../src/utils/position-utils.js');
    
    // Mock option parameters
    const stockPrice = 26000;
    const strikePrice = 26200;
    const riskFreeRate = 0.03;
    const volatility = 0.25;
    const isCall = true;
    const expiryDate = '2025-10-17';
    
    console.log('🧮 Testing Black-Scholes calculation for consecutive days:');
    console.log(`   Parameters: S=${stockPrice}, K=${strikePrice}, r=${riskFreeRate}, σ=${volatility}`);
    
    const testDates = ['2025-10-06', '2025-10-07', '2025-10-08'];
    let previousPrice = null;
    
    testDates.forEach(dateStr => {
      try {
        const daysToExpiry = positionUtils.calculateDaysToExpiry(expiryDate, dateStr);
        const timeToExpiry = daysToExpiry / 365;
        
        const optionPrice = blackScholes.calculateOptionPrice(
          stockPrice, strikePrice, timeToExpiry, riskFreeRate, volatility, isCall
        );
        
        const theta = previousPrice !== null ? (optionPrice - previousPrice) : 0;
        
        console.log(`\n   ${dateStr}:`);
        console.log(`     Days to expiry: ${daysToExpiry}`);
        console.log(`     Time to expiry: ${timeToExpiry.toFixed(4)}`);
        console.log(`     Option price: $${optionPrice.toFixed(2)}`);
        console.log(`     Theta (change): $${theta.toFixed(2)}`);
        
        if (dateStr === '2025-10-07') {
          if (Math.abs(theta) < 0.01) {
            console.log('     ✅ Minimal theta change (holiday correctly handled)');
          } else {
            console.log('     ❌ Significant theta change (holiday not handled)');
          }
        }
        
        previousPrice = optionPrice;
        
      } catch (error) {
        console.log(`   ${dateStr}: ERROR - ${error.message}`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error testing Black-Scholes:', error.message);
  }
}

// Run all tests
function runAllTests() {
  console.log('🚀 RUNNING COMPREHENSIVE TIMEZONE FIX TESTS');
  console.log('=' .repeat(60));
  
  testTimezoneFix();
  testHolidayDetection();
  testCalculateDaysToExpiry();
  testBlackScholesImpact();
  
  console.log('\n📋 TEST SUMMARY:');
  console.log('✅ If 2025-10-07 shows "holiday correctly handled", the fix worked');
  console.log('❌ If 2025-10-07 shows significant theta change, need more debugging');
  console.log('\n🎯 The goal is zero or minimal theta change on 2025-10-07');
}

// Export for testing
if (require.main === module) {
  runAllTests();
}

module.exports = { 
  testTimezoneFix, 
  testHolidayDetection, 
  testCalculateDaysToExpiry,
  testBlackScholesImpact 
};