import { useState, useEffect, useMemo } from 'react';
import useAnalysisStore from '../store/useAnalysisStore';

/**
 * Ticker Component
 *
 * This component displays information about the selected stock/index
 * and allows the user to select a different stock/index.
 *
 * It can work with either props (when used in the Options Analyzer page)
 * or context (when used in the Analysis page).
 */
const Ticker = (props) => {
  // Destructure props for better dependency tracking
  const { setStock } = props;

  // Zustand-based global state
  const storeSymbol = useAnalysisStore((s) => s.symbol);
  const storeSetSymbol = useAnalysisStore((s) => s.setSymbol);
  const storeStockInfo = useAnalysisStore((s) => s.stockInfo);
  const storeIsStockLoading = useAnalysisStore((s) => s.isStockLoading);
  const storeStockError = useAnalysisStore((s) => s.stockError);
  const fetchStockData = useAnalysisStore((s) => s.fetchStockData); // Get fetchStockData action

  // Define the mapping for shorthand symbols to Yahoo Finance tickers
  const symbolMap = useMemo(() => ({
    'HSI': '^HSI',
    'HHI': '^HSCE',
    'HTI': 'HSTECH.HK',
    'MHI': '^HSI' // As per user's explicit mapping
  }), []);

  const symbol = props.symbol !== undefined ? props.symbol : storeSymbol;
  const setSymbol = props.setSymbol !== undefined ? props.setSymbol : storeSetSymbol;
  const stockInfo = props.stockInfo !== undefined ? props.stockInfo : storeStockInfo;
  const isLoading = props.isLoading !== undefined ? props.isLoading : storeIsStockLoading;
  const error = props.error !== undefined ? props.error : storeStockError;

  // Predefined list of symbols
  const predefinedSymbols = ['HSI', 'HHI', 'HTI', 'MHI'];

  // State for custom input
  const [customInput, setCustomInput] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);

  // Effect to fetch stock data when symbol changes or component mounts
  useEffect(() => {
    if (symbol) {
      // Use the mapped symbol if it exists, otherwise use the symbol as is (for custom inputs)
      const yahooTicker = symbolMap[symbol] || symbol;
      fetchStockData(yahooTicker);
    }
  }, [symbol, fetchStockData, symbolMap]);

  // Effect to update parent's stock state when stockInfo from the store changes
  useEffect(() => {
    if (stockInfo && stockInfo.response && typeof stockInfo.response.regularMarketPrice === 'number') {
      const currentPrice = stockInfo.response.regularMarketPrice;
      if (setStock) {
        setStock(currentPrice);
      }
      // Note: setSelectedPrice is handled in the parent component if needed based on the updated stock price.
    }
  }, [stockInfo, setStock]);

  // Handle symbol selection from dropdown
  const handleSymbolChange = (e) => {
    const value = e.target.value;
    if (value === 'custom') {
      setShowCustomInput(true);
    } else {
      setSymbol(value);
      setShowCustomInput(false);
    }
  };

  // Handle custom input submission
  const handleCustomInputSubmit = () => {
    if (customInput.trim()) {
      // The symbol state will be set to customInput.trim().
      // The useEffect will then pick up this new symbol, and if it's a key in symbolMap,
      // it will be mapped; otherwise, it will be used directly.
      setSymbol(customInput.trim());
      setShowCustomInput(false);
    }
  };

  // Handle Enter key in custom input
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleCustomInputSubmit();
    }
  };

  return (
    <div className="mb-3">
      {/* <h2 className="text-lg font-bold mb-2 text-gray-800">Ticker</h2> */}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {/* Symbol Selection */}
        <div className="bg-white p-2 rounded-lg shadow">
          <div className="flex items-center">
            <label className="text-xs font-medium text-gray-700 mr-2 whitespace-nowrap">Symbol:</label>
            {!showCustomInput ? (
              <select
                className="w-full p-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                value={predefinedSymbols.includes(symbol) ? symbol : 'custom'}
                onChange={handleSymbolChange}
              >
                {predefinedSymbols.map(sym => (
                  <option key={sym} value={sym}>{sym}</option>
                ))}
                <option value="custom">Custom...</option>
              </select>
            ) : (
              <>
                <input
                  type="text"
                  className="w-full p-1 text-xs border border-gray-300 rounded-l focus:ring-blue-500 focus:border-blue-500"
                  value={customInput}
                  onChange={(e) => setCustomInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Enter symbol..."
                  autoFocus
                />
                <button
                  className="px-2 py-1 text-xs bg-blue-600 text-white hover:bg-blue-700 rounded-r"
                  onClick={handleCustomInputSubmit}
                >
                  Set
                </button>
              </>
            )}
          </div>

          {/* Additional information in smaller font - First row */}
          {stockInfo && (
            <div className="flex justify-between items-center mt-1 text-xs">
              <div className="flex items-center">
                <span className="text-gray-600 mr-1">IV:</span>
                <span className="font-medium">30%</span>
              </div>
              <div className="flex items-center">
                <span className="text-gray-600 mr-1">Exchange:</span>
                <span className="font-medium">{stockInfo.response?.exchange || 'N/A'}</span>
              </div>
              <div className="flex items-center">
                <span className="text-gray-600 mr-1">Currency:</span>
                <span className="font-medium">{stockInfo.currency || 'N/A'}</span>
              </div>
            </div>
          )}
        </div>

        {/* Stock Information */}
        <div className="bg-white p-2 rounded-lg shadow">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <p className="text-xs">Loading ticker information...</p>
            </div>
          ) : error ? (
            <div className="text-red-500 text-xs">
              <p>Error: {error}</p>
            </div>
          ) : stockInfo ? (
            <div>
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-semibold">{stockInfo.response?.shortName || symbol}</h3>
                <div className="flex items-center">
                  <span className="text-base font-bold mr-2">{stockInfo.response?.regularMarketPrice?.toFixed(0) || 'N/A'}</span>
                  <span className={`text-xs font-medium ${stockInfo.response?.regularMarketChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {stockInfo.response?.regularMarketChange?.toFixed(0) || 'N/A'}
                    ({stockInfo.response?.regularMarketChangePercent?.toFixed(2) || 'N/A'}%)
                  </span>
                </div>
              </div>

              {/* Additional information in smaller font - Second row */}
              <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
                <div>
                  <span className="text-gray-600 mr-1">Day Range:</span>
                  <span>{stockInfo.response?.regularMarketDayLow?.toFixed(0) || 'N/A'} - {stockInfo.response?.regularMarketDayHigh?.toFixed(0) || 'N/A'}</span>
                </div>
                <div>
                  <span className="text-gray-600 mr-1">Volume:</span>
                  <span>{stockInfo.response?.regularMarketVolume?.toLocaleString() || 'N/A'}</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex justify-center items-center h-full">
              <p className="text-xs">No ticker information available</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Ticker;
