const fs = require('fs');
const path = require('path');
const { parseStatement } = require('../src/server-utils/statement-parser');

async function testStatementParser() {
  try {
    console.log('Testing statement parser...');
    
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/84535253525450********.pdf');
    
    if (!fs.existsSync(pdfPath)) {
      console.error('PDF file not found:', pdfPath);
      return;
    }
    
    const pdfBuffer = fs.readFileSync(pdfPath);
    console.log('PDF file loaded, size:', pdfBuffer.length, 'bytes');
    
    // Try common passwords - often based on the date in filename
    const possiblePasswords = [
      '6369',      // Provided password
      '********',  // Date from filename
      '********',  // Date in different format
      '2025-08-26',
      'T545462',   // Account number
      ''           // No password
    ];
    
    let result = null;
    let successPassword = null;
    
    for (const password of possiblePasswords) {
      try {
        console.log(`Trying password: "${password}"`);
        result = await parseStatement(pdfBuffer, password);
        successPassword = password;
        console.log('✅ Password worked!');
        break;
      } catch (error) {
        console.log(`❌ Password "${password}" failed:`, error.message);
      }
    }
    
    if (result) {
      console.log('\n=== PARSED RESULT ===');
      console.log('Successful password:', successPassword);
      console.log('Header:', JSON.stringify(result.header, null, 2));
    } else {
      console.error('All passwords failed!');
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
    console.error(error.stack);
  }
}

testStatementParser();
