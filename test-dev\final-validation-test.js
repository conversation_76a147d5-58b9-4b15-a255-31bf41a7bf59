const statementParser = require('../src/server-utils/statement-parser');
const fs = require('fs');
const path = require('path');

async function finalValidationTest() {
  console.log('🎯 FINAL VALIDATION TEST - TRADE CONFIRMATION PARSER');
  console.log('====================================================');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    const password = '6369';
    
    console.log('📄 Parsing PDF statement...');
    const statement = await statementParser.parseStatement(pdfBuffer, password);
    
    // Validation 1: Correct number of trades
    console.log('\n✅ VALIDATION 1: TRADE COUNT');
    console.log(`Expected: 13 trades`);
    console.log(`Actual: ${statement.tradeConfirmation.length} trades`);
    console.log(`Status: ${statement.tradeConfirmation.length === 13 ? '✅ PASS' : '❌ FAIL'}`);
    
    // Validation 2: All trades match statement date
    console.log('\n✅ VALIDATION 2: DATE FILTERING');
    const statementDate = statement.header.statementDate;
    const wrongDateTrades = statement.tradeConfirmation.filter(trade => trade.date !== statementDate);
    console.log(`Statement Date: ${statementDate}`);
    console.log(`Trades with wrong date: ${wrongDateTrades.length}`);
    console.log(`Status: ${wrongDateTrades.length === 0 ? '✅ PASS' : '❌ FAIL'}`);
    
    // Validation 3: Required fields present
    console.log('\n✅ VALIDATION 3: REQUIRED FIELDS');
    const requiredFields = ['date', 'extendedHours', 'orderNo', 'market', 'description', 'buy', 'sell', 'strikePrice', 'optionType', 'premium', 'exchangeFee', 'commission'];
    let missingFields = 0;
    
    statement.tradeConfirmation.forEach(trade => {
      requiredFields.forEach(field => {
        if (trade[field] === undefined) {
          missingFields++;
        }
      });
    });
    
    console.log(`Missing fields: ${missingFields}`);
    console.log(`Status: ${missingFields === 0 ? '✅ PASS' : '❌ FAIL'}`);
    
    // Validation 4: Specific order numbers match expected
    console.log('\n✅ VALIDATION 4: ORDER NUMBER VERIFICATION');
    const expectedOrderNos = ['640201', '647740', '647978', '648123', '648124', '648148', '648361', '656490', '656550', '656682', '661409', '664050', '664135'];
    const actualOrderNos = statement.tradeConfirmation.map(trade => trade.orderNo).sort();
    const missingOrders = expectedOrderNos.filter(order => !actualOrderNos.includes(order));
    const extraOrders = actualOrderNos.filter(order => !expectedOrderNos.includes(order));
    
    console.log(`Expected orders: ${expectedOrderNos.length}`);
    console.log(`Actual orders: ${actualOrderNos.length}`);
    console.log(`Missing orders: ${missingOrders.length} ${missingOrders.length > 0 ? '(' + missingOrders.join(', ') + ')' : ''}`);
    console.log(`Extra orders: ${extraOrders.length} ${extraOrders.length > 0 ? '(' + extraOrders.join(', ') + ')' : ''}`);
    console.log(`Status: ${missingOrders.length === 0 && extraOrders.length === 0 ? '✅ PASS' : '❌ FAIL'}`);
    
    // Validation 5: Field format validation
    console.log('\n✅ VALIDATION 5: FIELD FORMAT VALIDATION');
    let formatErrors = 0;
    
    statement.tradeConfirmation.forEach((trade, idx) => {
      // Order number should be 6+ digits
      if (!/^\d{6,}$/.test(trade.orderNo)) {
        console.log(`Trade ${idx + 1}: Invalid order number format: ${trade.orderNo}`);
        formatErrors++;
      }
      
      // Market should be capital letters
      if (!/^[A-Z]{3,5}$/.test(trade.market)) {
        console.log(`Trade ${idx + 1}: Invalid market format: ${trade.market}`);
        formatErrors++;
      }
      
      // Option type should be CALL, PUT, or empty
      if (trade.optionType && !/^(CALL|PUT)$/.test(trade.optionType)) {
        console.log(`Trade ${idx + 1}: Invalid option type: ${trade.optionType}`);
        formatErrors++;
      }
      
      // Numeric fields should be numbers
      if (isNaN(trade.strikePrice) || isNaN(trade.premium) || isNaN(trade.exchangeFee) || isNaN(trade.commission)) {
        console.log(`Trade ${idx + 1}: Invalid numeric field values`);
        formatErrors++;
      }
    });
    
    console.log(`Format errors: ${formatErrors}`);
    console.log(`Status: ${formatErrors === 0 ? '✅ PASS' : '❌ FAIL'}`);
    
    // Validation 6: Sample trade data verification
    console.log('\n✅ VALIDATION 6: SAMPLE TRADE VERIFICATION');
    const trade1 = statement.tradeConfirmation.find(t => t.orderNo === '640201');
    const trade1Expected = {
      extendedHours: '#',
      market: 'HKFE',
      description: 'HH 05 SEP 25',
      buy: 2,
      strikePrice: 9300,
      optionType: 'CALL',
      premium: 100,
      exchangeFee: 8.08,
      commission: 40
    };
    
    let sampleErrors = 0;
    if (trade1) {
      Object.keys(trade1Expected).forEach(key => {
        if (trade1[key] !== trade1Expected[key]) {
          console.log(`Trade 640201 ${key}: Expected ${trade1Expected[key]}, got ${trade1[key]}`);
          sampleErrors++;
        }
      });
    } else {
      console.log('Trade 640201 not found');
      sampleErrors++;
    }
    
    console.log(`Sample verification errors: ${sampleErrors}`);
    console.log(`Status: ${sampleErrors === 0 ? '✅ PASS' : '❌ FAIL'}`);
    
    // Overall result
    console.log('\n🏆 OVERALL TEST RESULT');
    console.log('======================');
    const allTestsPassed = (
      statement.tradeConfirmation.length === 13 &&
      wrongDateTrades.length === 0 &&
      missingFields === 0 &&
      missingOrders.length === 0 && extraOrders.length === 0 &&
      formatErrors === 0 &&
      sampleErrors === 0
    );
    
    if (allTestsPassed) {
      console.log('🎉 ALL TESTS PASSED! ✅');
      console.log('✅ Problem 1 SOLVED: Correct number of trades (13)');
      console.log('✅ Problem 2 SOLVED: Fixed-width field parsing working');
      console.log('✅ Trade confirmation parser is production-ready!');
    } else {
      console.log('❌ SOME TESTS FAILED');
    }
    
    return allTestsPassed;
    
  } catch (error) {
    console.error('❌ Error during validation:', error.message);
    return false;
  }
}

// Run the test and exit with appropriate code
finalValidationTest().then(success => {
  process.exit(success ? 0 : 1);
});
