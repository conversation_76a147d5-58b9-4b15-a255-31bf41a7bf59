import React, { useState, useEffect } from 'react';
import { StrategyTypes } from '../../types/strategy';

/**
 * Strategy Edit Modal Component
 * 
 * Modal for editing existing strategy metadata with form validation.
 */
const StrategyEditModal = ({ isOpen, onClose, onUpdateStrategy, strategy }) => {
  const [formData, setFormData] = useState({
    name: '',
    stockSymbol: '',
    description: ''
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when strategy changes
  useEffect(() => {
    if (strategy) {
      setFormData({
        name: strategy.name || '',
        stockSymbol: strategy.stockSymbol || '',
        description: strategy.description || ''
      });
    }
  }, [strategy]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate form data
  const validateStrategy = (strategyData) => {
    const errors = {};
    
    if (!strategyData.name || strategyData.name.trim().length === 0) {
      errors.name = 'Strategy name is required';
    } else if (strategyData.name.length > StrategyTypes.VALIDATION_RULES.NAME_MAX_LENGTH) {
      errors.name = `Strategy name must be less than ${StrategyTypes.VALIDATION_RULES.NAME_MAX_LENGTH} characters`;
    }
    
    if (!strategyData.stockSymbol || strategyData.stockSymbol.trim().length === 0) {
      errors.stockSymbol = 'Stock symbol is required';
    } else if (strategyData.stockSymbol.length > StrategyTypes.VALIDATION_RULES.STOCK_SYMBOL_MAX_LENGTH) {
      errors.stockSymbol = `Stock symbol must be less than ${StrategyTypes.VALIDATION_RULES.STOCK_SYMBOL_MAX_LENGTH} characters`;
    }
    
    if (strategyData.description && strategyData.description.length > StrategyTypes.VALIDATION_RULES.DESCRIPTION_MAX_LENGTH) {
      errors.description = `Description must be less than ${StrategyTypes.VALIDATION_RULES.DESCRIPTION_MAX_LENGTH} characters`;
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Validate form data
      const validation = validateStrategy(formData);
      
      if (!validation.isValid) {
        setErrors(validation.errors);
        setIsSubmitting(false);
        return;
      }
      
      // Update strategy
      await onUpdateStrategy(strategy.id, formData);
      
      // Reset form and close modal
      setErrors({});
      onClose();
    } catch (error) {
      console.error('Error updating strategy:', error);
      setErrors({ submit: 'Failed to update strategy. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      setErrors({});
      onClose();
    }
  };

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isOpen || !strategy) return null;

  return (
    <div 
      className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      onClick={handleBackdropClick}
    >
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Modal Header */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Edit Strategy
            </h3>
            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Strategy Info */}
          <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
            <p className="text-sm text-gray-600">
              <strong>Strategy ID:</strong> {strategy.id}
            </p>
            <p className="text-sm text-gray-600">
              <strong>Created:</strong> {new Date(strategy.createdAt).toLocaleDateString()}
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Strategy Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Strategy Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                maxLength={StrategyTypes.VALIDATION_RULES.NAME_MAX_LENGTH}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter strategy name"
                disabled={isSubmitting}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            {/* Stock Symbol */}
            <div>
              <label htmlFor="stockSymbol" className="block text-sm font-medium text-gray-700 mb-1">
                Stock Symbol *
              </label>
              <input
                type="text"
                id="stockSymbol"
                name="stockSymbol"
                value={formData.stockSymbol}
                onChange={handleInputChange}
                maxLength={StrategyTypes.VALIDATION_RULES.STOCK_SYMBOL_MAX_LENGTH}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.stockSymbol ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="e.g., SPY, AAPL"
                style={{ textTransform: 'uppercase' }}
                disabled={isSubmitting}
              />
              {errors.stockSymbol && (
                <p className="mt-1 text-sm text-red-600">{errors.stockSymbol}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                maxLength={StrategyTypes.VALIDATION_RULES.DESCRIPTION_MAX_LENGTH}
                rows={3}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Optional description of the strategy"
                disabled={isSubmitting}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                {formData.description.length}/{StrategyTypes.VALIDATION_RULES.DESCRIPTION_MAX_LENGTH} characters
              </p>
            </div>

            {/* Submit Error */}
            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600">{errors.submit}</p>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !formData.name.trim() || !formData.stockSymbol.trim()}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </div>
                ) : (
                  'Update Strategy'
                )}
              </button>
            </div>
          </form>

          {/* Help Text */}
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-xs text-blue-800">
              <strong>Note:</strong> Changing the stock symbol will affect all trades in this strategy. 
              Make sure the new symbol matches your intended underlying asset.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategyEditModal;
