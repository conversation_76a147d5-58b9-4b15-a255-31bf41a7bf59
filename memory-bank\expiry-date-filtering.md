# Expiry Date Filtering Implementation

## Overview
This document describes the implementation of expiry date filtering in the Options Analyzer application, particularly focusing on how monthly expiry dates (YYYY-MM format) are handled.

## Monthly Expiry Date Handling

### Problem
When filtering positions with the "Less than or equal" option for expiry dates in YYYY-MM format, the application needs to correctly interpret this as the end of the month, not the beginning. Previously, the code was converting YYYY-MM to YYYY-MM-01 (first day of month), which was incorrect for filtering purposes.

### Solution
The solution implemented uses a JavaScript Date object technique to get the last day of a month:

```javascript
// To get the last day of the month, we set the date to the "0th" day of the next month
// Example: new Date(2025, 6, 0) returns June 30, 2025 (last day of June)
// Note: months are 0-indexed in JavaScript Date, but we're using 1-indexed months from the string
const [year, month] = selectedExpiryDate.split('-').map(num => parseInt(num));
filterDate = new Date(year, month, 0);
```

This works because in JavaScript, when you specify day 0 of a month, it gives you the last day of the previous month. So day 0 of July (month 7) is actually June 30th.

### Implementation Details

The filtering logic in the `filteredPositions` useMemo in positions-table.jsx was modified to:

1. For the 'lte' filter type, when the selected expiry date is in YYYY-MM format, use the last day of the month instead of the first day
2. Similarly, any position with a monthly expiry format (YYYY-MM) is also converted to the last day of that month
3. This ensures that when filtering for positions with expiry dates less than or equal to '2025-06', it correctly includes all positions that expire on or before June 30, 2025

## Date Handling in the Application

### Expiry Date Formats
The application supports two expiry date formats:
- YYYY-MM (monthly expiry, interpreted as the last day of the month)
- YYYY-MM-DD (specific day expiry)

### Date Comparison Logic
When comparing dates for filtering:
1. Convert both the filter date and position date to JavaScript Date objects
2. For YYYY-MM format, use the last day of the month
3. For YYYY-MM-DD format, use the specified date
4. Set hours to 0 to compare only dates
5. Use standard date comparison operators (<=, ===, etc.)

## Benefits of the Implementation

1. **Correctness**: Properly interprets monthly expiry dates as ending on the last day of the month
2. **Consistency**: Applies the same logic to both filter dates and position dates
3. **Clarity**: Includes detailed comments explaining the date handling logic for future maintainers
4. **Robustness**: Handles both YYYY-MM and YYYY-MM-DD formats correctly

## Related Components

This implementation affects:
- PositionsTable component's filtering logic
- Time Decay Chart date display
- Expiry date calculations throughout the application
