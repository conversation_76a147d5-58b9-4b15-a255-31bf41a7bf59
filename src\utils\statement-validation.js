/**
 * Statement Data Validation Utilities
 * 
 * This module provides validation functions for statement data
 * to ensure data integrity before saving to the database.
 */

/**
 * Validate statement data structure and required fields
 * @param {Object} statementData - The parsed statement data
 * @returns {Object} Validation result with isValid flag and errors array
 */
function validateStatementData(statementData) {
  const errors = [];
  
  if (!statementData) {
    errors.push('Statement data is required');
    return { isValid: false, errors };
  }

  // Validate header information
  const headerValidation = validateHeader(statementData.header);
  if (!headerValidation.isValid) {
    errors.push(...headerValidation.errors);
  }

  // Validate account movement data
  if (statementData.accountMovement) {
    const movementValidation = validateAccountMovement(statementData.accountMovement);
    if (!movementValidation.isValid) {
      errors.push(...movementValidation.errors);
    }
  }

  // Validate trade confirmation data
  if (statementData.tradeConfirmation) {
    const tradeValidation = validateTradeConfirmation(statementData.tradeConfirmation);
    if (!tradeValidation.isValid) {
      errors.push(...tradeValidation.errors);
    }
  }

  // Validate position data
  if (statementData.positionClosed) {
    const closedValidation = validatePositionClosed(statementData.positionClosed);
    if (!closedValidation.isValid) {
      errors.push(...closedValidation.errors);
    }
  }

  if (statementData.openPosition) {
    const openValidation = validateOpenPosition(statementData.openPosition);
    if (!openValidation.isValid) {
      errors.push(...openValidation.errors);
    }
  }

  // Validate financial summary
  if (statementData.financialSummary) {
    const financialValidation = validateFinancialSummary(statementData.financialSummary);
    if (!financialValidation.isValid) {
      errors.push(...financialValidation.errors);
    }
  }

  // Validate margin summary
  if (statementData.marginSummary) {
    const marginValidation = validateMarginSummary(statementData.marginSummary);
    if (!marginValidation.isValid) {
      errors.push(...marginValidation.errors);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate statement header
 */
function validateHeader(header) {
  const errors = [];

  if (!header) {
    errors.push('Statement header is required');
    return { isValid: false, errors };
  }

  if (!header.accountNumber || typeof header.accountNumber !== 'string') {
    errors.push('Account number is required and must be a string');
  }

  if (!header.statementDate || typeof header.statementDate !== 'string') {
    errors.push('Statement date is required and must be a string');
  } else if (!isValidDate(header.statementDate)) {
    errors.push('Statement date must be in YYYY-MM-DD format');
  }

  if (header.periodStart && !isValidDate(header.periodStart)) {
    errors.push('Period start date must be in YYYY-MM-DD format');
  }

  if (header.periodEnd && !isValidDate(header.periodEnd)) {
    errors.push('Period end date must be in YYYY-MM-DD format');
  }

  if (header.baseCurrency && (typeof header.baseCurrency !== 'string' || header.baseCurrency.length !== 3)) {
    errors.push('Base currency must be a 3-letter currency code');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate account movement data
 */
function validateAccountMovement(movements) {
  const errors = [];

  if (!Array.isArray(movements)) {
    errors.push('Account movement must be an array');
    return { isValid: false, errors };
  }

  movements.forEach((movement, index) => {
    if (!movement.date || !isValidDate(movement.date)) {
      errors.push(`Account movement ${index + 1}: Invalid date format`);
    }

    if (movement.debit !== undefined && (typeof movement.debit !== 'number' || isNaN(movement.debit))) {
      errors.push(`Account movement ${index + 1}: Debit must be a valid number`);
    }

    if (movement.credit !== undefined && (typeof movement.credit !== 'number' || isNaN(movement.credit))) {
      errors.push(`Account movement ${index + 1}: Credit must be a valid number`);
    }

    if (movement.balance !== undefined && (typeof movement.balance !== 'number' || isNaN(movement.balance))) {
      errors.push(`Account movement ${index + 1}: Balance must be a valid number`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate trade confirmation data
 */
function validateTradeConfirmation(trades) {
  const errors = [];

  if (!Array.isArray(trades)) {
    errors.push('Trade confirmation must be an array');
    return { isValid: false, errors };
  }

  trades.forEach((trade, index) => {
    if (!trade.date || !isValidDate(trade.date)) {
      errors.push(`Trade ${index + 1}: Invalid date format`);
    }

    if (!trade.instrument || typeof trade.instrument !== 'string') {
      errors.push(`Trade ${index + 1}: Instrument is required and must be a string`);
    }

    if (trade.qty !== undefined && (typeof trade.qty !== 'number' || isNaN(trade.qty))) {
      errors.push(`Trade ${index + 1}: Quantity must be a valid number`);
    }

    if (trade.strikePrice !== undefined && (typeof trade.strikePrice !== 'number' || isNaN(trade.strikePrice))) {
      errors.push(`Trade ${index + 1}: Strike price must be a valid number`);
    }

    if (trade.premium !== undefined && (typeof trade.premium !== 'number' || isNaN(trade.premium))) {
      errors.push(`Trade ${index + 1}: Premium must be a valid number`);
    }

    if (trade.commission !== undefined && (typeof trade.commission !== 'number' || isNaN(trade.commission))) {
      errors.push(`Trade ${index + 1}: Commission must be a valid number`);
    }

    if (trade.exchangeFee !== undefined && (typeof trade.exchangeFee !== 'number' || isNaN(trade.exchangeFee))) {
      errors.push(`Trade ${index + 1}: Exchange fee must be a valid number`);
    }

    if (trade.optionType && typeof trade.optionType === 'string') {
      const validTypes = ['CALL', 'PUT', 'Call', 'Put'];
      if (!validTypes.includes(trade.optionType)) {
        errors.push(`Trade ${index + 1}: Option type must be CALL or PUT`);
      }
    }

    if (trade.status && typeof trade.status === 'string') {
      const validStatuses = ['N', 'C', 'A', 'I']; // New, Close, etc.
      if (!validStatuses.includes(trade.status)) {
        errors.push(`Trade ${index + 1}: Invalid status value`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate position closed data
 */
function validatePositionClosed(positions) {
  const errors = [];

  if (!Array.isArray(positions)) {
    errors.push('Position closed must be an array');
    return { isValid: false, errors };
  }

  positions.forEach((position, index) => {
    if (!position.instrument || typeof position.instrument !== 'string') {
      errors.push(`Closed position ${index + 1}: Instrument is required and must be a string`);
    }

    if (position.openDate && !isValidDate(position.openDate)) {
      errors.push(`Closed position ${index + 1}: Invalid open date format`);
    }

    if (position.closeDate && !isValidDate(position.closeDate)) {
      errors.push(`Closed position ${index + 1}: Invalid close date format`);
    }

    if (position.quantity !== undefined && (typeof position.quantity !== 'number' || isNaN(position.quantity))) {
      errors.push(`Closed position ${index + 1}: Quantity must be a valid number`);
    }

    if (position.pnl !== undefined && (typeof position.pnl !== 'number' || isNaN(position.pnl))) {
      errors.push(`Closed position ${index + 1}: P&L must be a valid number`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate open position data
 */
function validateOpenPosition(positions) {
  const errors = [];

  if (!Array.isArray(positions)) {
    errors.push('Open position must be an array');
    return { isValid: false, errors };
  }

  positions.forEach((position, index) => {
    if (!position.instrument || typeof position.instrument !== 'string') {
      errors.push(`Open position ${index + 1}: Instrument is required and must be a string`);
    }

    if (position.quantity !== undefined && (typeof position.quantity !== 'number' || isNaN(position.quantity))) {
      errors.push(`Open position ${index + 1}: Quantity must be a valid number`);
    }

    if (position.marketValue !== undefined && (typeof position.marketValue !== 'number' || isNaN(position.marketValue))) {
      errors.push(`Open position ${index + 1}: Market value must be a valid number`);
    }

    if (position.unrealizedPnl !== undefined && (typeof position.unrealizedPnl !== 'number' || isNaN(position.unrealizedPnl))) {
      errors.push(`Open position ${index + 1}: Unrealized P&L must be a valid number`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate financial summary data
 */
function validateFinancialSummary(summary) {
  const errors = [];

  if (typeof summary !== 'object') {
    errors.push('Financial summary must be an object');
    return { isValid: false, errors };
  }

  const numericFields = ['openingBalance', 'closingBalance', 'netChange', 'totalDeposits', 'totalWithdrawals'];
  
  numericFields.forEach(field => {
    if (summary[field] !== undefined && (typeof summary[field] !== 'number' || isNaN(summary[field]))) {
      errors.push(`Financial summary: ${field} must be a valid number`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate margin summary data
 */
function validateMarginSummary(summary) {
  const errors = [];

  if (typeof summary !== 'object') {
    errors.push('Margin summary must be an object');
    return { isValid: false, errors };
  }

  const numericFields = ['initialMargin', 'maintenanceMargin', 'availableMargin'];
  
  numericFields.forEach(field => {
    if (summary[field] !== undefined && (typeof summary[field] !== 'number' || isNaN(summary[field]))) {
      errors.push(`Margin summary: ${field} must be a valid number`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Check if a date string is in valid YYYY-MM-DD format
 */
function isValidDate(dateString) {
  if (typeof dateString !== 'string') return false;
  
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateString)) return false;
  
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date) && date.toISOString().slice(0, 10) === dateString;
}

/**
 * Sanitize statement data by removing invalid entries and normalizing values
 */
function sanitizeStatementData(statementData) {
  if (!statementData) return null;

  const sanitized = { ...statementData };

  // Sanitize arrays by filtering out invalid entries
  if (sanitized.accountMovement) {
    sanitized.accountMovement = sanitized.accountMovement.filter(movement => 
      movement && (movement.date || movement.description)
    );
  }

  if (sanitized.tradeConfirmation) {
    sanitized.tradeConfirmation = sanitized.tradeConfirmation.filter(trade => 
      trade && trade.instrument
    );
  }

  if (sanitized.positionClosed) {
    sanitized.positionClosed = sanitized.positionClosed.filter(position => 
      position && position.instrument
    );
  }

  if (sanitized.openPosition) {
    sanitized.openPosition = sanitized.openPosition.filter(position => 
      position && position.instrument
    );
  }

  return sanitized;
}

module.exports = {
  validateStatementData,
  sanitizeStatementData,
  isValidDate
};
