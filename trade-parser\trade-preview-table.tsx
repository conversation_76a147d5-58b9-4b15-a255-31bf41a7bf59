"use client";

import type { ParsedTrade } from "@/types/trade";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableCaption,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { UploadCloud, Loader2, InfoIcon } from "lucide-react";

interface TradePreviewTableProps {
  trades: ParsedTrade[];
  onUpload: () => void;
  isUploading: boolean;
}

export function TradePreviewTable({ trades, onUpload, isUploading }: TradePreviewTableProps) {
  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="text-xl">Preview Parsed Trades</CardTitle>
        <CardDescription>
          Review the parsed trades below. If everything looks correct, you can upload them.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <ScrollArea className="h-[400px] w-full rounded-md border">
          <Table>
            {trades.length === 0 && (
              <TableCaption>
                <div className="flex flex-col items-center justify-center py-10 text-muted-foreground">
                  <InfoIcon className="h-10 w-10 mb-2" />
                  No trades parsed yet, or no data to display. Paste data and click "Parse Trades".
                </div>
              </TableCaption>
            )}
            <TableHeader>
              <TableRow>
                <TableHead>Ticker</TableHead>
                <TableHead>Expiry</TableHead>
                <TableHead>Strike</TableHead>
                <TableHead>Type</TableHead>
                <TableHead className="text-right">Quantity</TableHead>
                <TableHead className="text-right">Price</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {trades.map((trade) => (
                <TableRow key={trade.id}>
                  <TableCell className="font-medium">{trade.ticker}</TableCell>
                  <TableCell>{trade.expiry}</TableCell>
                  <TableCell>{trade.strike}</TableCell>
                  <TableCell>{trade.type}</TableCell>
                  <TableCell className={`text-right ${trade.quantity < 0 ? 'text-red-500' : 'text-green-600'}`}>
                    {trade.quantity}
                  </TableCell>
                  <TableCell className="text-right">{trade.price.toFixed(2)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
        {trades.length > 0 && (
           <Button onClick={onUpload} disabled={isUploading || trades.length === 0} className="w-full sm:w-auto">
            {isUploading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <UploadCloud className="mr-2 h-4 w-4" />
            )}
            Upload {trades.length} Trade{trades.length === 1 ? '' : 's'} to Firebase
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
