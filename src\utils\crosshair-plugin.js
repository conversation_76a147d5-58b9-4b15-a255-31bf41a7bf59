/**
 * Custom implementation of a crosshair plugin for Chart.js
 * This is a simplified version that adds a vertical line following the cursor
 */

// Define the plugin
export const Crosshair = {
  id: 'crosshair',
  
  // Default configuration
  defaults: {
    line: {
      color: 'rgba(100, 100, 100, 0.5)',
      width: 1,
      dashPattern: [5, 5]
    },
    sync: {
      enabled: true,
      group: 1,
      suppressTooltips: false
    },
    snap: {
      enabled: true
    }
  },
  
  // Plugin callbacks
  beforeInit: function(chart) {
    chart.crosshair = {
      x: 0,
      y: 0,
      active: false
    };
  },
  
  afterEvent: function(chart, args) {
    const { event, inChartArea } = args;
    
    // Only process mousemove events
    if (event.type !== 'mousemove') {
      return;
    }
    
    // Update crosshair position
    chart.crosshair.x = event.x;
    chart.crosshair.y = event.y;
    chart.crosshair.active = inChartArea;
    
    // Request a redraw
    chart.draw();
  },
  
  afterDraw: function(chart, args, options) {
    // Don't draw if not active
    if (!chart.crosshair.active) {
      return;
    }
    
    const ctx = chart.ctx;
    const x = chart.crosshair.x;
    const chartArea = chart.chartArea;
    
    // Save context state
    ctx.save();
    
    // Draw vertical line
    ctx.beginPath();
    ctx.lineWidth = options.line.width;
    ctx.strokeStyle = options.line.color;
    
    // Set dash pattern if specified
    if (options.line.dashPattern && options.line.dashPattern.length) {
      ctx.setLineDash(options.line.dashPattern);
    }
    
    // Draw the line from top to bottom of chart area
    ctx.moveTo(x, chartArea.top);
    ctx.lineTo(x, chartArea.bottom);
    ctx.stroke();
    
    // Restore context state
    ctx.restore();
  }
};
