import { useEffect, useState } from 'react';
import Ticker from '../components/ticker';
import AnalysisParameters from '../components/analysis-parameters';
import StoreDebugger from '../components/PnLStoreDebugger'; // Import the enhanced store debugger
import AllTrades from '../components/all-trades';
import FilteredTradesPnLChart from '../components/filtered-trades-pnl-chart'; // Import the filtered trades chart component
import TradesTimeDecayChart from '../components/trades-time-decay-chart'; // Import the time decay chart component
import useAllTradesStore from '../store/useAllTradesStore'; // Import useAllTradesStore

/**
 * Analysis Page
 *
 * This page provides tools for analyzing options strategies.
 * It includes the ticker component and analysis parameters component,
 * both now using Zustand for state management.
 */
const AnalysisPage = () => {
  // State to track filters from AllTrades component
  const [allTradesFilters, setAllTradesFilters] = useState({
    selectedStock: 'ALL',
    selectedExpiry: 'ALL',
    filteredTrades: []
  });



  // Fetch All trades into zustand useAllTradesStore()
  useEffect(() => {
    // Assuming useAllTradesStore has a function like fetchAllTradesData to populate the store
    // You might need to create this function in your useAllTradesStore if it doesn't exist
    // For example: useAllTradesStore.getState().fetchAllTradesData();
    // Or if the fetch logic is initiated by just calling a hook or a specific action:
    const { fetchAllTradesData } = useAllTradesStore.getState();
    if (fetchAllTradesData) {
      fetchAllTradesData();
    }
  }, []); // Empty dependency array ensures this runs once on mount

  return (
    <div>
      {/* <h1 className="text-2xl font-bold mb-4 text-gray-800">Analysis</h1> */}

      {/* Ticker Component */}
      <div className="mb-4">
        <Ticker />
      </div>

      {/* Analysis Parameters Component */}
      <div className="mb-4">
        <AnalysisParameters />
      </div>

      {/* Trades P&L Chart Component */}
      {/* All Trades Component (global state) */}
      <div className="mb-4">
        <AllTrades onFiltersChange={setAllTradesFilters} />
      </div>

      {/* Filtered Trades P&L Chart */}
      <div className="mb-4">
        <FilteredTradesPnLChart selectedExpiryDate={allTradesFilters.selectedExpiry} />
      </div>

      {/* Trades Time Decay Chart Component */}      
      <div className="mb-4">
        <TradesTimeDecayChart selectedExpiryDate={allTradesFilters.selectedExpiry} />
      </div>

      {/* Store Debugger Component - Only visible in debug mode */}
      <StoreDebugger />
    </div>
  );
};

export default AnalysisPage;
