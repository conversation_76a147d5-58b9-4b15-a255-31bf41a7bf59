<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Weekly Theta Engine: A Masterclass in Trading Time & Volatility</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #F2F5DE; 
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .flow-arrow::after {
            content: '▼';
            position: absolute;
            bottom: -28px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            color: #7A8B8B;
        }
    </style>
</head>
<body class="text-gray-800">

    <header class="bg-[#2A3D45] text-white p-8 text-center sticky top-0 z-50 shadow-2xl">
        <h1 class="text-3xl md:text-5xl font-extrabold mb-2">The Weekly Theta Engine</h1>
        <p class="text-lg md:text-xl text-[#D3BBAF]">A Masterclass in High-Frequency Time & Volatility Trading</p>
    </header>

    <main class="p-4 md:p-8">
        <div class="max-w-7xl mx-auto space-y-12">

            <section id="introduction" class="bg-white rounded-2xl shadow-lg p-6 md:p-8">
                <h2 class="text-3xl font-bold text-[#2A3D45] mb-4">Section 1: The Principle of Accelerated Time Decay</h2>
                <p class="text-lg mb-6">The "Weekly Theta Engine" is an aggressive strategy that aims to generate income by capitalizing on the accelerated decay of time value (Theta) in short-dated options. As an option nears expiration, its time value erodes at an exponential rate, a phenomenon most pronounced in the final 7-14 days. This non-linear decay is the primary fuel for the engine.</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                    <div class="flex flex-col justify-center">
                        <h3 class="text-xl font-semibold text-center mb-2">The Theta Decay Curve</h3>
                        <p class="text-center mb-4">This chart illustrates how an option's Theta (rate of daily decay) increases dramatically as it approaches expiration. The strategy focuses its activity in the steepest part of this curve, where time decay is most potent.</p>
                        <div class="chart-container h-64 md:h-80">
                            <canvas id="thetaDecayChart"></canvas>
                        </div>
                    </div>
                    <div class="text-center bg-[#D3BBAF] p-8 rounded-xl shadow-inner">
                        <p class="text-2xl font-semibold text-[#C17C74] mb-2">The Engine's Two opposing Forces</p>
                        <p class="text-lg mb-4">The strategy isn't monolithic; it's a choice between two opposing volatility bets:</p>
                        <div class="flex flex-col space-y-4">
                            <div class="bg-white p-4 rounded-lg shadow">
                                <p class="font-bold text-lg text-[#2A3D45]">Long Vega Plays (e.g., Long Calendars)</p>
                                <p>Profits when Implied Volatility (IV) <span class="text-green-600 font-bold">rises</span>. You are buying time and volatility.</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow">
                                <p class="font-bold text-lg text-[#2A3D45]">Short Vega Plays (e.g., Iron Condors)</p>
                                <p>Profits when Implied Volatility (IV) <span class="text-red-600 font-bold">falls</span>. You are selling time and volatility.</p>
                            </div>
                        </div>
                        <p class="mt-6 text-sm italic">Choosing the right structure depends entirely on your forecast for future volatility.</p>
                    </div>
                </div>
            </section>

            <section id="entry" class="bg-white rounded-2xl shadow-lg p-6 md:p-8">
                <h2 class="text-3xl font-bold text-[#2A3D45] mb-4">Section 2: The Pre-Trade Entry Checklist</h2>
                <p class="text-lg mb-6">Optimal entry requires a systematic evaluation of market conditions. A successful trade aligns quantitative volatility data with confirming technical signals, creating a statistical "edge."</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                    <div class="bg-gray-50 p-6 rounded-lg shadow-md">
                        <h3 class="font-semibold text-xl mb-4 text-[#2A3D45]">1. Volatility Environment</h3>
                        <p class="mb-4">Sell premium in high IV environments; buy it in low IV environments.</p>
                        <div class="w-full bg-gray-200 rounded-full h-6">
                            <div class="bg-[#C17C74] h-6 rounded-full text-white flex items-center justify-center font-bold" style="width: 75%">High IV Rank (75%)</div>
                        </div>
                        <p class="text-sm mt-2">Favorable for short premium strategies.</p>
                    </div>
                    <div class="bg-gray-50 p-6 rounded-lg shadow-md">
                        <h3 class="font-semibold text-xl mb-4 text-[#2A3D45]">2. Volatility Term Structure</h3>
                        <p class="mb-4">The shape of the volatility curve across expirations is critical for calendar spreads.</p>
                        <div class="chart-container h-48">
                            <canvas id="termStructureChart"></canvas>
                        </div>
                        <p class="text-sm mt-2">Backwardation is ideal for entering long calendars.</p>
                    </div>
                    <div class="bg-gray-50 p-6 rounded-lg shadow-md">
                        <h3 class="font-semibold text-xl mb-4 text-[#2A3D45]">3. Technical Confirmation</h3>
                        <p class="mb-4">Enter on clear signals of consolidation or mean reversion.</p>
                         <div class="font-mono text-3xl text-green-600 p-4 border-2 border-dashed border-gray-300 rounded-lg">
                            <span>📈...</span><span class="text-red-600">📉</span>
                        </div>
                        <p class="text-sm mt-2">Example: Sell calls after a spike above the upper Bollinger Band.</p>
                    </div>
                </div>
            </section>
            
            <section id="gamma" class="bg-white rounded-2xl shadow-lg p-6 md:p-8">
                <h2 class="text-3xl font-bold text-[#2A3D45] mb-4">Section 3: Taming the Gamma Serpent</h2>
                <p class="text-lg mb-6">The single greatest risk in any weekly options strategy is Gamma. As an option approaches expiration, its directional sensitivity (Delta) becomes unstable and can change with explosive speed. This "Gamma Explosion" can turn a winning trade into a catastrophic loss in minutes.</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                     <div class="flex flex-col justify-center">
                         <h3 class="text-xl font-semibold text-center mb-2">The Gamma Explosion Curve</h3>
                         <p class="text-center mb-4">This chart shows the exponential increase in Gamma risk during the final days to expiration (DTE). The risk/reward of holding a position in the final 1-2 days is exceptionally poor.</p>
                         <div class="chart-container h-64 md:h-80">
                            <canvas id="gammaRiskChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-[#C17C74] text-white p-8 rounded-xl shadow-inner">
                        <h3 class="text-2xl font-bold text-center mb-4">Non-Negotiable Risk Protocol</h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <span class="text-4xl mr-4">⏱️</span>
                                <div>
                                    <h4 class="font-bold">Time-Based Exits</h4>
                                    <p>Close ALL weekly positions before the final 1-2 days of expiration, regardless of P&L.</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <span class="text-4xl mr-4">📌</span>
                                <div>
                                    <h4 class="font-bold">Avoid Pin Risk</h4>
                                    <p>Never hold a short option into the close on expiration day. The risk of uncertain assignment is not worth the final cents of premium.</p>
                                </div>
                            </div>
                        </div>
                         <p class="mt-6 text-center text-sm italic">Discipline is the only defense against gamma.</p>
                    </div>
                </div>
            </section>
            
            <section id="management" class="bg-white rounded-2xl shadow-lg p-6 md:p-8">
                <h2 class="text-3xl font-bold text-[#2A3D45] mb-4">Section 4: The Rules-Based Management Playbook</h2>
                <p class="text-lg mb-6">Emotion is the enemy of consistency. A disciplined trading plan with predefined rules for every scenario is essential for removing emotion and managing trades systematically.</p>
                <div class="flex flex-col items-center space-y-6">
                    <div class="bg-[#2A3D45] text-white p-4 rounded-lg shadow-lg w-72 text-center relative flow-arrow">
                        <h3 class="font-bold">1. Entry</h3>
                        <p class="text-sm">Position opened based on checklist.</p>
                    </div>
                    <div class="bg-[#7A8B8B] text-white p-4 rounded-lg shadow-lg w-72 text-center relative flow-arrow">
                        <h3 class="font-bold">2. Set GTC Exit Order</h3>
                         <p class="text-sm">Immediately place orders to close at profit target AND stop loss.</p>
                    </div>
                    <div class="w-full max-w-2xl relative pt-8">
                         <div class="absolute w-full h-px bg-gray-300 top-1/2"></div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-3xl">
                        <div class="bg-green-100 border-l-4 border-green-500 p-6 rounded-r-lg shadow-md">
                            <h4 class="font-bold text-xl text-green-800">Winning Trade Protocol</h4>
                            <p class="mt-2"><strong class="text-green-600">Target:</strong> 25-50% of max profit.</p>
                            <p class="mt-1">If target is hit, especially quickly, close the trade immediately. Do not get greedy. Capture the edge and redeploy capital.</p>
                        </div>
                         <div class="bg-red-100 border-l-4 border-red-500 p-6 rounded-r-lg shadow-md">
                            <h4 class="font-bold text-xl text-red-800">Losing Trade Protocol</h4>
                             <p class="mt-2"><strong class="text-red-600">Stop:</strong> 10-20% of margin/debit.</p>
                             <p class="mt-1">If stop is hit, close the trade immediately. Do not average down or roll defensively. A small, planned loss is part of the business.</p>
                        </div>
                    </div>
                </div>
            </section>
            
            <section id="comparison" class="bg-white rounded-2xl shadow-lg p-6 md:p-8">
                <h2 class="text-3xl font-bold text-[#2A3D45] mb-4">Section 5: Strategy Selection Matrix</h2>
                <p class="text-lg mb-6">The "Weekly Theta Engine" is not one strategy, but a family of related structures. The most critical factor in choosing the right tool is your specific forecast for implied volatility.</p>
                 <div class="overflow-x-auto">
                    <table class="w-full min-w-max text-left border-collapse">
                        <thead>
                            <tr class="bg-[#2A3D45] text-white">
                                <th class="p-4">Strategy</th>
                                <th class="p-4">IV Bias (Vega)</th>
                                <th class="p-4">Ideal IV Forecast</th>
                                <th class="p-4">Capital Requirement</th>
                                <th class="p-4">Primary Use Case</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y">
                            <tr class="bg-blue-50">
                                <td class="p-4 font-semibold">Long Calendar Spread</td>
                                <td class="p-4 font-bold text-green-600">Positive (Long Vega)</td>
                                <td class="p-4">Low IV, expecting a rise</td>
                                <td class="p-4">Low (Net Debit)</td>
                                <td class="p-4">Betting on price stability and rising volatility.</td>
                            </tr>
                            <tr class="bg-red-50">
                                <td class="p-4 font-semibold">Iron Condor / Short Strangle</td>
                                <td class="p-4 font-bold text-red-600">Negative (Short Vega)</td>
                                <td class="p-4">High IV, expecting a fall (Vol Crush)</td>
                                <td class="p-4">Low (Margin on Spread)</td>
                                <td class="p-4">Selling overpriced premium in volatile markets.</td>
                            </tr>
                            <tr class="bg-blue-50">
                                <td class="p-4 font-semibold">Long Diagonal (PMCC)</td>
                                <td class="p-4 font-bold text-green-600">Positive (Long Vega)</td>
                                <td class="p-4">Low IV, expecting a rise</td>
                                <td class="p-4">Low (Net Debit)</td>
                                <td class="p-4">A capital-efficient, directional bet with a time decay benefit.</td>
                            </tr>
                              <tr class="bg-red-50">
                                <td class="p-4 font-semibold">Covered Call</td>
                                <td class="p-4 font-bold text-red-600">Negative (Short Vega)</td>
                                <td class="p-4">High IV, expecting a fall</td>
                                <td class="p-4">Very High (Cost of Stock)</td>
                                <td class="p-4">Generating yield on a long-term stock holding.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
    </main>
    
    <footer class="text-center p-8 mt-8 bg-[#2A3D45] text-[#D3BBAF]">
        <p>This infographic is for educational purposes only and not financial advice. All data synthesized from the "Weekly Theta Engine" research report.</p>
        <p class="text-sm mt-2">© 2025 Quantitative Strategy Visualizations</p>
    </footer>

    <script>
        const corporateModern = {
            darkSlate: '#2A3D45',
            mutedRed: '#C17C74',
            paleYellow: '#F2F5DE',
            greyGreen: '#7A8B8B',
            tan: '#D3BBAF'
        };

        const chartTooltipConfig = {
            plugins: {
                tooltip: {
                    callbacks: {
                        title: function(tooltipItems) {
                            const item = tooltipItems[0];
                            let label = item.chart.data.labels[item.dataIndex];
                            if (Array.isArray(label)) {
                              return label.join(' ');
                            } else {
                              return label;
                            }
                        }
                    }
                },
                legend: {
                    labels: {
                        color: corporateModern.darkSlate
                    }
                }
            },
            scales: {
                x: {
                   ticks: { color: corporateModern.darkSlate },
                   grid: { color: '#e5e7eb' },
                   title: { display: true, text: 'Days to Expiration (DTE)', color: corporateModern.darkSlate }
                },
                y: {
                   ticks: { color: corporateModern.darkSlate },
                   grid: { color: '#e5e7eb' }
                }
            },
            maintainAspectRatio: false,
            responsive: true
        };
        
        function wrapLabel(str, maxWidth) {
            if (str.length <= maxWidth) {
                return str;
            }
            const words = str.split(' ');
            let lines = [];
            let currentLine = words[0];
            for (let i = 1; i < words.length; i++) {
                if (currentLine.length + words[i].length + 1 < maxWidth) {
                    currentLine += ' ' + words[i];
                } else {
                    lines.push(currentLine);
                    currentLine = words[i];
                }
            }
            lines.push(currentLine);
            return lines;
        }


        new Chart(document.getElementById('thetaDecayChart'), {
            type: 'line',
            data: {
                labels: ['60', '50', '40', '30', '21', '14', '7', '3', '1'],
                datasets: [{
                    label: 'Theta Value (Absolute)',
                    data: [0.05, 0.06, 0.07, 0.09, 0.12, 0.18, 0.35, 0.5, 0.7],
                    borderColor: corporateModern.mutedRed,
                    backgroundColor: 'rgba(193, 124, 116, 0.2)',
                    fill: 'start',
                    tension: 0.4
                }]
            },
            options: { ...chartTooltipConfig, scales: { ...chartTooltipConfig.scales, y: { ...chartTooltipConfig.scales.y, title: { display: true, text: 'Daily Time Decay ($)', color: corporateModern.darkSlate }}}}
        });

        new Chart(document.getElementById('termStructureChart'), {
            type: 'line',
            data: {
                labels: ['7', '30', '60', '90'],
                datasets: [{
                    label: 'Backwardation (Ideal)',
                    data: [45, 38, 35, 33],
                    borderColor: '#4ade80',
                    fill: false,
                }, {
                    label: 'Contango (Typical)',
                    data: [20, 22, 24, 25],
                    borderColor: '#f87171',
                    fill: false,
                }]
            },
            options: { ...chartTooltipConfig, scales: { ...chartTooltipConfig.scales, x: { ...chartTooltipConfig.scales.x, title: { display: false } }, y: { ...chartTooltipConfig.scales.y, title: { display: true, text: 'IV %', color: corporateModern.darkSlate }}}}
        });

        new Chart(document.getElementById('gammaRiskChart'), {
            type: 'line',
            data: {
                labels: ['30', '21', '14', '10', '7', '5', '3', '1'],
                datasets: [{
                    label: 'Gamma Exposure',
                    data: [0.02, 0.04, 0.08, 0.15, 0.25, 0.4, 0.7, 1.2],
                    borderColor: corporateModern.mutedRed,
                    backgroundColor: 'rgba(193, 124, 116, 0.5)',
                    pointRadius: 4,
                    pointBackgroundColor: corporateModern.mutedRed,
                    fill: 'start',
                    tension: 0.3
                }]
            },
            options: { ...chartTooltipConfig, scales: { ...chartTooltipConfig.scales, y: { ...chartTooltipConfig.scales.y, title: { display: true, text: 'Gamma Value', color: corporateModern.darkSlate }}}}
        });

    </script>
</body>
</html>
