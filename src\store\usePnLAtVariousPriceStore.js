import { create } from 'zustand';
import useAllTradesStore from './useAllTradesStore';
import useAnalysisStore from './useAnalysisStore';
import { calculatePositionPremium, parseExpiryDate, calculateDaysToExpiry } from '../utils/position-utils';

/**
 * Zustand store for calculating and storing PnL and market value (Calc Prem)
 * for all trades at various price points within +/-5% of the target stock price.
 *
 * This store is reactive to changes in:
 * - Analysis parameters (from useAnalysisStore): targetStockPrice, volatility, riskFreeRate, daysToVisualize
 * - All trades data (from useAllTradesStore): allTrades
 *
 * This state is the single source of truth for all chart and table components.
 * It provides read-only access to calculated PnL data.
 */

const getPricePoints = (targetPrice) => {
  // Convert to number if it's a string
  const price = typeof targetPrice === 'string' ? parseFloat(targetPrice) : targetPrice;
  
  if (!price || price <= 0) {
    console.warn('Invalid target price:', targetPrice);
    return [price];
  }  // For index products (>$1000), use optimized 21-point approach
  if (price > 1000) {
    const range = price * 0.05; // 5% range
    const minPrice = price - range;
    const maxPrice = price + range;
    
    // Round target to nearest $50 for index products
    const targetRounded = Math.round(price / 50) * 50;
    
    // Calculate optimal step size to get close to 21 points
    const totalRange = maxPrice - minPrice;
    const baseStep = Math.max(25, Math.round(totalRange / 20 / 25) * 25); // Minimum $25 steps, rounded to $25
    
    const points = [];
    
    // Start from minimum and step up to maximum
    for (let currentPrice = minPrice; currentPrice <= maxPrice; currentPrice += baseStep) {
      const rounded = Math.round(currentPrice / 25) * 25; // Round to nearest $25
      if (rounded >= minPrice && rounded <= maxPrice && !points.includes(rounded)) {
        points.push(rounded);
      }
    }
    
    // Ensure target price is included
    if (!points.includes(targetRounded)) {
      points.push(targetRounded);
      points.sort((a, b) => a - b);
    }
    
    // Add more points if we have fewer than 15 (aim for 15-21 range)
    while (points.length < 15) {
      let largestGap = 0;
      let gapIndex = -1;
      
      for (let i = 0; i < points.length - 1; i++) {
        const gap = points[i + 1] - points[i];
        if (gap > largestGap && gap > 25) {
          largestGap = gap;
          gapIndex = i;
        }
      }
      
      if (gapIndex !== -1) {
        const midPoint = Math.round(((points[gapIndex] + points[gapIndex + 1]) / 2) / 25) * 25;
        if (!points.includes(midPoint)) {
          points.splice(gapIndex + 1, 0, midPoint);
        } else {
          break;
        }
      } else {
        break;
      }
    }
    
    // If still too few points, reduce step size and try again
    if (points.length < 15) {
      const finerPoints = [];
      const finerStep = 25; // Use $25 steps
      
      for (let currentPrice = minPrice; currentPrice <= maxPrice; currentPrice += finerStep) {
        const rounded = Math.round(currentPrice / 25) * 25;
        if (rounded >= minPrice && rounded <= maxPrice && !finerPoints.includes(rounded)) {
          finerPoints.push(rounded);
        }
      }
      
      // Ensure target is included
      if (!finerPoints.includes(targetRounded)) {
        finerPoints.push(targetRounded);
        finerPoints.sort((a, b) => a - b);
      }
      
      // If we have more than 21 points, remove the ones furthest from target
      while (finerPoints.length > 21) {
        const distances = finerPoints.map(p => Math.abs(p - targetRounded));
        const maxDistanceIndex = distances.indexOf(Math.max(...distances));
        finerPoints.splice(maxDistanceIndex, 1);
      }
      
      console.log(`Generated ${finerPoints.length} optimized price points for $${price} (target: $${targetRounded})`);
      console.log(`Range: $${finerPoints[0]} to $${finerPoints[finerPoints.length - 1]}`);
      return finerPoints;
    }
    
    console.log(`Generated ${points.length} optimized price points for $${price} (target: $${targetRounded})`);
    console.log(`Range: $${points[0]} to $${points[points.length - 1]}`);
    return points;
  }

  // For stocks under $1000, use the original enhanced approach
  const range = price * 0.05; // 5% range
  const minPrice = price - range;
  const maxPrice = price + range;
  
  // Determine increment based on price range
  let increment;
  if (price > 500) {
    increment = 2; // $2 increments for higher prices
  } else if (price > 100) {
    increment = 1; // $1 increments for mid-range prices
  } else if (price > 20) {
    increment = 0.5; // $0.50 increments for lower prices
  } else {
    increment = 0.25; // $0.25 increments for very low prices
  }
  
  const points = [];
  for (let currentPrice = minPrice; currentPrice <= maxPrice; currentPrice += increment) {
    points.push(Math.round(currentPrice * 100) / 100); // Round to 2 decimal places
  }
  
  // Ensure target price is included
  if (!points.some(p => Math.abs(p - price) < increment / 2)) {
    points.push(price);
    points.sort((a, b) => a - b);
  }
  
  console.log(`Generated ${points.length} price points for $${price}`);
  return points;
};


// Calculate PnL and market value for all trades at all price points, with daysToVisualize support
const calculatePnLAtVariousPrices = ({ trades, targetStockPrice, volatility, riskFreeRate, daysToVisualize }) => {
  if (!Array.isArray(trades) || !targetStockPrice) return { positionData: [], totals: {}, pricePoints: [] };
  const pricePoints = getPricePoints(targetStockPrice);
  const positionData = [];
  const totals = {};
  pricePoints.forEach(price => {
    totals[price] = { marketValue: 0, pnl: 0 };
  });

  trades.forEach(trade => {
    const positionValues = { id: trade.id, trade };
    pricePoints.forEach(price => {      // Calculate days to expiry for this trade (adjusted for daysToVisualize)
      let currentDaysToExpiry = trade.daysToExpiry;
      if (trade.ExpiryDate) {
        // Use utility functions for consistent date parsing
        const parsedExpiryDate = parseExpiryDate(trade.ExpiryDate);
        currentDaysToExpiry = calculateDaysToExpiry(parsedExpiryDate);
      }
      // Adjust for daysToVisualize (target date offset)
      const adjustedDaysToExpiry = Math.max(0, currentDaysToExpiry - (daysToVisualize || 0));
      const tradeForCalc = { ...trade, daysToExpiry: adjustedDaysToExpiry };
      // Calculate premium (Black-Scholes)
      const premium = calculatePositionPremium(
        tradeForCalc,
        price,
        riskFreeRate,
        volatility
      );
      // Market value
      const marketValue = Math.round(trade.quantity * premium);
      // PnL = market value - debitCredit
      const pnl = marketValue - (trade.debitCredit || 0);
      positionValues[`premium_${price}`] = premium;
      positionValues[`marketValue_${price}`] = marketValue;
      positionValues[`pnl_${price}`] = pnl;
      // Add to totals
      totals[price].marketValue += marketValue;
      totals[price].pnl += pnl;
    });
    positionData.push(positionValues);
  });
  return { positionData, totals, pricePoints };
};


const usePnLAtVariousPriceStore = create((set, get) => {
  // Store state
  const store = {
    // Main data
    pnlData: { positionData: [], totals: {}, pricePoints: [] },
    isCalculating: false,
    lastCalculationTime: null,

    // Internal tracking for change detection
    _lastTrades: null,
    _lastAnalysisParams: null,
    _subscriptions: [],

    // Get current analysis parameters from useAnalysisStore
    _getCurrentParams: () => {
      const analysisState = useAnalysisStore.getState();
      return {
        symbol: analysisState.symbol,
        targetStockPrice: analysisState.targetStockPrice,
        volatility: analysisState.volatility,
        riskFreeRate: analysisState.riskFreeRate,
        daysToVisualize: analysisState.daysToVisualize || 0
      };
    },

    // Check if parameters have changed
    _paramsChanged: (newParams) => {
      const current = get()._lastAnalysisParams;
      if (!current) return true;
      return (
        current.symbol !== newParams.symbol ||
        current.targetStockPrice !== newParams.targetStockPrice ||
        current.volatility !== newParams.volatility ||
        current.riskFreeRate !== newParams.riskFreeRate ||
        current.daysToVisualize !== newParams.daysToVisualize
      );
    },

    // Main recalculation method
    recalculate: () => {
      const currentParams = get()._getCurrentParams();
      const allTrades = useAllTradesStore.getState().allTrades;

      // Filter trades by the selected stock symbol
      const filteredTrades = allTrades.filter(trade => {
        if (!currentParams.symbol || !trade.stock) {
          return false; // Skip if no symbol selected or trade has no stock
        }
        // Case-insensitive exact match with the underlying stock symbol
        return trade.stock.toUpperCase() === currentParams.symbol.toUpperCase();
      });

      // Check if anything actually changed
      const paramsChanged = get()._paramsChanged(currentParams);
      const tradesChanged = allTrades !== get()._lastTrades;

      if (!paramsChanged && !tradesChanged) {
        return; // No changes, skip recalculation
      }

      set({ isCalculating: true });

      try {
        const pnlData = calculatePnLAtVariousPrices({
          trades: filteredTrades,
          ...currentParams
        });

        set({
          pnlData,
          _lastTrades: allTrades, // Store reference to all trades for change detection
          _lastAnalysisParams: currentParams,
          lastCalculationTime: new Date().toISOString(),
          isCalculating: false
        });

        console.log('PnL data recalculated:', {
          symbol: currentParams.symbol,
          totalTradesCount: allTrades?.length || 0,
          filteredTradesCount: filteredTrades?.length || 0,
          pricePointsCount: pnlData.pricePoints?.length || 0,
          params: currentParams
        });
      } catch (error) {
        console.error('Error calculating PnL data:', error);
        set({ isCalculating: false });
      }
    },

    // Initialize subscriptions to automatically recalculate when dependencies change
    _initializeSubscriptions: () => {
      const state = get();

      // Subscribe to changes in useAnalysisStore
      const analysisUnsubscribe = useAnalysisStore.subscribe(
        (analysisState) => {
          // Trigger recalculation when analysis parameters change
          get().recalculate();
        }
      );

      // Subscribe to changes in useAllTradesStore
      const tradesUnsubscribe = useAllTradesStore.subscribe(
        (tradesState) => {
          // Trigger recalculation when trades change
          get().recalculate();
        }
      );

      // Store unsubscribe functions
      state._subscriptions.push(analysisUnsubscribe, tradesUnsubscribe);
    },

    // Cleanup subscriptions
    _cleanup: () => {
      const state = get();
      state._subscriptions.forEach(unsubscribe => unsubscribe());
      set({ _subscriptions: [] });
    },

    // Selector for filtered/sorted data (by expiry, symbol, etc)
    getFilteredData: (filters = {}) => {
      const { positionData, totals, pricePoints } = get().pnlData;
      let filtered = positionData;

      if (filters.stockSymbol) {
        const symbol = filters.stockSymbol.toUpperCase();
        filtered = filtered.filter(d => {
          const ticker = (d.trade.ticker || '').toUpperCase();
          return ticker.startsWith(symbol);
        });
      }

      if (filters.expiry) {
        filtered = filtered.filter(d => d.trade.ExpiryDate === filters.expiry);
      }

      return { positionData: filtered, totals, pricePoints };
    },

    // Get current input parameters for debugging
    getCurrentInputs: () => {
      const params = get()._getCurrentParams();
      const allTrades = useAllTradesStore.getState().allTrades;

      // Filter trades by the selected stock symbol (same logic as recalculate)
      const filteredTrades = allTrades.filter(trade => {
        if (!params.symbol || !trade.stock) {
          return false;
        }
        return trade.stock.toUpperCase() === params.symbol.toUpperCase();
      });

      return {
        parameters: params,
        totalTradesCount: allTrades?.length || 0,
        filteredTradesCount: filteredTrades?.length || 0,
        lastCalculationTime: get().lastCalculationTime,
        isCalculating: get().isCalculating
      };
    },    // Calculate global P&L bounds for consistent y-axis scaling
    getGlobalPnLBounds: () => {
      const { positionData, totals, pricePoints } = get().pnlData;
      
      if (!positionData || positionData.length === 0 || !pricePoints || pricePoints.length === 0) {
        console.log('No data available for bounds calculation');
        return null; // Return null instead of {min: 0, max: 0}
      }

      let minPnL = Infinity;
      let maxPnL = -Infinity;

      // Check aggregated totals at each price point
      pricePoints.forEach(price => {
        const totalPnL = totals[price]?.pnl;
        if (typeof totalPnL === 'number') {
          minPnL = Math.min(minPnL, totalPnL);
          maxPnL = Math.max(maxPnL, totalPnL);
        }
      });

      // If no valid values found, return null
      if (minPnL === Infinity || maxPnL === -Infinity) {
        console.log('No valid P&L values found for bounds calculation');
        return null;
      }      // Add 20% padding to ensure data fits comfortably and round to major intervals
      const range = maxPnL - minPnL;
      const basePadding = Math.max(range * 0.2, 100); // At least $100 padding, now 20%
      
      // Calculate preliminary bounds
      const prelimMin = minPnL - basePadding;
      const prelimMax = maxPnL + basePadding;
      
      // Determine appropriate rounding interval based on range
      const totalRange = prelimMax - prelimMin;
      let roundingInterval;
      
      if (totalRange < 1000) {
        roundingInterval = 100; // Round to nearest $100 for small ranges
      } else if (totalRange < 5000) {
        roundingInterval = 500; // Round to nearest $500 for medium ranges
      } else if (totalRange < 20000) {
        roundingInterval = 1000; // Round to nearest $1000 for large ranges
      } else {
        roundingInterval = 2500; // Round to nearest $2500 for very large ranges
      }
      
      // Round bounds to major intervals
      let roundedMin = Math.floor(prelimMin / roundingInterval) * roundingInterval;
      let roundedMax = Math.ceil(prelimMax / roundingInterval) * roundingInterval;

      // DYNAMIC RESCALING: Check if any data points exceed the calculated bounds
      // This ensures all data is always visible even if calculations change
      let needsRescaling = false;
      let actualMin = minPnL;
      let actualMax = maxPnL;
      
      // Check all individual position data points as well
      positionData.forEach(position => {
        pricePoints.forEach(price => {
          const pnlValue = position[`pnl_${price}`];
          if (typeof pnlValue === 'number') {
            if (pnlValue < roundedMin || pnlValue > roundedMax) {
              needsRescaling = true;
              actualMin = Math.min(actualMin, pnlValue);
              actualMax = Math.max(actualMax, pnlValue);
            }
          }
        });
      });
      
      // If rescaling is needed, recalculate bounds with expanded range
      if (needsRescaling) {
        console.log('Data exceeds bounds, rescaling...', { 
          originalBounds: { min: roundedMin, max: roundedMax },
          actualRange: { min: actualMin, max: actualMax }
        });
        
        // Recalculate with expanded range and additional padding
        const expandedRange = actualMax - actualMin;
        const expandedPadding = Math.max(expandedRange * 0.25, 200); // 25% padding for expanded range
        
        const expandedMin = actualMin - expandedPadding;
        const expandedMax = actualMax + expandedPadding;
        
        // Determine new rounding interval for expanded range
        const expandedTotalRange = expandedMax - expandedMin;
        if (expandedTotalRange < 1000) {
          roundingInterval = 100;
        } else if (expandedTotalRange < 5000) {
          roundingInterval = 500;
        } else if (expandedTotalRange < 20000) {
          roundingInterval = 1000;
        } else {
          roundingInterval = 2500;
        }
        
        // Apply new rounded bounds
        roundedMin = Math.floor(expandedMin / roundingInterval) * roundingInterval;
        roundedMax = Math.ceil(expandedMax / roundingInterval) * roundingInterval;
      }

      const bounds = {
        min: roundedMin,
        max: roundedMax
      };

      console.log('Calculated P&L bounds:', bounds);
      return bounds;
    }
  };

  // Initialize subscriptions when store is created
  setTimeout(() => {
    store._initializeSubscriptions();
    store.recalculate(); // Initial calculation
  }, 0);

  return store;
});

export default usePnLAtVariousPriceStore;
