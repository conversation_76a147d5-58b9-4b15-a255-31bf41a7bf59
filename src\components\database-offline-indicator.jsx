import React from 'react';
import useDatabaseStore from '../store/useDatabaseStore';

/**
 * Database Offline Indicator Component
 * 
 * Displays an offline indicator when the database is not available
 * 
 * @returns {JSX.Element} The rendered component
 */
const DatabaseOfflineIndicator = () => {
  const { isOffline } = useDatabaseStore();

  if (!isOffline) {
    return null;
  }

  return (
    <div className="flex items-center text-gray-500">
      <span className="text-xs mr-1">Database</span>
      <svg className="h-4 w-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"></path>
      </svg>
    </div>
  );
};

export default DatabaseOfflineIndicator;
