import React, { useState, useEffect } from 'react';

/**
 * Error Notification Component
 *
 * Displays a nice-looking popup with error details
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Error title
 * @param {string} props.message - Error message
 * @param {Object} props.details - Additional error details (optional)
 * @param {string} props.type - Error type ('error', 'warning', 'info')
 * @param {boolean} props.show - Whether to show the notification
 * @param {function} props.onClose - Function to call when notification is closed
 * @param {number} props.autoCloseTime - Time in ms after which to auto-close (0 to disable)
 */
const ErrorNotification = ({
  title = 'Error',
  message = 'An error occurred',
  details = null,
  type = 'error',
  show = false,
  onClose = () => {},
  autoCloseTime = 0
}) => {
  const [isVisible, setIsVisible] = useState(show);
  const [showDetails, setShowDetails] = useState(false);

  // Set up auto-close timer if specified
  useEffect(() => {
    setIsVisible(show);

    if (show && autoCloseTime > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onClose();
      }, autoCloseTime);

      return () => clearTimeout(timer);
    }
  }, [show, autoCloseTime, onClose]);

  // Handle close button click
  const handleClose = () => {
    setIsVisible(false);
    setShowDetails(false); // Reset details view when closing
    onClose();
  };

  // Determine background color based on type
  const getBgColor = () => {
    switch (type) {
      case 'warning':
        return 'bg-yellow-50 border-yellow-400';
      case 'info':
        return 'bg-blue-50 border-blue-400';
      case 'success':
        return 'bg-green-50 border-green-400';
      case 'error':
      default:
        return 'bg-red-50 border-red-400';
    }
  };

  // Determine text color based on type
  const getTextColor = () => {
    switch (type) {
      case 'warning':
        return 'text-yellow-700';
      case 'info':
        return 'text-blue-700';
      case 'success':
        return 'text-green-700';
      case 'error':
      default:
        return 'text-red-700';
    }
  };

  // Determine icon based on type
  const getIcon = () => {
    switch (type) {
      case 'warning':
        return (
          <svg className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
      case 'info':
        return (
          <svg className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'success':
        return (
          <svg className="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'error':
      default:
        return (
          <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div className={`max-w-md w-full ${getBgColor()} border rounded-lg shadow-lg overflow-hidden`}>
        {/* Header */}
        <div className={`px-4 py-2 flex justify-between items-center border-b ${getTextColor()}`}>
          <div className="flex items-center">
            {getIcon()}
            <h3 className="ml-2 font-bold">{title}</h3>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700"
            aria-label="Close"
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Body */}
        <div className="px-4 py-3">
          <p className={`${getTextColor()} mb-2`}>{message}</p>

          {details && (
            <div className="mt-2">
              <button
                className={`text-sm ${getTextColor()} underline focus:outline-none`}
                onClick={() => setShowDetails(!showDetails)}
              >
                {showDetails ? 'Hide Details' : 'Show Details'}
              </button>

              {showDetails && (
                <div className="mt-2 p-2 bg-gray-100 rounded text-xs font-mono overflow-auto max-h-40">
                  {typeof details === 'object' ? (
                    <pre>{JSON.stringify(details, null, 2)}</pre>
                  ) : (
                    <pre>{details}</pre>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-4 py-2 bg-gray-50 border-t flex justify-end">
          <button
            onClick={handleClose}
            className={`px-3 py-1 text-xs font-medium rounded ${getTextColor()} bg-white border border-gray-300 hover:bg-gray-100`}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ErrorNotification;
