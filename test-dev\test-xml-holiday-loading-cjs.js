/**
 * CommonJS test script to verify XML holiday loading functionality
 * This tests the server-utils exchange-holidays module directly
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing XML Holiday Loading (CommonJS)...\n');

console.log('=== 1. Testing Server-Utils Exchange Holidays Module ===');

try {
  const exchangeHolidays = require('../src/server-utils/exchange-holidays.js');
  
  console.log('✅ Server-utils module loaded successfully');
  
  // Test the getExchangeHolidays function
  const holidays = exchangeHolidays.getExchangeHolidays();
  console.log(`📅 Loaded ${holidays.length} holidays using getExchangeHolidays()`);
  
  if (holidays.length > 0) {
    console.log('\n📋 First 5 holidays:');
    holidays.slice(0, 5).forEach((holiday, index) => {
      const dateStr = holiday.toISOString().split('T')[0];
      const dayName = holiday.toLocaleDateString('en-US', { weekday: 'long' });
      console.log(`  ${index + 1}. ${dateStr} (${dayName})`);
    });
    
    // Check for critical 2025-10-07 holiday
    const oct7_2025 = holidays.find(h => 
      h.getFullYear() === 2025 && h.getMonth() === 9 && h.getDate() === 7
    );
    
    if (oct7_2025) {
      console.log('\n✅ Critical holiday 2025-10-07 found in holiday list');
      console.log(`   Date: ${oct7_2025.toISOString().split('T')[0]}`);
      console.log(`   Day: ${oct7_2025.toLocaleDateString('en-US', { weekday: 'long' })}`);
    } else {
      console.log('\n❌ Critical holiday 2025-10-07 NOT found in holiday list');
    }
  }
  
} catch (error) {
  console.error('❌ Error testing server-utils module:', error.message);
}

console.log('\n=== 2. Testing Direct XML File Loading ===');

try {
  const exchangeHolidays = require('../src/server-utils/exchange-holidays.js');
  
  // Test loadExchangeHolidaysFromFile function directly
  const holidays = exchangeHolidays.loadExchangeHolidaysFromFile();
  console.log(`📅 Loaded ${holidays.length} holidays from XML file`);
  
  if (holidays.length > 0) {
    console.log('\n📋 First 5 holidays from XML:');
    holidays.slice(0, 5).forEach((holiday, index) => {
      const dateStr = holiday.toISOString().split('T')[0];
      const dayName = holiday.toLocaleDateString('en-US', { weekday: 'long' });
      console.log(`  ${index + 1}. ${dateStr} (${dayName})`);
    });
    
    // Check for critical 2025-10-07 holiday
    const oct7_2025 = holidays.find(h => 
      h.getFullYear() === 2025 && h.getMonth() === 9 && h.getDate() === 7
    );
    
    if (oct7_2025) {
      console.log('\n✅ Critical holiday 2025-10-07 found in XML data');
      console.log(`   Date: ${oct7_2025.toISOString().split('T')[0]}`);
      console.log(`   Day: ${oct7_2025.toLocaleDateString('en-US', { weekday: 'long' })}`);
    } else {
      console.log('\n❌ Critical holiday 2025-10-07 NOT found in XML data');
    }
    
    // Show last few holidays too
    console.log('\n📋 Last 5 holidays from XML:');
    holidays.slice(-5).forEach((holiday, index) => {
      const dateStr = holiday.toISOString().split('T')[0];
      const dayName = holiday.toLocaleDateString('en-US', { weekday: 'long' });
      console.log(`  ${index + 1}. ${dateStr} (${dayName})`);
    });
    
  } else {
    console.log('❌ No holidays loaded from XML file');
  }
  
} catch (error) {
  console.error('❌ Error testing direct XML loading:', error.message);
}

console.log('\n=== 3. Testing XML File Content Analysis ===');

try {
  const config = require('../src/utils/config.js');
  const xmlFilePath = config.getHolidayDataPath();
  
  console.log(`📁 XML file path: ${xmlFilePath}`);
  
  if (fs.existsSync(xmlFilePath)) {
    const xmlContent = fs.readFileSync(xmlFilePath, 'utf8');
    const stats = fs.statSync(xmlFilePath);
    
    console.log(`✅ XML file exists (${Math.round(stats.size / 1024)}KB)`);
    console.log(`📅 Last modified: ${stats.mtime.toISOString()}`);
    
    // Analyze XML structure
    const lines = xmlContent.split('\n');
    console.log(`📄 Total lines: ${lines.length}`);
    
    // Count table rows
    const rowCount = (xmlContent.match(/<tr>/g) || []).length;
    console.log(`📊 Table rows found: ${rowCount}`);
    
    // Extract all dates using the same pattern as the parser
    const datePattern = /(\d{1,2}\/\d{1,2}\/\d{4})/g;
    const dates = xmlContent.match(datePattern) || [];
    console.log(`🔍 Date patterns found: ${dates.length}`);
    
    if (dates.length > 0) {
      console.log('\n📋 All dates found in XML:');
      dates.forEach((date, index) => {
        // Parse the date to show it properly
        try {
          const [day, month, year] = date.split('/');
          const parsedDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
          const isoDate = parsedDate.toISOString().split('T')[0];
          const dayName = parsedDate.toLocaleDateString('en-US', { weekday: 'short' });
          console.log(`  ${String(index + 1).padStart(2, ' ')}. ${date} → ${isoDate} (${dayName})`);
        } catch (e) {
          console.log(`  ${String(index + 1).padStart(2, ' ')}. ${date} → [Parse Error]`);
        }
      });
      
      // Specifically check for 2025-10-07 pattern
      const oct7Pattern = dates.find(date => {
        const [day, month, year] = date.split('/');
        return parseInt(day) === 7 && parseInt(month) === 10 && parseInt(year) === 2025;
      });
      
      if (oct7Pattern) {
        console.log(`\n✅ Found 2025-10-07 pattern in XML: ${oct7Pattern}`);
      } else {
        console.log('\n❌ 2025-10-07 pattern NOT found in XML dates');
      }
    }
    
  } else {
    console.log('❌ XML file does not exist');
  }
  
} catch (error) {
  console.error('❌ Error analyzing XML file:', error.message);
}

console.log('\n=== 4. Testing Fallback Holidays ===');

try {
  const exchangeHolidays = require('../src/server-utils/exchange-holidays.js');
  
  const fallbackHolidays = exchangeHolidays.getFallbackHolidays();
  console.log(`📅 Fallback holidays: ${fallbackHolidays.length}`);
  
  console.log('\n📋 Fallback holidays list:');
  fallbackHolidays.forEach((holiday, index) => {
    const dateStr = holiday.toISOString().split('T')[0];
    const dayName = holiday.toLocaleDateString('en-US', { weekday: 'long' });
    console.log(`  ${String(index + 1).padStart(2, ' ')}. ${dateStr} (${dayName})`);
  });
  
} catch (error) {
  console.error('❌ Error testing fallback holidays:', error.message);
}

console.log('\n🎯 XML Holiday Loading Test Complete!');