const statementParser = require('../src/server-utils/statement-parser');
const fs = require('fs');
const path = require('path');

async function testFixedTradeExtraction() {
  console.log('🧪 TESTING FIXED TRADE CONFIRMATION EXTRACTION');
  console.log('==============================================');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    const password = '6369';
    
    console.log('📄 Parsing PDF...');
    const statement = await statementParser.parseStatement(pdfBuffer, password);
    
    console.log('\n📊 STATEMENT HEADER:');
    console.log('Account:', statement.header.accountHolder);
    console.log('Account Number:', statement.header.accountNumber);
    console.log('Statement Date:', statement.header.statementDate);
    
    console.log('\n🎯 TRADE CONFIRMATION RESULTS:');
    console.log('Total trades extracted:', statement.tradeConfirmation.length);
    console.log('Expected: 13 trades');
    
    if (statement.tradeConfirmation.length === 13) {
      console.log('✅ CORRECT NUMBER OF TRADES!');
    } else {
      console.log('❌ WRONG NUMBER OF TRADES!');
    }
    
    console.log('\n📋 DETAILED TRADE LIST:');
    console.log('=' .repeat(80));
    
    statement.tradeConfirmation.forEach((trade, idx) => {
      console.log(`\nTrade ${idx + 1}:`);
      console.log(`  📅 TRADE_DATE: ${trade.date}`);
      console.log(`  ⏰ Extended Hours (N): ${trade.extendedHours ? '#' : ' '}`);
      console.log(`  🔢 ORDER_NO: ${trade.orderNo}`);
      console.log(`  🏛️ MARKET: ${trade.market}`);
      console.log(`  📝 DESCRIPTION: ${trade.description}`);
      console.log(`  🔺 BUY: ${trade.buy || '-'}`);
      console.log(`  🔻 SELL: ${trade.sell || '-'}`);
      console.log(`  💰 STRIKE_PRICE: ${trade.strikePrice}`);
      console.log(`  📊 TYPE: ${trade.optionType || 'FUTURES'}`);
      console.log(`  💵 PREMIUM: ${trade.premium}`);
      console.log(`  📈 EXCHANGE_FEE: ${trade.exchangeFee}`);
      console.log(`  🏦 COMMISSION: ${trade.commission}`);
    });
    
    console.log('\n📊 SUMMARY STATISTICS:');
    console.log('=' .repeat(40));
    
    const totalBuy = statement.tradeConfirmation.reduce((sum, t) => sum + (t.buy || 0), 0);
    const totalSell = statement.tradeConfirmation.reduce((sum, t) => sum + (t.sell || 0), 0);
    const totalCommission = statement.tradeConfirmation.reduce((sum, t) => sum + Math.abs(t.commission || 0), 0);
    const totalFees = statement.tradeConfirmation.reduce((sum, t) => sum + Math.abs(t.exchangeFee || 0), 0);
    
    const instruments = [...new Set(statement.tradeConfirmation.map(t => t.description.split(' ')[0]))];
    const optionTypes = [...new Set(statement.tradeConfirmation.map(t => t.optionType).filter(t => t))];
    
    console.log(`Total Buy Quantity: ${totalBuy}`);
    console.log(`Total Sell Quantity: ${totalSell}`);
    console.log(`Total Commission: ${totalCommission.toFixed(2)}`);
    console.log(`Total Exchange Fees: ${totalFees.toFixed(2)}`);
    console.log(`Instruments: ${instruments.join(', ')}`);
    console.log(`Option Types: ${optionTypes.join(', ') || 'None (Futures only)'}`);
    
    console.log('\n🎉 TEST COMPLETED!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

testFixedTradeExtraction();
