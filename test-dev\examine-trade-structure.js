const PDFExtract = require('pdf.js-extract').PDFExtract;
const fs = require('fs');
const path = require('path');

async function examineTradeLineStructure() {
  console.log('🔍 EXAMINING TRADE LINE STRUCTURE IN DETAIL');
  console.log('===========================================');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    const password = '6369';
    
    const pdfExtract = new PDFExtract();
    const tempFilePath = path.join(__dirname, 'temp_statement.pdf');
    fs.writeFileSync(tempFilePath, pdfBuffer);
    
    const data = await new Promise((resolve, reject) => {
      pdfExtract.extract(tempFilePath, { password: password.trim() }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    
    // Convert to text
    const text = data.pages.map(page =>
      page.content.map(item => item.str).join(' ')
    ).join('\n');
    
    // Find the specific trade line
    const tradeConfStart = text.toUpperCase().indexOf('TRADE CONFIRMATION');
    const positionClosedStart = text.toUpperCase().indexOf('POSITION(S) CLOSED');
    const tradeSection = text.substring(tradeConfStart, positionClosedStart);
    
    // Find the line with the trades
    const lines = tradeSection.split('\n');
    const tradeLine = lines.find(line => line.includes('26/08/2025') && line.includes('640201'));
    
    console.log('🎯 FOUND TRADE LINE:');
    console.log('=' .repeat(100));
    console.log(tradeLine);
    console.log('=' .repeat(100));
    
    // Let's manually parse this according to the expected format
    console.log('\n📋 MANUAL PARSING ACCORDING TO USER FORMAT:');
    console.log('Expected format:');
    console.log('TRADE_DATE, N, ORDER_NO, MARKET, DESCRIPTION, BUY, SELL, STRIKE_PRICE, TYPE, PREMIUM, EXCHANGE_FEE, COMMISSION');
    
    // Based on the user's example, let's see what patterns we can identify
    const userExamples = [
      '26/08/2025 # 640201          HKFE     HH    05 SEP 25 N       2          HKD   9300.000000 CALL     100.000000    HKD      8.08- HKD     40.00- HKD',
      '26/08/2025   647740          HKFE     HH       AUG 25 C       4          HKD   9100.000000 PUT       20.000000    HKD     16.16- HKD     80.00- HKD',
      '26/08/2025   647978          HKFE     HS       AUG 25 N       1          HKD  25200.000000 PUT       20.000000    HKD     10.54- HKD     20.00- HKD'
    ];
    
    console.log('\n📝 USER PROVIDED EXAMPLES:');
    userExamples.forEach((example, idx) => {
      console.log(`${idx + 1}. ${example}`);
    });
    
    console.log('\n🎯 COMPARING WITH ACTUAL DATA:');
    
    // Try to split the actual trade line and identify patterns
    const actualTradeParts = tradeLine.split('26/08/2025').filter(part => part.trim().length > 5);
    console.log(`Found ${actualTradeParts.length} trade parts in the line`);
    
    actualTradeParts.forEach((part, idx) => {
      console.log(`\nTrade ${idx + 1} part:`);
      console.log(`"26/08/2025${part.substring(0, 200)}..."`);
      
      // Try to identify the patterns
      const tokens = part.trim().split(/\s+/);
      console.log(`Tokens: [${tokens.slice(0, 15).join(', ')}...]`);
    });
    
    // Clean up
    fs.unlinkSync(tempFilePath);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

examineTradeLineStructure();
