import React, { useState, useEffect } from 'react';
import useDatabaseStore from '../../store/useDatabaseStore';
import DatabaseOfflineIndicator from '../database-offline-indicator';

/**
 * Expected vs Actual Trades Component
 * 
 * This component displays the expected trades alongside what's actually coming from the API.
 */
const ExpectedVsActual = () => {
  // Access database status context
  const { isOffline } = useDatabaseStore();

  // State for API trades
  const [apiTrades, setApiTrades] = useState([]);
  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  // State for error message
  const [error, setError] = useState(null);
  // State for raw API response
  const [rawResponse, setRawResponse] = useState(null);

  // Expected trades
  const expectedTrades = [
    {
      id: 'expected-htim5',
      ticker: 'HTIM5',
      type: 'Future',
      ExpiryDate: '2025-06',
      strike: 'N/A',
      quantity: 2,
      premium: 5600.00,
      created_at: '2025-05-13T00:00:00.000Z'
    },
    {
      id: 'expected-hti6000f5',
      ticker: 'HTI6000F5',
      type: 'Call',
      ExpiryDate: '2025-06',
      strike: 6000,
      quantity: 2,
      premium: 360.00,
      created_at: '2025-05-13T00:00:00.000Z'
    }
  ];

  // Fetch HTI trades with expiry 2025-06
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setIsLoading(true);
        
        // Get trades for HTI with expiry 2025-06
        const response = await fetch('/api/firebase-trades-by-expiry?expiryDate=2025-06&stock=HTI');
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('API response for HTI 2025-06 trades:', data);
        
        setApiTrades(data.trades || []);
        setRawResponse(data);
        setIsLoading(false);
        
        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOnline();
      } catch (error) {
        console.error('Error fetching trades:', error);
        setError('Failed to fetch trades');
        setIsLoading(false);
        
        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOffline(error);
      }
    };

    fetchTrades();
  }, []);

  // Check if a trade exists in the API response
  const tradeExistsInApi = (expectedTrade) => {
    return apiTrades.some(apiTrade => 
      apiTrade.ticker === expectedTrade.ticker && 
      apiTrade.type === expectedTrade.type
    );
  };

  return (
    <div className="space-y-8">
      <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold text-gray-800">Expected vs Actual Trades for HTI 2025-06</h2>
          <DatabaseOfflineIndicator />
        </div>

        {isLoading ? (
          <div className="text-center py-4">
            <p className="text-gray-500">Loading trades...</p>
          </div>
        ) : error ? (
          <div className="text-center py-4">
            <p className="text-red-500">{error}</p>
          </div>
        ) : (
          <div>
            <div className="mb-4">
              <h3 className="text-md font-semibold mb-2">Expected Trades:</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ticker
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Expiry
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Strike
                      </th>
                      <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th scope="col" className="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {expectedTrades.map((trade) => (
                      <tr key={trade.id} className={
                        trade.type === 'Future' ? 'bg-yellow-50' :
                        trade.type === 'Call' ? 'bg-cyan-50' :
                        trade.type === 'Put' ? 'bg-red-50' :
                        'bg-gray-50'
                      }>
                        <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                          {trade.ticker}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.type}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.ExpiryDate}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.strike}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {trade.quantity}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {trade.premium.toFixed(2)}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-center">
                          {tradeExistsInApi(trade) ? (
                            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-200 text-green-800">
                              Found in API
                            </span>
                          ) : (
                            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-red-200 text-red-800">
                              Missing in API
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="mb-4">
              <h3 className="text-md font-semibold mb-2">Actual Trades from API:</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ticker
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Expiry
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Strike
                      </th>
                      <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {apiTrades.length === 0 ? (
                      <tr>
                        <td colSpan="6" className="px-3 py-4 text-center text-sm text-gray-500">
                          No trades found in API response.
                        </td>
                      </tr>
                    ) : (
                      apiTrades.map((trade) => (
                        <tr key={trade.id} className={
                          trade.type === 'Future' || trade.type === 'future' ? 'bg-yellow-50' :
                          trade.type === 'Call' || trade.type === 'call' ? 'bg-cyan-50' :
                          trade.type === 'Put' || trade.type === 'put' ? 'bg-red-50' :
                          'bg-gray-50'
                        }>
                          <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                            {trade.ticker}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                            {trade.type}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                            {trade.ExpiryDate || trade.expiry}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                            {trade.strike}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                            {trade.quantity}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                            {trade.premium != null ? trade.premium.toFixed(2) : 'N/A'}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h3 className="text-md font-semibold mb-2">Raw API Response:</h3>
              <div className="bg-gray-100 p-4 rounded mb-4 h-[200px] overflow-y-auto">
                <pre className="text-xs">{JSON.stringify(rawResponse, null, 2)}</pre>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExpectedVsActual;
