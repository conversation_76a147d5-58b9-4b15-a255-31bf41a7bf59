/**
 * Debug date parsing in holiday XML loading
 */

console.log('🔍 Debugging Date Parsing Issue...\n');

// Test the exact parsing logic used in server-utils
function testDateParsing(dateString) {
  console.log(`Testing date string: "${dateString}"`);
  
  const datePart = dateString.split(' ')[0];
  console.log(`  Date part extracted: "${datePart}"`);
  
  const [day, month, year] = datePart.split('/');
  console.log(`  Parsed components: day=${day}, month=${month}, year=${year}`);
  
  // Create date using the same logic as server-utils
  const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0, 0);
  console.log(`  Created Date object: ${holidayDate.toString()}`);
  console.log(`  ISO format: ${holidayDate.toISOString()}`);
  console.log(`  Local date string: ${holidayDate.toLocaleDateString()}`);
  console.log(`  getFullYear(): ${holidayDate.getFullYear()}`);
  console.log(`  getMonth(): ${holidayDate.getMonth()} (0-indexed)`);
  console.log(`  getDate(): ${holidayDate.getDate()}`);
  
  return holidayDate;
}

console.log('=== Testing Critical Date: 7/10/2025 ===');
const oct7Date = testDateParsing('7/10/2025');

console.log('\n=== Expected vs Actual ===');
console.log(`Expected: October 7, 2025`);
console.log(`Actual: ${oct7Date.toLocaleDateString('en-US', { 
  year: 'numeric', 
  month: 'long', 
  day: 'numeric' 
})}`);

// Check if the date is correct
const isCorrect = oct7Date.getFullYear() === 2025 && 
                  oct7Date.getMonth() === 9 && // October is month 9 (0-indexed)
                  oct7Date.getDate() === 7;

console.log(`\n✅ Date parsing correct: ${isCorrect}`);

if (!isCorrect) {
  console.log('❌ Date parsing is incorrect!');
  console.log(`Expected: Year=2025, Month=9, Date=7`);
  console.log(`Actual: Year=${oct7Date.getFullYear()}, Month=${oct7Date.getMonth()}, Date=${oct7Date.getDate()}`);
}

console.log('\n=== Testing Other Sample Dates ===');
const testDates = [
  '1/1/2025',
  '29/1/2025', 
  '25/12/2025'
];

testDates.forEach(dateStr => {
  console.log(`\n--- Testing: ${dateStr} ---`);
  const parsed = testDateParsing(dateStr);
});

console.log('\n=== Testing Current System Date ===');
const now = new Date();
console.log(`Current system time: ${now.toString()}`);
console.log(`Current timezone offset: ${now.getTimezoneOffset()} minutes`);
console.log(`Current locale: ${Intl.DateTimeFormat().resolvedOptions().locale}`);
console.log(`Current timezone: ${Intl.DateTimeFormat().resolvedOptions().timeZone}`);