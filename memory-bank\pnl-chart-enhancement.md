# P&L Chart Data Points Enhancement

## Overview
Modified the `usePnLAtVariousPriceStore.js` to provide significantly more data points for the P&L chart, resulting in smoother and more accurate visualization.

## Changes Made

### Before
- 9 data points at $100 intervals around the target stock price
- Example for $200 stock: $0, $100, $200, $300, $400, $500, $600 (7 points actually used)
- Very coarse resolution, leading to angular/choppy P&L curves

### After
- 20-50 data points within ±5% of the target stock price
- Adaptive increment based on stock price range:
  - **$0.25 increments** for price ranges ≤ $10
  - **$0.50 increments** for price ranges ≤ $50  
  - **$1.00 increments** for price ranges ≤ $100
  - **$2.00 increments** for price ranges > $100

## Examples

| Stock Price | Data Points | Range | Increment | Total Points |
|-------------|-------------|-------|-----------|--------------|
| $20 | $19.00 - $21.00 | 5% | $0.25 | 9 points |
| $50 | $47.50 - $52.50 | 5% | $0.25 | 21 points |
| $100 | $95.00 - $105.00 | 5% | $0.25 | 41 points |
| $200 | $190.00 - $210.00 | 5% | $0.50 | 41 points |

## Benefits

1. **Smoother P&L Curves**: Much more granular data provides smooth, accurate curves
2. **Better Accuracy**: More precise P&L calculations at various price points
3. **Focused Range**: ±5% range is more relevant for options analysis than wide $100 intervals
4. **Performance Optimized**: Adaptive increments prevent excessive data points while maintaining accuracy
5. **Floating Point Safe**: Proper rounding prevents floating point precision issues

## Technical Details

### Function: `getPricePoints(basePrice)`
- Calculates minPrice = basePrice × 0.95 (5% below)
- Calculates maxPrice = basePrice × 1.05 (5% above)  
- Determines optimal increment based on price range
- Generates points with proper rounding to 2 decimal places
- Ensures the exact base price is always included

### Impact on Components
- **TradesPnLChart**: Now displays much smoother P&L curves
- **PnL Table**: More granular data for analysis
- **Performance**: Optimized to balance accuracy with computational efficiency

## Files Modified
- `src/store/usePnLAtVariousPriceStore.js`: Updated `getPricePoints()` function
- Removed obsolete `roundToNearestHundred()` utility function

This enhancement significantly improves the accuracy and visual quality of P&L analysis while maintaining optimal performance.
