import admin from 'firebase-admin';

// Initialize Firebase Admin if not already done
if (!admin.apps.length) {
  const serviceAccount = require('../../firebase.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

const db = admin.firestore();

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: true, message: 'Method Not Allowed' });
  }

  try {
    const { header, tradeConfirmations } = req.body;

    if (!header || !header.accountNumber || !header.statementDate) {
      return res.status(400).json({
        error: true,
        message: 'Account number and statement date are required'
      });
    }

    if (!tradeConfirmations || !Array.isArray(tradeConfirmations) || tradeConfirmations.length === 0) {
      return res.status(400).json({
        error: true,
        message: 'Trade confirmation records are required'
      });
    }

    const { accountNumber, statementDate } = header;

    // Check for duplicate trade confirmations
    const existingConfirmations = await db.collection('trade-confirmations')
      .where('accountNumber', '==', accountNumber)
      .where('statementDate', '==', statementDate)
      .get();

    if (!existingConfirmations.empty) {
      return res.status(409).json({
        error: true,
        message: `Trade confirmations for account ${accountNumber} on ${statementDate} already exist.`
      });
    }

    // Create batch to save all trade confirmations
    const batch = db.batch();
    const savedRecords = [];

    tradeConfirmations.forEach((trade, index) => {
      const docRef = db.collection('trade-confirmations').doc();
      const tradeData = {
        ...trade,
        accountNumber,
        statementDate,
        recordIndex: index,
        importedAt: admin.firestore.FieldValue.serverTimestamp()
      };
      
      batch.set(docRef, tradeData);
      savedRecords.push({ id: docRef.id, ...tradeData });
    });

    // Commit the batch
    await batch.commit();

    console.log(`Saved ${tradeConfirmations.length} trade confirmation records for account ${accountNumber}`);

    res.status(201).json({
      success: true,
      message: `Successfully saved ${tradeConfirmations.length} trade confirmation records`,
      count: tradeConfirmations.length,
      accountNumber,
      statementDate,
      records: savedRecords
    });

  } catch (error) {
    console.error('Error saving trade confirmations:', error);
    res.status(500).json({
      error: true,
      message: 'Error saving trade confirmations to database',
      details: error.message
    });
  }
}