const [isSaving, setIsSaving] = useState(false);
const [saveSuccess, setSaveSuccess] = useState(false);
const [saveError, setSaveError] = useState(null);

const handleSaveToDatabase = async () => {
  if (!parsedData || !parsedData.tradeConfirmation || parsedData.tradeConfirmation.length === 0) {
    setSaveError('No trade confirmation data to save');
    return;
  }

  setIsSaving(true);
  setSaveError(null);
  setSaveSuccess(false);

  try {
    const response = await fetch('/api/save-trade-confirmation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        header: parsedData.header,
        tradeConfirmations: parsedData.tradeConfirmation
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || 'Failed to save trade confirmations');
    }

    setSaveSuccess(true);
    console.log('Trade confirmations saved successfully:', result);
    
    // Optional: Clear the data after successful save
    // setParsedData(null);
    
  } catch (error) {
    console.error('Error saving trade confirmations:', error);
    setSaveError(error.message || 'Failed to save trade confirmations to database');
  } finally {
    setIsSaving(false);
  }
};

// Add this to your JSX where you want the save button
<div className="mt-4">
  <button
    onClick={handleSaveToDatabase}
    disabled={isSaving || !parsedData?.tradeConfirmation?.length}
    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
  >
    {isSaving ? 'Saving...' : 'Save to Database'}
  </button>
  
  {saveSuccess && (
    <div className="mt-2 p-2 bg-green-100 text-green-800 rounded">
      Successfully saved {parsedData.tradeConfirmation.length} trade confirmation records to database
    </div>
  )}
  
  {saveError && (
    <div className="mt-2 p-2 bg-red-100 text-red-800 rounded">
      Error: {saveError}
    </div>
  )}
</div>