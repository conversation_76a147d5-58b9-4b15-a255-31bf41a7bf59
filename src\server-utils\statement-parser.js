const pdf = require('pdf-parse');
const PDFExtract = require('pdf.js-extract').PDFExtract;

/**
 * Statement Parser Utility
 * 
 * This module provides functionality to parse PDF trading statements
 * and extract structured data from various sections.
 */

/**
 * Parse a PDF statement buffer and extract trading data
 * @param {Buffer} pdfBuffer - The PDF file buffer
 * @param {string} password - Optional password for encrypted PDFs
 * @returns {Object} Parsed statement data
 */
async function parseStatement(pdfBuffer, password = null) {
  try {
    let text = '';
    let leftBlockLines = null; // Store positioned left block lines

    if (password && password.trim() !== '') {
      // Use pdf.js-extract for password-protected PDFs
      console.log('Parsing password-protected PDF with pdf.js-extract');
      const pdfExtract = new PDFExtract();

      // Write buffer to temporary file for pdf.js-extract
      const fs = require('fs');
      const path = require('path');
      const tempFilePath = path.join(__dirname, '../../temp_statement.pdf');

      fs.writeFileSync(tempFilePath, pdfBuffer);

      try {
        const extractOptions = {
          password: password.trim()
        };

        const data = await new Promise((resolve, reject) => {
          pdfExtract.extract(tempFilePath, extractOptions, (err, data) => {
            if (err) reject(err);
            else resolve(data);
          });
        });

        // Convert extracted data to text using positional information
        const contentItems = data.pages[0].content; // Use first page for header
        
        // Group items by y-coordinate (line) and sort by x-coordinate (column)
        const lineGroups = {};
        for (const item of contentItems) {
          const y = Math.round(item.y); // Round to group items on same line
          if (!lineGroups[y]) {
            lineGroups[y] = [];
          }
          lineGroups[y].push(item);
        }
        
        // Sort lines by y-coordinate and items within lines by x-coordinate
        const sortedLines = Object.keys(lineGroups)
          .map(y => parseInt(y))
          .sort((a, b) => a - b)
          .slice(0, 10); // Take first 10 lines for header
          
        // Extract left block content (x < 250) from the first few lines
        leftBlockLines = []; // Use the outer variable, not declare a new one
        for (const y of sortedLines) {
          const lineItems = lineGroups[y].sort((a, b) => a.x - b.x);
          const leftItems = lineItems.filter(item => item.x < 250); // Items in left column
          if (leftItems.length > 0) {
            const lineText = leftItems.map(item => item.str).join('').trim();
            if (lineText.length > 0) {
              leftBlockLines.push(lineText);
            }
          }
        }
        
        console.log('Extracted left block lines using position:', leftBlockLines);
        
        // Convert to single text string for other parsing logic  
        text = data.pages.map(page =>
          page.content.map(item => item.str).join(' ')
        ).join('\n');

        // Clean up temp file
        fs.unlinkSync(tempFilePath);

      } catch (extractError) {
        // Clean up temp file on error
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
        throw extractError;
      }
    } else {
      // Use pdf-parse for non-password-protected PDFs
      console.log('Parsing PDF with pdf-parse');
      const data = await pdf(pdfBuffer);
      text = data.text;
    }

    console.log('PDF parsed successfully, extracting sections...');
    console.log('Extracted text preview (first 500 chars):', text.substring(0, 500));

    // Extract different sections from the statement
    const parsedData = {
      header: extractStatementHeader(text, leftBlockLines),
      accountMovement: extractAccountMovement(text),
      tradeConfirmation: extractTradeConfirmation(text),
      positionClosed: extractPositionClosed(text),
      openPosition: extractOpenPosition(text),
      financialSummary: extractFinancialSummary(text),
      marginSummary: extractMarginSummary(text)
    };

    console.log('Statement parsing completed');
    return parsedData;

  } catch (error) {
    console.error('Error parsing PDF statement:', error);
    throw new Error(`Failed to parse PDF: ${error.message}`);
  }
}

/**
 * Extract statement header information (account number, date, etc.)
 */
function extractStatementHeader(text, leftBlockLines = null) {
  const header = {};
  console.log('Extracting header from text...');

  // Use positioned left block lines if available, otherwise fall back to text parsing
  let extractedLeftLines = leftBlockLines;
  
  if (!extractedLeftLines) {
    // Fallback: Extract left block from text (original logic)
    const headerLines = text.split(/\r?\n/).slice(0, 10); // Take first 10 lines for header processing
    extractedLeftLines = [];
    
    for (const line of headerLines) {
      // Split each line by the right block field patterns and take the left part
      const rightBlockPattern = /(Date\s+日期|A\/C\s+No\.|Branch\s+Code|Page\s+頁數)/;
      const match = line.search(rightBlockPattern);
      
      if (match !== -1) {
        // Extract the left part before the right block field
        const leftPart = line.substring(0, match).trim();
        if (leftPart.length > 0) {
          extractedLeftLines.push(leftPart);
        }
      } else {
        // No right block field found, take the whole line if it's not empty
        const trimmedLine = line.trim();
        if (trimmedLine.length > 0) {
          extractedLeftLines.push(trimmedLine);
        }
      }
    }
  }
  
  console.log('Left block lines:', extractedLeftLines.length, 'lines found');
  
  let nameIdx = -1;
  for (let i = 0; i < extractedLeftLines.length; i++) {
    if (/^[A-Z ]+[\u4e00-\u9fa5]+/.test(extractedLeftLines[i])) {
      nameIdx = i;
      break;
    }
  }
  
  console.log('Name index found:', nameIdx);
  
  if (nameIdx !== -1) {
    header.accountHolder = extractedLeftLines[nameIdx];
    // Take the next 3 lines after the name, with no regex or format assumptions
    const addressLines = extractedLeftLines.slice(nameIdx + 1, nameIdx + 4).filter(line => line.length > 0);
    header.address = addressLines;
    
    console.log('Account holder:', header.accountHolder);
    console.log('Address lines:', addressLines.length, 'lines');
  }

  // 2. Extract right block fields
  // R1. Date   日期   : 26 AUG 2025
  const dateRegex = /Date\s+日期\s*:\s*([\d]{1,2}\s+[A-Z]{3}\s+[\d]{4})/i;
  const dateMatch = text.match(dateRegex);
  if (dateMatch) {
    header.statementDate = normalizeDate(dateMatch[1]);
  }

  // R2.  A/C No.   帳戶號碼   : T545462
  const acNoRegex = /A\/C\s+No\.\s+帳戶號碼\s*:\s*([A-Z0-9]+)/i;
  const acNoMatch = text.match(acNoRegex);
  if (acNoMatch) {
    header.accountNumber = acNoMatch[1].trim();
  }

  // R3. Branch Code   分行 編號   : JB9
  const branchRegex = /Branch\s+Code\s+分行\s*編號\s*:\s*([A-Z0-9]+)/i;
  const branchMatch = text.match(branchRegex);
  if (branchMatch) {
    header.branchCode = branchMatch[1].trim();
  }

  // R4. Page   頁數   :   1
  const pageRegex = /Page\s+頁數\s*:\s*(\d+)/i;
  const pageMatch = text.match(pageRegex);
  if (pageMatch) {
    header.pageNumber = parseInt(pageMatch[1]);
  }

  return header;
}

/**
 * Extract account movement data
 */
function extractAccountMovement(text) {
  const movements = [];
  
  // Look for account movement section
  const movementSection = extractSection(text, 'Account Movement', 'Trade Confirmation');
  if (!movementSection) return movements;

  // Parse movement lines - typical format:
  // Date | Description | Reference | Debit | Credit | Balance | Currency
  const lines = movementSection.split('\n');
  
  for (const line of lines) {
    const movement = parseMovementLine(line.trim());
    if (movement) {
      movements.push(movement);
    }
  }

  return movements;
}

/**
 * Extract trade confirmation data
 */
function extractTradeConfirmation(text) {
  const trades = [];
  
  // Extract TRADE CONFIRMATION section between headers
  const tradeSection = extractSectionCaseInsensitive(text, 'TRADE CONFIRMATION', 'POSITION(S) CLOSED');
  if (!tradeSection) return trades;

  // Extract statement date from the header for filtering
  const statementDate = extractStatementDate(text);
  if (!statementDate) return trades;
  
  // Convert statement date to DD/MM/YYYY format for matching
  const datePattern = formatDateForMatching(statementDate);
  
  console.log(`Looking for trades with date pattern: ${datePattern}`);

  // Find lines that contain the statement date
  const lines = tradeSection.split('\n');
  const tradeDateLines = lines.filter(line => line.includes(datePattern));
  
  console.log(`Found ${tradeDateLines.length} lines with statement date`);
  
  // Parse each line containing trades
  for (const line of tradeDateLines) {
    const lineTradeList = parseTradeConfirmationLine(line, datePattern);
    trades.push(...lineTradeList);
  }

  console.log(`Extracted ${trades.length} trades from TRADE CONFIRMATION section`);
  return trades;
}

/**
 * Extract statement date from header
 */
function extractStatementDate(text) {
  const dateRegex = /Date\s+日期\s*:\s*([\d]{1,2}\s+[A-Z]{3}\s+[\d]{4})/i;
  const dateMatch = text.match(dateRegex);
  if (dateMatch) {
    return normalizeDate(dateMatch[1]);
  }
  return null;
}

/**
 * Format date for matching in trade lines (YYYY-MM-DD to DD/MM/YYYY)
 */
function formatDateForMatching(dateStr) {
  if (!dateStr) return null;
  
  // Convert from YYYY-MM-DD to DD/MM/YYYY
  const parts = dateStr.split('-');
  if (parts.length === 3) {
    return `${parts[2]}/${parts[1]}/${parts[0]}`;
  }
  return dateStr;
}

/**
 * Parse a trade confirmation line using fixed-width fields
 */
function parseTradeConfirmationLine(line, targetDate) {
  const trades = [];
  
  // Split the line into individual trade entries based on date pattern
  const tradeParts = line.split(targetDate).filter(part => part.trim().length > 0);
  
  console.log(`Found ${tradeParts.length} parts when splitting by date`);
  
  for (let i = 0; i < tradeParts.length; i++) {
    // Reconstruct each trade line with the date
    let tradeLine;
    if (i === 0) {
      // First part - check if it contains trade data, skip if it's just headers
      if (!tradeParts[i].match(/\d{6,}/)) {
        continue; // Skip header part
      }
      tradeLine = tradeParts[i] + targetDate;
    } else {
      // Subsequent parts - date is at the beginning
      tradeLine = targetDate + tradeParts[i];
    }
    
    // Clean the trade line - remove summary content after the trade data
    const summaryMarkers = ['-----', '=====', 'N - Open', 'C - Close'];
    for (const marker of summaryMarkers) {
      const markerIndex = tradeLine.indexOf(marker);
      if (markerIndex !== -1) {
        tradeLine = tradeLine.substring(0, markerIndex).trim();
        break;
      }
    }
    
    const trade = parseFixedWidthTradeLine(tradeLine, targetDate);
    if (trade) {
      trades.push(trade);
    }
  }
  
  return trades;
}

/**
 * Parse a single trade line using fixed-width field positions
 */
function parseFixedWidthTradeLine(line, targetDate) {
  try {
    // Clean the trade line - remove summary content after the trade data
    const summaryMarkers = ['-----', '=====', 'N - Open', 'C - Close'];
    let cleanLine = line;
    for (const marker of summaryMarkers) {
      const markerIndex = cleanLine.indexOf(marker);
      if (markerIndex !== -1) {
        cleanLine = cleanLine.substring(0, markerIndex).trim();
        break;
      }
    }
    
    // Skip if this is not a trade line or contains header/summary info
    if (cleanLine.includes('TRADE CONFIRMATION') || cleanLine.includes('ORDER NO') || cleanLine.length < 50) {
      return null;
    }
    
    // Find the start of the actual trade data (after the date)
    const dateIndex = cleanLine.indexOf(targetDate);
    if (dateIndex === -1) return null;
    
    // Extract the trade portion starting from the date
    const tradePortion = cleanLine.substring(dateIndex);
    
    // Define fixed-width field positions based on analysis
    // Format: 26/08/2025 # 640201   HKFE   HH   05 SEP 25 N   2   HKD   9300.000000 CALL   100.000000   HKD   8.08- HKD   40.00- HKD
    const trade = {
      date: normalizeDate(targetDate),
      extendedHours: '',
      orderNo: '',
      instrument: '',
      expiry: '',
      status: '',
      qty: 0,
      strikePrice: 0,
      optionType: '',
      premium: 0,
      exchangeFee: 0,
      commission: 0
    };
    
    // Parse using fixed positions relative to start of trade data
    let pos = 10; // Start after "26/08/2025"
    
    // Extended Hours indicator (position 11-12)
    if (pos < tradePortion.length) {
      const extendedHoursChar = tradePortion.substring(pos, pos + 2).trim();
      if (extendedHoursChar === '#') {
        trade.extendedHours = '#';
        pos += 2;
      } else {
        pos += 1; // Skip space
      }
    }
    
    // Skip spaces and find ORDER NO (around position 13-20)
    while (pos < tradePortion.length && tradePortion[pos] === ' ') pos++;
    
    // ORDER NO (6+ digits)
    const orderNoMatch = tradePortion.substring(pos).match(/^(\d{6,})/);
    if (orderNoMatch) {
      trade.orderNo = orderNoMatch[1];
      pos += orderNoMatch[1].length;
    }
    
    // Skip spaces and parse DESCRIPTION (instrument + expiry + status)
    while (pos < tradePortion.length && tradePortion[pos] === ' ') pos++;
    
    // Skip over MARKET field (HKFE, etc.) without storing it
    const marketMatch = tradePortion.substring(pos).match(/^([A-Z]{3,5})/);
    if (marketMatch) {
      pos += marketMatch[1].length;
    }
    
    // Skip spaces and continue with DESCRIPTION parsing
    while (pos < tradePortion.length && tradePortion[pos] === ' ') pos++;
    
    // Extract description part until we hit quantity or HKD
    const descriptionMatch = tradePortion.substring(pos).match(/^([A-Z]{1,3}\s+(?:\d{1,2}\s+)?[A-Z]{3}\s+\d{2}\s+[NCAI])/);
    if (descriptionMatch) {
      const descParts = descriptionMatch[1].trim().split(/\s+/);
      
      // Parse instrument (first 1-3 characters)
      if (descParts[0]) {
        trade.instrument = descParts[0];
      }
      
      // Parse expiry and status
      if (descParts.length >= 4) {
        // Weekly format: HH 05 SEP 25 N
        if (/^\d{1,2}$/.test(descParts[1])) {
          trade.expiry = `${descParts[1]} ${descParts[2]} ${descParts[3]}`;
          trade.status = descParts[4];
        } 
        // Monthly format: HH AUG 25 C
        else if (/^[A-Z]{3}$/.test(descParts[1])) {
          trade.expiry = `${descParts[1]} ${descParts[2]}`;
          trade.status = descParts[3];
        }
      }
      
      pos += descriptionMatch[1].length;
    }
    
    // Skip spaces and parse BUY/SELL quantities
    while (pos < tradePortion.length && tradePortion[pos] === ' ') pos++;
    
    // Look for quantity patterns
    const quantityMatch = tradePortion.substring(pos).match(/^(\d+)(?:\s+(\d+))?\s+HKD/);
    if (quantityMatch) {
      if (quantityMatch[2]) {
        // Two quantities: first is buy (positive), second is sell (negative)
        trade.qty = parseInt(quantityMatch[1]); // Buy is positive
        // For now, we'll handle single quantity case - if we have two quantities,
        // we need to determine which is buy/sell based on context
      } else {
        // Single quantity - determine buy/sell based on status
        const qty = parseInt(quantityMatch[1]);
        if (trade.status === 'C') {
          trade.qty = -qty; // Sell is negative
        } else {
          trade.qty = qty; // Buy is positive
        }
      }
      
      // Move past quantities and HKD
      pos += quantityMatch[0].length;
    }
    
    // Skip spaces and parse STRIKE PRICE
    while (pos < tradePortion.length && tradePortion[pos] === ' ') pos++;
    
    const strikePriceMatch = tradePortion.substring(pos).match(/^([\d,.]+)/);
    if (strikePriceMatch) {
      trade.strikePrice = parseFloat(strikePriceMatch[1].replace(/,/g, ''));
      pos += strikePriceMatch[1].length;
    }
    
    // Skip spaces and parse OPTION TYPE
    while (pos < tradePortion.length && tradePortion[pos] === ' ') pos++;
    
    const optionTypeMatch = tradePortion.substring(pos).match(/^(CALL|PUT)/);
    if (optionTypeMatch) {
      trade.optionType = optionTypeMatch[1];
      pos += optionTypeMatch[1].length;
    }
    
    // Skip spaces and parse PREMIUM
    while (pos < tradePortion.length && tradePortion[pos] === ' ') pos++;
    
    const premiumMatch = tradePortion.substring(pos).match(/^([\d,.]+)/);
    if (premiumMatch) {
      trade.premium = parseFloat(premiumMatch[1].replace(/,/g, ''));
      pos += premiumMatch[1].length;
    }
    
    // Skip to HKD and parse EXCHANGE FEE
    const hkdIndex = tradePortion.indexOf('HKD', pos);
    if (hkdIndex !== -1) {
      pos = hkdIndex + 3;
      
      while (pos < tradePortion.length && tradePortion[pos] === ' ') pos++;
      
      const exchangeFeeMatch = tradePortion.substring(pos).match(/^([\d,.-]+)/);
      if (exchangeFeeMatch) {
        trade.exchangeFee = parseAmount(exchangeFeeMatch[1]);
        pos += exchangeFeeMatch[1].length;
      }
    }
    
    // Skip to next HKD and parse COMMISSION
    const nextHkdIndex = tradePortion.indexOf('HKD', pos);
    if (nextHkdIndex !== -1) {
      pos = nextHkdIndex + 3;
      
      while (pos < tradePortion.length && tradePortion[pos] === ' ') pos++;
      
      const commissionMatch = tradePortion.substring(pos).match(/^([\d,.-]+)/);
      if (commissionMatch) {
        trade.commission = parseAmount(commissionMatch[1]);
      }
    }
    
    // Only return if we have minimum required fields
    if (trade.orderNo && trade.instrument) {
      return trade;
    }
    
  } catch (error) {
    console.log('Error parsing fixed-width trade line:', error.message);
  }
  
  return null;
}

/**
 * Extract all trades from a compressed line containing multiple trades
 */
function extractAllTradesFromLine(line) {
  const trades = [];
  const parts = line.split(/\s+/).filter(part => part.trim());
  
  // Find all date positions
  const dateIndices = [];
  parts.forEach((part, index) => {
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(part)) {
      dateIndices.push(index);
    }
  });
  
  // Extract each trade based on date positions
  for (let i = 0; i < dateIndices.length; i++) {
    const startIdx = dateIndices[i];
    const endIdx = i + 1 < dateIndices.length ? dateIndices[i + 1] : parts.length;
    const tradeParts = parts.slice(startIdx, endIdx);
    
    const trade = parseSingleTrade(tradeParts);
    if (trade) {
      trades.push(trade);
    }
  }
  
  return trades;
}

/**
 * Extract position closed data
 */
function extractPositionClosed(text) {
  const positions = [];
  
  // Look for position closed section
  const positionSection = extractSection(text, 'Position Closed', 'Open Position');
  if (!positionSection) return positions;

  // Parse position lines
  const lines = positionSection.split('\n');
  
  for (const line of lines) {
    const position = parseClosedPositionLine(line.trim());
    if (position) {
      positions.push(position);
    }
  }

  return positions;
}

/**
 * Extract open position data
 */
function extractOpenPosition(text) {
  const positions = [];
  
  // Look for open position section
  const positionSection = extractSection(text, 'Open Position', 'Summary of Financial Position');
  if (!positionSection) return positions;

  // Parse position lines
  const lines = positionSection.split('\n');
  
  for (const line of lines) {
    const position = parseOpenPositionLine(line.trim());
    if (position) {
      positions.push(position);
    }
  }

  return positions;
}

/**
 * Extract financial summary data
 */
function extractFinancialSummary(text) {
  const summary = {};
  
  // Look for financial summary section
  const summarySection = extractSection(text, 'Summary of Financial Position', 'Summary of Margin Position');
  if (!summarySection) return null;

  // Extract key financial metrics
  const openingMatch = summarySection.match(/Opening\s+Balance[\s:]+([0-9,.-]+)\s*([A-Z]{3})?/i);
  if (openingMatch) {
    summary.openingBalance = parseFloat(openingMatch[1].replace(/,/g, ''));
    summary.currency = openingMatch[2] || 'HKD';
  }

  const closingMatch = summarySection.match(/Closing\s+Balance[\s:]+([0-9,.-]+)\s*([A-Z]{3})?/i);
  if (closingMatch) {
    summary.closingBalance = parseFloat(closingMatch[1].replace(/,/g, ''));
  }

  const netChangeMatch = summarySection.match(/Net\s+Change[\s:]+([0-9,.-]+)/i);
  if (netChangeMatch) {
    summary.netChange = parseFloat(netChangeMatch[1].replace(/,/g, ''));
  }

  return Object.keys(summary).length > 0 ? summary : null;
}

/**
 * Extract margin summary data
 */
function extractMarginSummary(text) {
  const summary = {};
  
  // Look for margin summary section
  const summarySection = extractSection(text, 'Summary of Margin Position', null);
  if (!summarySection) return null;

  // Extract margin metrics
  const initialMarginMatch = summarySection.match(/Initial\s+Margin[\s:]+([0-9,.-]+)\s*([A-Z]{3})?/i);
  if (initialMarginMatch) {
    summary.initialMargin = parseFloat(initialMarginMatch[1].replace(/,/g, ''));
    summary.currency = initialMarginMatch[2] || 'HKD';
  }

  const maintenanceMarginMatch = summarySection.match(/Maintenance\s+Margin[\s:]+([0-9,.-]+)/i);
  if (maintenanceMarginMatch) {
    summary.maintenanceMargin = parseFloat(maintenanceMarginMatch[1].replace(/,/g, ''));
  }

  const availableMarginMatch = summarySection.match(/Available\s+Margin[\s:]+([0-9,.-]+)/i);
  if (availableMarginMatch) {
    summary.availableMargin = parseFloat(availableMarginMatch[1].replace(/,/g, ''));
  }

  return Object.keys(summary).length > 0 ? summary : null;
}

/**
 * Helper function to extract a section between two headers
 */
function extractSection(text, startHeader, endHeader) {
  const startIndex = text.indexOf(startHeader);
  if (startIndex === -1) return null;

  let endIndex = text.length;
  if (endHeader) {
    const endHeaderIndex = text.indexOf(endHeader, startIndex);
    if (endHeaderIndex !== -1) {
      endIndex = endHeaderIndex;
    }
  }

  return text.substring(startIndex, endIndex);
}

/**
 * Helper function to extract a section between two headers (case insensitive)
 */
function extractSectionCaseInsensitive(text, startHeader, endHeader) {
  const upperText = text.toUpperCase();
  const upperStartHeader = startHeader.toUpperCase();
  
  const startIndex = upperText.indexOf(upperStartHeader);
  if (startIndex === -1) return null;

  let endIndex = text.length;
  if (endHeader) {
    const upperEndHeader = endHeader.toUpperCase();
    const endHeaderIndex = upperText.indexOf(upperEndHeader, startIndex);
    if (endHeaderIndex !== -1) {
      endIndex = endHeaderIndex;
    }
  }

  return text.substring(startIndex, endIndex);
}

/**
 * Parse a movement line
 */
function parseMovementLine(line) {
  // Skip header lines and empty lines
  if (!line || line.includes('Date') || line.includes('Description') || line.length < 10) {
    return null;
  }

  // Try to parse tab-separated or space-separated values
  const parts = line.split(/\t+|\s{2,}/).filter(part => part.trim());
  
  if (parts.length >= 4) {
    return {
      date: normalizeDate(parts[0]),
      description: parts[1] || '',
      reference: parts[2] || '',
      debit: parseAmount(parts[3]),
      credit: parseAmount(parts[4]),
      balance: parseAmount(parts[5]),
      currency: parts[6] || 'HKD'
    };
  }

  return null;
}

/**
 * Parse a trade line
 */
function parseTradeLine(line) {
  // Skip header lines and empty lines
  if (!line || line.includes('Date') || line.includes('Instrument') || line.length < 10) {
    return null;
  }

  const parts = line.split(/\t+|\s{2,}/).filter(part => part.trim());
  
  if (parts.length >= 6) {
    return {
      date: normalizeDate(parts[0]),
      instrument: parts[1] || '',
      buySell: parts[2] || '',
      quantity: parseInt(parts[3]) || 0,
      price: parseFloat(parts[4]) || 0,
      amount: parseAmount(parts[5]),
    };
  }

  return null;
}

/**
 * Parse Hong Kong futures trade line
 * Handles both multi-line and single compressed line formats
 */
function parseHKFuturesLine(line) {
  // Skip header lines, empty lines, and summary lines
  if (!line || 
      line.includes('TRADE DATE') || 
      line.includes('ORDER NO') || 
      line.includes('DESCRIPTION') || 
      line.includes('-----') || 
      line.includes('=====') ||
      line.includes('N - Open') ||
      line.includes('新倉') ||
      line.includes('*"Fees and Levy"') ||
      line.length < 20) {
    return null;
  }

  const trades = [];
  
  // Split the line by whitespace, handling multiple spaces
  const parts = line.split(/\s+/).filter(part => part.trim());
  
  // Find all date patterns in the line to identify individual trades
  const dateIndices = [];
  parts.forEach((part, index) => {
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(part)) {
      dateIndices.push(index);
    }
  });
  
  if (dateIndices.length === 0) return null;
  
  // Parse each trade based on date positions
  for (let i = 0; i < dateIndices.length; i++) {
    const startIdx = dateIndices[i];
    const endIdx = i + 1 < dateIndices.length ? dateIndices[i + 1] : parts.length;
    const tradeParts = parts.slice(startIdx, endIdx);
    
    const trade = parseSingleTrade(tradeParts);
    if (trade) {
      trades.push(trade);
    }
  }
  
  // Return the first trade found (since this function expects a single trade)
  return trades.length > 0 ? trades[0] : null;
}

/**
 * Parse a single trade from an array of parts
 */
function parseSingleTrade(parts) {
  if (parts.length < 5) return null;
  
  try {
    let trade = {
      date: normalizeDate(parts[0]),
      orderNo: '',
      instrument: '',
      expiry: '',
      quantity: 0,
      currency: '',
      strikePrice: 0,
      optionType: '',
      premium: 0,
      commission: 0,
      fees: 0,
      buySell: 'N/A',
      price: 0,
      amount: 0
    };
    
    let idx = 1;
    
    // Skip # symbol if present
    if (parts[idx] === '#') {
      idx++;
    }
    
    // Extract order number (6+ digits)
    if (idx < parts.length && /^\d{6,}$/.test(parts[idx])) {
      trade.orderNo = parts[idx];
      idx++;
    }
    
    // Skip market field (3-5 capital letters) without storing it
    if (idx < parts.length && /^[A-Z]{3,5}$/.test(parts[idx])) {
      idx++;
    }
    
    // Extract instrument code (1-3 capital letters)
    if (idx < parts.length && /^[A-Z]{1,3}$/.test(parts[idx])) {
      trade.instrument = parts[idx];
      idx++;
    }
    
    // Extract expiry and description parts
    let descParts = [];
    while (idx < parts.length && 
           !/^\d+$/.test(parts[idx]) && 
           !/^[A-Z]{3}$/.test(parts[idx]) &&
           !/^[\d,.]+$/.test(parts[idx])) {
      descParts.push(parts[idx]);
      idx++;
    }
    trade.expiry = descParts.join(' ');
    
    // Extract quantity (pure number)
    if (idx < parts.length && /^\d+$/.test(parts[idx])) {
      trade.quantity = parseInt(parts[idx]);
      idx++;
    }
    
    // Extract currency (3 letters)
    if (idx < parts.length && /^[A-Z]{3}$/.test(parts[idx])) {
      trade.currency = parts[idx];
      idx++;
    }
    
    // Extract strike price (number with decimals)
    if (idx < parts.length && /^[\d,.]+$/.test(parts[idx])) {
      trade.strikePrice = parseFloat(parts[idx].replace(/,/g, ''));
      trade.price = trade.strikePrice;
      idx++;
    }
    
    // Extract option type (CALL/PUT)
    if (idx < parts.length && /^(CALL|PUT)$/.test(parts[idx])) {
      trade.optionType = parts[idx];
      idx++;
    }
    
    // Extract premium (number with decimals)
    if (idx < parts.length && /^[\d,.]+$/.test(parts[idx])) {
      trade.premium = parseFloat(parts[idx].replace(/,/g, ''));
      idx++;
    }
    
    // Skip currency if present
    if (idx < parts.length && /^[A-Z]{3}$/.test(parts[idx])) {
      idx++;
    }
    
    // Extract commission (usually negative)
    if (idx < parts.length && /^[\d,.-]+$/.test(parts[idx])) {
      trade.commission = parseAmount(parts[idx]);
      idx++;
    }
    
    // Skip commission currency if present
    if (idx < parts.length && /^[A-Z]{3}$/.test(parts[idx])) {
      idx++;
    }
    
    // Extract fees (usually negative)
    if (idx < parts.length && /^[\d,.-]+$/.test(parts[idx])) {
      trade.fees = parseAmount(parts[idx]);
      idx++;
    }
    
    // Calculate amount
    if (trade.quantity && trade.premium) {
      trade.amount = trade.quantity * trade.premium;
    }
    
    // Only return trade if we have minimum required data
    if (trade.orderNo && trade.instrument && trade.quantity > 0) {
      return trade;
    }
    
  } catch (error) {
    console.log('Error parsing single trade:', error.message);
  }
  
  return null;
}

/**
 * Parse a closed position line
 */
function parseClosedPositionLine(line) {
  if (!line || line.includes('Instrument') || line.length < 10) {
    return null;
  }

  const parts = line.split(/\t+|\s{2,}/).filter(part => part.trim());
  
  if (parts.length >= 6) {
    return {
      instrument: parts[0] || '',
      openDate: normalizeDate(parts[1]),
      closeDate: normalizeDate(parts[2]),
      quantity: parseInt(parts[3]) || 0,
      openPrice: parseFloat(parts[4]) || 0,
      closePrice: parseFloat(parts[5]) || 0,
      pnl: parseAmount(parts[6]),
      currency: parts[7] || 'HKD'
    };
  }

  return null;
}

/**
 * Parse an open position line
 */
function parseOpenPositionLine(line) {
  if (!line || line.includes('Instrument') || line.length < 10) {
    return null;
  }

  const parts = line.split(/\t+|\s{2,}/).filter(part => part.trim());
  
  if (parts.length >= 5) {
    return {
      instrument: parts[0] || '',
      position: parts[1] || '',
      quantity: parseInt(parts[2]) || 0,
      averagePrice: parseFloat(parts[3]) || 0,
      marketPrice: parseFloat(parts[4]) || 0,
      marketValue: parseAmount(parts[5]),
      unrealizedPnl: parseAmount(parts[6]),
      currency: parts[7] || 'HKD'
    };
  }

  return null;
}

/**
 * Normalize date format to YYYY-MM-DD
 */
function normalizeDate(dateStr) {
  if (!dateStr) return '';

  const trimmedDate = dateStr.trim();

  // Handle DD/MM/YYYY format
  const ddmmyyyy = trimmedDate.match(/(\d{2})\/(\d{2})\/(\d{4})/);
  if (ddmmyyyy) {
    return `${ddmmyyyy[3]}-${ddmmyyyy[2]}-${ddmmyyyy[1]}`;
  }

  // Handle YYYY-MM-DD format (already normalized)
  if (trimmedDate.match(/\d{4}-\d{2}-\d{2}/)) {
    return trimmedDate;
  }

  // Handle "DD MMM YYYY" format (e.g., "26 AUG 2025")
  const ddmmmyyyy = trimmedDate.match(/(\d{1,2})\s+([A-Z]{3})\s+(\d{4})/i);
  if (ddmmmyyyy) {
    const day = ddmmmyyyy[1].padStart(2, '0');
    const monthName = ddmmmyyyy[2].toUpperCase();
    const year = ddmmmyyyy[3];

    // Convert month name to number
    const monthMap = {
      'JAN': '01', 'FEB': '02', 'MAR': '03', 'APR': '04',
      'MAY': '05', 'JUN': '06', 'JUL': '07', 'AUG': '08',
      'SEP': '09', 'OCT': '10', 'NOV': '11', 'DEC': '12'
    };

    const month = monthMap[monthName];
    if (month) {
      return `${year}-${month}-${day}`;
    }
  }

  return trimmedDate;
}

/**
 * Parse amount string to number
 */
function parseAmount(amountStr) {
  if (!amountStr || amountStr === '-') return 0;
  
  // Remove commas and parse as float
  const cleaned = amountStr.replace(/,/g, '');
  const amount = parseFloat(cleaned);
  
  return isNaN(amount) ? 0 : amount;
}

module.exports = {
  parseStatement
};
