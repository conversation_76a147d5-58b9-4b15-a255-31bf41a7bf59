const statementParser = require('../src/server-utils/statement-parser');
const fs = require('fs');
const path = require('path');

async function examineRawText() {
  console.log('🔍 EXAMINING RAW PDF TEXT STRUCTURE');
  console.log('===================================');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    const password = '6369';
    
    // Parse PDF to get raw text
    const PDFExtract = require('pdf.js-extract').PDFExtract;
    const pdfExtract = new PDFExtract();
    
    // Write buffer to temporary file
    const tempFilePath = path.join(__dirname, 'temp_statement.pdf');
    fs.writeFileSync(tempFilePath, pdfBuffer);
    
    const data = await new Promise((resolve, reject) => {
      pdfExtract.extract(tempFilePath, { password: password.trim() }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    
    // Convert to text
    const text = data.pages.map(page =>
      page.content.map(item => item.str).join(' ')
    ).join('\n');
    
    console.log('📄 Full PDF Text Length:', text.length);
    
    // Find TRADE CONFIRMATION section
    const tradeConfStart = text.toUpperCase().indexOf('TRADE CONFIRMATION');
    const positionClosedStart = text.toUpperCase().indexOf('POSITION(S) CLOSED');
    
    if (tradeConfStart !== -1) {
      console.log('\n🎯 TRADE CONFIRMATION SECTION FOUND at position:', tradeConfStart);
      
      let endPos = text.length;
      if (positionClosedStart !== -1 && positionClosedStart > tradeConfStart) {
        endPos = positionClosedStart;
        console.log('📍 POSITION(S) CLOSED SECTION FOUND at position:', positionClosedStart);
      }
      
      const tradeSection = text.substring(tradeConfStart, endPos);
      console.log('\n📝 TRADE CONFIRMATION SECTION (first 1000 chars):');
      console.log('=' .repeat(60));
      console.log(tradeSection.substring(0, 1000));
      console.log('=' .repeat(60));
      
      console.log('\n📊 TRADE CONFIRMATION SECTION (last 500 chars):');
      console.log('=' .repeat(60));
      console.log(tradeSection.substring(Math.max(0, tradeSection.length - 500)));
      console.log('=' .repeat(60));
      
      // Look for lines with dates
      const lines = tradeSection.split('\n');
      console.log('\n📅 LINES WITH DATES (26/08/2025):');
      console.log('=' .repeat(60));
      
      let tradeLines = [];
      lines.forEach((line, idx) => {
        if (line.includes('26/08/2025')) {
          console.log(`Line ${idx}: ${line}`);
          tradeLines.push(line);
        }
      });
      
      console.log(`\n✅ Found ${tradeLines.length} lines with statement date (26/08/2025)`);
      
    } else {
      console.log('❌ TRADE CONFIRMATION section not found');
    }
    
    // Clean up
    fs.unlinkSync(tempFilePath);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

examineRawText();
