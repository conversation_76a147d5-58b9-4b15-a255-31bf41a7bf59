# OptionsTradesQuery Component Updates

## Overview
The OptionsTradesQuery component has been redesigned to display options trades in an accordion-style table, grouped by expiry date. This document outlines the key features and implementation details of the updated component.

## Key Features

### 1. Accordion-Style Grouping by Expiry Date
- Trades are now grouped by expiry date in collapsible accordion sections
- Each expiry date section can be expanded/collapsed independently
- The first expiry date section is auto-expanded by default
- Each accordion header displays:
  - Formatted expiry date
  - Total number of positions
  - Count of Call and Put options

### 2. Expiry Date Sorting and Formatting
- Expiry dates are sorted chronologically with special handling for month-only formats
- Month-only formats (YYYY-MM) are interpreted as end-of-month and sorted after specific dates in the same month
- Example: "2025-05-16" < "2025-05-23" < "2025-05"
- Month-only formats display the actual last day of the month with an "(EOM)" indicator
- Example: "2025-05" displays as "25-05-31 (EOM)"

### 3. Futures and Options Organization
- Within each expiry date section, futures trades are displayed at the top
- Options trades (Calls and Puts) are displayed below futures
- Futures have a distinct yellow background and separate table structure
- Options maintain the Call/Strike/Put table structure with cyan and red headers

### 4. Responsive to Position Table Filtering
- The component is linked to the position table and only displays what's showing there
- It uses the filtered positions directly from the parent component
- No separate API calls or data fetching is performed

## Implementation Details

### Data Structure
The component organizes data in a hierarchical structure:
1. Expiry Dates (accordion sections)
2. Position Types (Futures at top, Options below)
3. Strike Prices (for options only)
4. Call/Put sides

### Key Functions

#### `groupPositionsByExpiryDate()`
- Groups positions by their expiry date
- Implements custom sorting for month-only formats
- Returns an object with positions grouped by expiry date and a sorted array of expiry dates

#### `groupTradesByStrike(positionsForExpiry)`
- Groups positions for a specific expiry date by strike price and type (Call/Put)
- Filters out Future trades as they don't have a strike price
- Returns an object with positions grouped by strike price and a sorted array of strike prices

#### `calculateTotalsForExpiry(positionsForExpiry)`
- Calculates total quantities and P&L for Call and Put options for a specific expiry date
- Used for the totals row in each options table

#### `formatDate(dateString)`
- Formats expiry dates for display
- Handles both YYYY-MM-DD and YYYY-MM formats
- For month-only formats, calculates the last day of the month and adds "(EOM)" indicator

### State Management
- `expandedExpiryDates`: Tracks which expiry date sections are expanded/collapsed
- Auto-expands the first expiry date section by default
- Toggle function allows users to expand/collapse sections

## UI Components

### Accordion Header
```jsx
<div 
  className="flex justify-between items-center p-2 bg-gray-100 cursor-pointer hover:bg-gray-200"
  onClick={() => toggleExpiryDateExpansion(expiryDate)}
>
  <div className="font-bold text-sm">
    Expiry: {formatDate(expiryDate)}
    <span className="ml-2 text-xs text-gray-500">
      ({positionsForExpiry.length} positions)
    </span>
  </div>
  <div className="flex items-center">
    <div className="text-xs mr-4">
      <span className="bg-cyan-100 px-1 py-0.5 rounded mr-1">
        Calls: {positionsForExpiry.filter(p => p.type === 'Call').length}
      </span>
      <span className="bg-red-100 px-1 py-0.5 rounded">
        Puts: {positionsForExpiry.filter(p => p.type === 'Put').length}
      </span>
    </div>
    <svg 
      className={`w-4 h-4 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`} 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
    </svg>
  </div>
</div>
```

### Futures Section
```jsx
{positionsForExpiry.some(p => p.type === 'Future') && (
  <div className="mb-3">
    <div className="bg-yellow-100 p-1 text-center font-bold text-xs rounded">
      FUTURES
    </div>
    <div className="grid grid-cols-4 gap-0 mb-1 text-center font-bold border-b border-gray-300 text-xs">
      <div className="p-1 border-r border-gray-300">Ticker</div>
      <div className="p-1 border-r border-gray-300">Qty</div>
      <div className="p-1 border-r border-gray-300">Price</div>
      <div className="p-1">debitCredit</div>
    </div>
    {positionsForExpiry.filter(p => p.type === 'Future').map(future => (
      <div key={future.id} className="grid grid-cols-4 gap-0 text-center border-b border-gray-200 text-xs bg-yellow-50">
        <div className="p-1 border-r border-gray-300">{future.ticker}</div>
        <div className="p-1 border-r border-gray-300">{future.quantity}</div>
        <div className="p-1 border-r border-gray-300">{future.premium.toFixed(2)}</div>
        <div className="p-1">{future.debitCredit.toFixed(2)}</div>
      </div>
    ))}
  </div>
)}
```

## Usage
The component is used in the options-analyzer.jsx file and receives the following props:
- `positions`: Array of position objects from the position table
- `symbol`: Current selected symbol (e.g., 'HSI', 'HHI')

```jsx
<OptionsTradesQuery
  positions={filteredPositions.length > 0 ? filteredPositions : positions}
  symbol={symbol}
/>
```

## Future Enhancements
Potential future enhancements could include:
- Adding a "Expand All" / "Collapse All" button
- Implementing search/filter functionality within the component
- Adding sorting options for the tables
- Enhancing the visual design with more interactive elements
