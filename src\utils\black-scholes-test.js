// Test script to verify Black-Scholes calculations
import { calculateOptionPrice } from './black-scholes-robust.js';
import { calculatePositionPremium } from './position-utils.js';

// Function to test Black-Scholes directly
function testBlackScholes() {
  console.log("=== DIRECT BLACK-SCHOLES TESTS ===");

  // Test case 1: Call option with standard parameters
  const callPrice = calculateOptionPrice(
    'Call',
    22600, // Stock price
    22200, // Strike price
    0.02,  // Risk-free rate (2%)
    0.3,   // Volatility (30%)
    3/365  // Time to expiry (3 days)
  );
  console.log(`Call option (S=22600, K=22200, r=2%, v=30%, T=3 days): ${callPrice.toFixed(2)}`);

  // Test case 2: Put option with standard parameters
  const putPrice = calculateOptionPrice(
    'Put',
    22600, // Stock price
    22200, // Strike price
    0.02,  // Risk-free rate (2%)
    0.3,   // Volatility (30%)
    3/365  // Time to expiry (3 days)
  );
  console.log(`Put option (S=22600, K=22200, r=2%, v=30%, T=3 days): ${putPrice.toFixed(2)}`);

  // Test case 3: Call option at expiry (in the money)
  const callAtExpiry = calculateOptionPrice(
    'Call',
    22600, // Stock price
    22200, // Strike price
    0.02,  // Risk-free rate (2%)
    0.3,   // Volatility (30%)
    0      // Time to expiry (0 days)
  );
  console.log(`Call option at expiry (S=22600, K=22200): ${callAtExpiry.toFixed(2)}`);

  // Test case 4: Put option at expiry (out of the money)
  const putAtExpiry = calculateOptionPrice(
    'Put',
    22600, // Stock price
    22200, // Strike price
    0.02,  // Risk-free rate (2%)
    0.3,   // Volatility (30%)
    0      // Time to expiry (0 days)
  );
  console.log(`Put option at expiry (S=22600, K=22200): ${putAtExpiry.toFixed(2)}`);
}

// Function to test position premium calculation
function testPositionPremium() {
  console.log("\n=== POSITION PREMIUM TESTS ===");

  // Test case 1: Call option with standard parameters
  const callPosition = {
    id: 'test-call',
    type: 'Call',
    strike: 22200,
    daysToExpiry: 3
  };
  const callPremium = calculatePositionPremium(
    callPosition,
    22600, // Stock price
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  console.log(`Call position premium (S=22600, K=22200, r=2%, v=30%, T=3 days): ${callPremium.toFixed(2)}`);

  // Test case 2: Put option with standard parameters
  const putPosition = {
    id: 'test-put',
    type: 'Put',
    strike: 22200,
    daysToExpiry: 3
  };
  const putPremium = calculatePositionPremium(
    putPosition,
    22600, // Stock price
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  console.log(`Put position premium (S=22600, K=22200, r=2%, v=30%, T=3 days): ${putPremium.toFixed(2)}`);

  // Test case 3: Call option at expiry (in the money)
  const callAtExpiryPosition = {
    id: 'test-call-expiry',
    type: 'Call',
    strike: 22200,
    daysToExpiry: 0
  };
  const callAtExpiryPremium = calculatePositionPremium(
    callAtExpiryPosition,
    22600, // Stock price
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  console.log(`Call position at expiry (S=22600, K=22200): ${callAtExpiryPremium.toFixed(2)}`);

  // Test case 4: Put option at expiry (out of the money)
  const putAtExpiryPosition = {
    id: 'test-put-expiry',
    type: 'Put',
    strike: 22200,
    daysToExpiry: 0
  };
  const putAtExpiryPremium = calculatePositionPremium(
    putAtExpiryPosition,
    22600, // Stock price
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  console.log(`Put position at expiry (S=22600, K=22200): ${putAtExpiryPremium.toFixed(2)}`);

  // Test case 5: Future position
  const futurePosition = {
    id: 'test-future',
    type: 'Future',
    strike: 'N/A',
    daysToExpiry: 3
  };
  const futurePremium = calculatePositionPremium(
    futurePosition,
    22600, // Stock price
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  console.log(`Future position premium (S=22600): ${futurePremium.toFixed(2)}`);
}

// Run the tests
testBlackScholes();
testPositionPremium();

// Export a function to run the tests
export function runBlackScholesTests() {
  testBlackScholes();
  testPositionPremium();
}
