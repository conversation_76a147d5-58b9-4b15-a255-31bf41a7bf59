# Options Strategy Analyzer

A React-based web application for visualizing and analyzing options trading strategies.

## Features

- Interactive options strategy builder with position management
- Black-Scholes model implementation for accurate options pricing
- Profit/loss visualization at different price points
- Time decay analysis showing how option values change over time
- Parameter controls for stock price, volatility, risk-free rate, and days to expiration
- Firebase Firestore integration for trade data storage
- Stock filtering by symbol and expiry date
- Accordion-style trade display grouped by expiry date

## Technology Stack

- React 18
- Chart.js with plugins for data visualization
- Tailwind CSS for styling
- Express.js backend
- Firebase Firestore for data storage
- PostgreSQL database (optional)

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/WP00N/options-analyzer.git
   cd options-analyzer
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the backend server:
   ```
   node server.js
   ```

4. In a new terminal, start the development server:
   ```
   npm start
   ```

5. Open your browser and navigate to `http://localhost:3000`

### Database Setup

The application supports both PostgreSQL and Firebase Firestore:

#### PostgreSQL Setup
Make sure you have PostgreSQL installed and running, and set up the database connection in the `.env` file:

```
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
```

The required table will be created automatically when the server starts.

#### Firebase Setup
Place your Firebase service account key in `firebase.json` at the project root.

## Project Structure

```
options-analyzer/
├── memory-bank/           # Project documentation
├── public/                # Static assets
├── server.js              # Express backend server
├── src/
│   ├── components/        # React components
│   │   ├── options-analyzer/
│   │   │   ├── PositionTable.jsx
│   │   │   ├── Sliders.jsx
│   │   │   ├── StrategyChart.jsx
│   │   │   ├── TimeDecayChart.jsx
│   │   │   └── index.jsx
│   │   ├── strategy-chart-fixed.jsx
│   │   ├── database-offline-indicator.jsx
│   │   └── ...
│   ├── context/           # React context providers
│   ├── utils/             # Client-side utility functions
│   │   ├── black-scholes.js
│   │   ├── firebase-position-utils.js
│   │   ├── position-utils.js
│   │   └── ...
│   ├── App.jsx            # Main application component
│   └── index.jsx          # Application entry point
├── utils/                 # Server-side utility functions
│   ├── firebase.ts        # Firebase admin SDK setup
│   ├── stockUtils.js      # Yahoo Finance API integration
│   ├── trade-parser.js    # Trade data parsing utilities
│   └── ...
└── obsolete/              # Deprecated code and files
```

## Usage

1. Add options positions using the position table
2. Adjust parameters using the sliders section
3. View profit/loss visualization in the strategy chart
4. Analyze time decay effects in the time decay chart
5. Query and filter trades by stock symbol and expiry date

## API Endpoints

The application provides several API endpoints:

- `GET /api/firebase-stocks` - Get all unique stock symbols
- `GET /api/firebase-expiry-dates?stock=XXX` - Get expiry dates for a specific stock
- `GET /api/firebase-trades-by-expiry?expiryDate=YYYY-MM-DD&stock=XXX` - Get trades for a specific expiry date and stock
- `GET /api/firebase-all-trades` - Get all trades
- `POST /api/firebase-add-trade` - Add a new trade
- `POST /api/parse-trades` - Parse trade data
- `POST /api/upload-trades` - Upload parsed trades
- `GET /api/server-logs` - Get server logs
- `GET /api/firebase-raw-data` - Get raw Firestore data

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
