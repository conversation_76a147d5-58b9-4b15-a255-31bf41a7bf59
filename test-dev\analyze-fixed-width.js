const PDFExtract = require('pdf.js-extract').PDFExtract;
const fs = require('fs');
const path = require('path');

async function analyzeFixedWidthPositions() {
  console.log('🔍 ANALYZING FIXED-WIDTH COLUMN POSITIONS');
  console.log('========================================');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    const password = '6369';
    
    const pdfExtract = new PDFExtract();
    const tempFilePath = path.join(__dirname, 'temp_statement.pdf');
    fs.writeFileSync(tempFilePath, pdfBuffer);
    
    const data = await new Promise((resolve, reject) => {
      pdfExtract.extract(tempFilePath, { password: password.trim() }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    
    // Convert to text
    const text = data.pages.map(page =>
      page.content.map(item => item.str).join(' ')
    ).join('\n');
    
    // Find the trade section
    const tradeConfStart = text.toUpperCase().indexOf('TRADE CONFIRMATION');
    const positionClosedStart = text.toUpperCase().indexOf('POSITION(S) CLOSED');
    const tradeSection = text.substring(tradeConfStart, positionClosedStart);
    
    // Find the line with trades
    const lines = tradeSection.split('\n');
    const tradeLine = lines.find(line => line.includes('26/08/2025') && line.includes('640201'));
    
    if (!tradeLine) {
      console.log('❌ Trade line not found');
      return;
    }
    
    console.log('📏 ANALYZING LINE CHARACTER POSITIONS:');
    console.log('Line length:', tradeLine.length);
    console.log('');
    
    // Print character positions for analysis
    console.log('Position ruler (every 10 chars):');
    const ruler1 = '0123456789'.repeat(Math.ceil(tradeLine.length / 10)).substring(0, tradeLine.length);
    const ruler2 = '0         1         2         3         4         5         6         7         8         9         '.repeat(Math.ceil(tradeLine.length / 100)).substring(0, tradeLine.length);
    
    console.log(ruler2);
    console.log(ruler1);
    console.log(tradeLine);
    console.log('');
    
    // Split by dates to get individual trade entries
    const tradeParts = tradeLine.split('26/08/2025').filter(part => part.trim().length > 5);
    
    console.log(`📊 Found ${tradeParts.length} trade entries`);
    
    // Analyze first few trades to determine column positions
    for (let i = 0; i < Math.min(3, tradeParts.length); i++) {
      const tradeText = '26/08/2025' + tradeParts[i];
      const cleanTrade = tradeText.substring(0, tradeText.indexOf('26/08/2025', 1) > 0 ? tradeText.indexOf('26/08/2025', 1) : tradeText.length);
      
      console.log(`\n🎯 TRADE ${i + 1} ANALYSIS:`);
      console.log(`Length: ${cleanTrade.length}`);
      console.log('Position ruler:');
      const tradeRuler1 = '0123456789'.repeat(Math.ceil(cleanTrade.length / 10)).substring(0, cleanTrade.length);
      const tradeRuler2 = '0         1         2         3         4         5         6         7         8         '.repeat(Math.ceil(cleanTrade.length / 100)).substring(0, cleanTrade.length);
      
      console.log(tradeRuler2);
      console.log(tradeRuler1);
      console.log(cleanTrade);
      
      // Try to identify key field boundaries
      console.log('\n📍 Field boundaries:');
      
      // Find key markers
      const dateEnd = cleanTrade.indexOf('26/08/2025') + 10;
      const hkfePos = cleanTrade.indexOf('HKFE');
      const hkdPositions = [];
      let hkdIndex = cleanTrade.indexOf('HKD');
      while (hkdIndex !== -1) {
        hkdPositions.push(hkdIndex);
        hkdIndex = cleanTrade.indexOf('HKD', hkdIndex + 1);
      }
      
      console.log(`Date ends at: ${dateEnd}`);
      console.log(`HKFE at: ${hkfePos}`);
      console.log(`HKD positions: [${hkdPositions.join(', ')}]`);
      
      // Look for CALL/PUT positions
      const callPos = cleanTrade.indexOf('CALL');
      const putPos = cleanTrade.indexOf('PUT');
      console.log(`CALL at: ${callPos}, PUT at: ${putPos}`);
      
      if (i < 2) console.log(''); // Add spacing between trades
    }
    
    // Clean up
    fs.unlinkSync(tempFilePath);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

analyzeFixedWidthPositions();
