/**
 * Test the fixed date parsing for 2025-10-07
 */

function testFixedDateParsing() {
  console.log('🧪 TESTING FIXED DATE PARSING');
  console.log('=' .repeat(35));
  
  // Test the fixed parsing logic
  const testDateStr = '7/10/2025';
  console.log(`🎯 Testing date string: "${testDateStr}"`);
  
  // Parse with the new logic (setting time to noon)
  const [day, month, year] = testDateStr.split('/');
  const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0, 0);
  
  console.log(`   Created Date: ${holidayDate.toISOString()}`);
  console.log(`   Local date string: ${holidayDate.toISOString().split('T')[0]}`);
  console.log(`   Expected: 2025-10-07`);
  
  const isCorrect = holidayDate.toISOString().split('T')[0] === '2025-10-07';
  console.log(`   ✅ Parsing fixed: ${isCorrect ? 'YES' : 'NO'}`);
  
  // Test the date comparison that would happen in isHoliday function
  const testReferenceDate = new Date('2025-10-07');
  testReferenceDate.setHours(12, 0, 0, 0);
  
  console.log(`\n🔍 Testing date comparison:`);
  console.log(`   Holiday date: ${holidayDate.getFullYear()}-${holidayDate.getMonth() + 1}-${holidayDate.getDate()}`);
  console.log(`   Reference date: ${testReferenceDate.getFullYear()}-${testReferenceDate.getMonth() + 1}-${testReferenceDate.getDate()}`);
  
  const datesMatch = holidayDate.getFullYear() === testReferenceDate.getFullYear() &&
                    holidayDate.getMonth() === testReferenceDate.getMonth() &&
                    holidayDate.getDate() === testReferenceDate.getDate();
  
  console.log(`   ✅ Dates match: ${datesMatch ? 'YES' : 'NO'}`);
  
  return isCorrect && datesMatch;
}

function testCalculateDaysToExpiryLogic() {
  console.log('\n🎯 TESTING calculateDaysToExpiry LOGIC');
  console.log('=' .repeat(40));
  
  // Simulate the fixed logic
  const mockHolidays = [
    new Date(2025, 9, 7, 12, 0, 0, 0), // October 7, 2025 at noon
    new Date(2025, 9, 1, 12, 0, 0, 0), // October 1, 2025 at noon  
  ];
  
  console.log('📅 Mock holidays:');
  mockHolidays.forEach(h => {
    console.log(`   - ${h.toISOString().split('T')[0]} (${h.toLocaleDateString('en-US', { weekday: 'long' })})`);
  });
  
  // Test dates around the holiday
  const testDates = ['2025-10-06', '2025-10-07', '2025-10-08'];
  const expiryDate = '2025-10-17';
  
  console.log(`\n🧮 Testing days to expiry (expiry: ${expiryDate}):`);
  
  testDates.forEach(dateStr => {
    const referenceDate = new Date(dateStr);
    referenceDate.setHours(12, 0, 0, 0);
    
    const expiry = new Date('2025-10-17');
    expiry.setHours(12, 0, 0, 0);
    
    // Simple trading days calculation
    let tradingDays = 0;
    const currentDate = new Date(referenceDate);
    currentDate.setDate(currentDate.getDate() + 1);
    
    while (currentDate <= expiry) {
      const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6;
      const isHoliday = mockHolidays.some(holiday => {
        return holiday.getFullYear() === currentDate.getFullYear() &&
               holiday.getMonth() === currentDate.getMonth() &&
               holiday.getDate() === currentDate.getDate();
      });
      
      if (!isWeekend && !isHoliday) {
        tradingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    console.log(`   ${dateStr}: ${tradingDays} trading days`);
    
    if (dateStr === '2025-10-07') {
      console.log(`   🎯 This should equal 2025-10-06 (since 2025-10-07 is a holiday)`);
    }
  });
}

function testOverallFix() {
  console.log('\n🚀 TESTING OVERALL FIX');
  console.log('=' .repeat(25));
  
  const parsingFixed = testFixedDateParsing();
  
  if (parsingFixed) {
    console.log('✅ Date parsing is now fixed!');
    testCalculateDaysToExpiryLogic();
    
    console.log('\n📋 EXPECTED RESULTS AFTER FIX:');
    console.log('1. ✅ 2025-10-07 should be correctly parsed as October 7th');
    console.log('2. ✅ Holiday comparison should work correctly');
    console.log('3. ✅ 2025-10-07 should show same trading days as 2025-10-06');
    console.log('4. ✅ Black-Scholes should not show time decay on holiday');
    
  } else {
    console.log('❌ Date parsing still has issues');
  }
}

if (require.main === module) {
  testOverallFix();
}

module.exports = { testFixedDateParsing, testCalculateDaysToExpiryLogic };