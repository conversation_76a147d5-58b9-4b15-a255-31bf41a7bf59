# Filtered Trades P&L Chart Implementation

## Overview
Added a P&L chart component that displays the profit/loss visualization for filtered trades from the All-trades component. This chart updates dynamically based on the stock and expiry filters selected in the All-trades table.

## Components Created/Modified

### 1. Modified `src/components/all-trades.jsx`
- Added optional `onFiltersChange` prop to expose filter state
- Added useEffect to notify parent component when filters change
- Passes `selectedStock`, `selectedExpiry`, and `filteredTrades` to the callback

### 2. Created `src/components/filtered-trades-pnl-chart.jsx`
- New component that displays P&L chart for filtered trades
- Uses `usePnLAtVariousPriceStore.getFilteredData()` to get filtered P&L data
- Reuses the existing `TradesPnLChart` component for rendering
- Shows appropriate message when no trades match the filters
- Displays filter information in the chart title

### 3. Modified `src/pages/analysis.jsx`
- Added state to track filters from AllTrades component
- Added `FilteredTradesPnLChart` component after the AllTrades component
- Connected the filter state between AllTrades and FilteredTradesPnLChart

## Data Flow
1. User selects filters in AllTrades component (stock/expiry)
2. AllTrades component calls `onFiltersChange` callback with current filter state
3. Analysis page updates its state with the new filter values
4. FilteredTradesPnLChart receives the filter props and updates accordingly
5. FilteredTradesPnLChart uses `usePnLAtVariousPriceStore.getFilteredData()` to get filtered P&L data
6. The filtered data is passed to the reused `TradesPnLChart` component for visualization

## Key Features
- **Dynamic Filtering**: Chart updates automatically when filters change in AllTrades
- **Reusable Components**: Leverages existing TradesPnLChart component
- **Data Integration**: Uses existing usePnLAtVariousPriceStore for P&L calculations
- **User Feedback**: Shows appropriate messages when no data matches filters
- **Filter Display**: Chart title shows current filter selection

## Usage
The filtered trades P&L chart appears automatically below the All-trades component in the Analysis page. Users can:
1. Select stock and/or expiry filters in the All-trades table
2. View the corresponding P&L chart that updates based on their selections
3. See trade count and filter information in the chart header

## Technical Notes
- Uses `usePnLAtVariousPriceStore.getFilteredData()` for data filtering
- Maintains consistency with existing analysis parameters (volatility, risk-free rate, etc.)
- Handles edge cases like no matching trades gracefully
- Integrates seamlessly with existing state management patterns
