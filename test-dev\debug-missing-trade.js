const PDFExtract = require('pdf.js-extract').PDFExtract;
const fs = require('fs');
const path = require('path');

async function debugMissingTrade() {
  console.log('🔍 DEBUGGING THE MISSING 13TH TRADE');
  console.log('===================================');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    const password = '6369';
    
    const pdfExtract = new PDFExtract();
    const tempFilePath = path.join(__dirname, 'temp_statement.pdf');
    fs.writeFileSync(tempFilePath, pdfBuffer);
    
    const data = await new Promise((resolve, reject) => {
      pdfExtract.extract(tempFilePath, { password: password.trim() }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    
    // Convert to text
    const text = data.pages.map(page =>
      page.content.map(item => item.str).join(' ')
    ).join('\n');
    
    // Find the trade section
    const tradeConfStart = text.toUpperCase().indexOf('TRADE CONFIRMATION');
    const positionClosedStart = text.toUpperCase().indexOf('POSITION(S) CLOSED');
    const tradeSection = text.substring(tradeConfStart, positionClosedStart);
    
    // Look for all occurrences of 26/08/2025
    const dateOccurrences = [];
    let index = tradeSection.indexOf('26/08/2025');
    while (index !== -1) {
      dateOccurrences.push(index);
      index = tradeSection.indexOf('26/08/2025', index + 1);
    }
    
    console.log(`📅 Found ${dateOccurrences.length} occurrences of '26/08/2025' in trade section`);
    
    // Look specifically for the 664135 trade
    console.log('\n🔍 Looking for trade 664135:');
    const trade664135Index = tradeSection.indexOf('664135');
    if (trade664135Index !== -1) {
      console.log('✅ Found trade 664135 at position:', trade664135Index);
      
      // Extract context around this trade
      const contextStart = Math.max(0, trade664135Index - 100);
      const contextEnd = Math.min(tradeSection.length, trade664135Index + 200);
      const context = tradeSection.substring(contextStart, contextEnd);
      
      console.log('\n📝 Context around trade 664135:');
      console.log('=' .repeat(80));
      console.log(context);
      console.log('=' .repeat(80));
      
      // Look for the pattern after 664135
      const afterTrade = tradeSection.substring(trade664135Index);
      const nextDateIndex = afterTrade.indexOf('26/08/2025', 1);
      
      if (nextDateIndex === -1) {
        console.log('\n🚨 No date found after 664135 - this might be the last trade');
        
        // Look for summary patterns
        const summaryIndex = afterTrade.indexOf('-----');
        if (summaryIndex !== -1) {
          const tradeContent = afterTrade.substring(0, summaryIndex);
          console.log('\n📋 Trade 664135 full content (until summary):');
          console.log('=' .repeat(80));
          console.log('26/08/2025' + tradeContent);
          console.log('=' .repeat(80));
        }
      }
      
    } else {
      console.log('❌ Trade 664135 not found in trade section');
    }
    
    // Check the actual line parsing
    console.log('\n🧪 TESTING LINE PARSING:');
    const lines = tradeSection.split('\n');
    const tradeDateLines = lines.filter(line => line.includes('26/08/2025'));
    
    console.log(`Found ${tradeDateLines.length} lines containing the date`);
    
    if (tradeDateLines.length > 0) {
      const mainTradeLine = tradeDateLines[0];
      console.log('\n📝 Main trade line length:', mainTradeLine.length);
      
      // Count 26/08/2025 occurrences in the main line
      const dateMatches = (mainTradeLine.match(/26\/08\/2025/g) || []);
      console.log(`Trade line contains ${dateMatches.length} date occurrences`);
      
      // Split by date and examine each part
      const tradeParts = mainTradeLine.split('26/08/2025').filter(part => part.trim().length > 0);
      console.log(`Split into ${tradeParts.length} parts`);
      
      // Look for 664135 specifically
      const part664135 = tradeParts.find(part => part.includes('664135'));
      if (part664135) {
        console.log('\n🎯 Found 664135 in part:');
        console.log('=' .repeat(80));
        console.log('26/08/2025' + part664135.substring(0, 300));
        console.log('=' .repeat(80));
      } else {
        console.log('\n❌ 664135 not found in any split part');
        
        // Check if it's at the very end
        const fullLine = '26/08/2025' + tradeParts.join('26/08/2025');
        if (fullLine.includes('664135')) {
          console.log('✅ 664135 is present in the full reconstructed line');
          
          // Find its position
          const pos664135 = fullLine.indexOf('664135');
          const context = fullLine.substring(Math.max(0, pos664135 - 50), pos664135 + 100);
          console.log('Context:', context);
        }
      }
    }
    
    // Clean up
    fs.unlinkSync(tempFilePath);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugMissingTrade();
