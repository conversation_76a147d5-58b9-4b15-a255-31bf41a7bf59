# Tech Context

## Technologies Used:
- React framework (v18)
- React Router (v7.5.3) for page navigation with custom implementation
- Recharts for data visualization
- Chart.js with plugins for enhanced chart functionality
- Tailwind CSS for styling
- Black-Scholes model implementation for options pricing (standard and robust versions)
- PostgreSQL for database storage
- Firebase Firestore for trade data storage
- Express.js for server-side API

## Development Setup:
- Node.js
- npm or yarn
- Create React App for project structure
- PostgreSQL database
- .env file for environment variables

## Technical Constraints:
- Browser compatibility with modern browsers
- Responsive design for different screen sizes
- Performance considerations for chart rendering
- Accuracy of options pricing calculations
- Robust handling of edge cases in Black-Scholes calculations
- Database connection reliability
- Date handling without timezone issues
- Zero stock price is a valid value and all components should work correctly with it
- Navigation between pages requires full page reloads for proper component re-rendering

## Dependencies:
- react: ^18.2.0
- react-dom: ^18.2.0
- react-router-dom: ^7.5.3 for page navigation
- recharts: For chart visualization
- chart.js: ^4.4.9 with plugins for enhanced chart functionality
- tailwindcss: For styling
- express: Web framework for Node.js
- pg: PostgreSQL client for Node.js
- firebase-admin: ^13.4.0 for Firestore database access
- dotenv: Environment variable management

## Tool Usage Patterns:
- Component composition for UI organization
- State management through React hooks and prop drilling
- Modular architecture with separation of concerns
- Parent components maintain state and pass down as props to children
- RESTful API endpoints for database operations
- Loading state pattern to prevent premature user interaction
- Environment variables for configuration
- Custom navigation implementation with forced page reloads for reliable routing
