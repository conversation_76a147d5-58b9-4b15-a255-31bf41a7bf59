/**
 * Node.js-specific holiday loader using CommonJS modules
 * This file is specifically designed for Node.js environments and server-side usage
 */

const fs = require('fs');
const path = require('path');
const { getHolidayDataPath } = require('./config.js');

/**
 * Load exchange holidays from XML file in Node.js environment
 * @returns {Date[]} Array of holiday dates
 */
function loadExchangeHolidaysFromXMLFile() {
  try {
    console.log('📁 Loading holidays from XML file (Node.js)...');
    
    // Use environment-configured path
    const xmlFilePath = getHolidayDataPath();
    
    if (!fs.existsSync(xmlFilePath)) {
      console.warn(`❌ XML file not found: ${xmlFilePath}`);
      return null;
    }
    
    const xmlContent = fs.readFileSync(xmlFilePath, 'utf8');
    console.log('✅ XML file loaded successfully');
    
    // Try using jsdom for proper DOM parsing
    let holidays = [];
    
    try {
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(xmlContent, { contentType: 'text/xml' });
      const document = dom.window.document;
      
      // Check for parsing errors
      const parseError = document.querySelector('parsererror');
      if (parseError) {
        throw new Error('XML parsing error: ' + parseError.textContent);
      }
      
      const rows = document.querySelectorAll('tr');
      console.log(`🔍 Found ${rows.length} table rows in XML`);
      
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
          // Get the first cell which should contain the date
          const dateText = cells[0].textContent.trim();
          
          // Extract date part (before any space)
          const datePart = dateText.split(' ')[0];
          
          // Parse date in DD/MM/YYYY format
          if (datePart && datePart.includes('/')) {
            try {
              const [day, month, year] = datePart.split('/');
              // Create date using local timezone, set to noon to avoid timezone issues
              const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0, 0);
              holidays.push(holidayDate);
            } catch (e) {
              console.warn('Could not parse holiday date:', datePart);
            }
          }
        }
      }
      
      console.log(`✅ Loaded ${holidays.length} holidays using jsdom`);
      
    } catch (jsdomError) {
      console.warn('❌ jsdom not available, using regex parsing');
      holidays = parseXMLWithRegex(xmlContent);
    }
    
    // Verify the critical date is included
    const oct7_2025 = holidays.find(h => 
      h.getFullYear() === 2025 && 
      h.getMonth() === 9 && 
      h.getDate() === 7
    );
    
    if (oct7_2025) {
      console.log('✅ 2025-10-07 found in holidays (Mid-Autumn Festival following day)');
    } else {
      console.warn('⚠️ 2025-10-07 NOT found in XML holidays');
    }
    
    return holidays;
    
  } catch (error) {
    console.error('❌ Error loading XML file:', error.message);
    return null;
  }
}

/**
 * Parse XML using regex patterns when jsdom is not available
 * @param {string} xmlContent - Raw XML content
 * @returns {Date[]} Array of holiday dates
 */
function parseXMLWithRegex(xmlContent) {
  console.log('🔧 Using regex-based XML parsing...');
  
  const holidays = [];
  
  // Extract date patterns using regex (DD/MM/YYYY format)
  const datePattern = /(\d{1,2}\/\d{1,2}\/\d{4})/g;
  const dates = xmlContent.match(datePattern) || [];
  
  console.log(`🔍 Found ${dates.length} date patterns in XML`);
  
  dates.forEach(dateStr => {
    try {
      const [day, month, year] = dateStr.split('/');
      const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0, 0);
      
      // Only add valid dates from 2024 onwards
      if (holidayDate.getFullYear() >= 2024) {
        holidays.push(holidayDate);
      }
    } catch (e) {
      console.warn('Could not parse date:', dateStr);
    }
  });
  
  // Remove duplicates
  const uniqueHolidays = holidays.filter((holiday, index, self) => 
    index === self.findIndex(h => h.getTime() === holiday.getTime())
  );
  
  console.log(`✅ Parsed ${uniqueHolidays.length} unique holidays using regex`);
  return uniqueHolidays;
}

/**
 * Get comprehensive 2025 HKEX holidays as fallback
 * @returns {Date[]} Array of holiday dates
 */
function get2025HKEXHolidays() {
  const holidays2025 = [
    new Date(2025, 0, 1),   // New Year's Day (1/1/2025)
    new Date(2025, 0, 29),  // Lunar New Year's Day (29/1/2025)
    new Date(2025, 0, 30),  // The second day of Lunar New Year (30/1/2025)
    new Date(2025, 0, 31),  // The third day of Lunar New Year (31/1/2025)
    new Date(2025, 3, 4),   // Ching Ming Festival (4/4/2025)
    new Date(2025, 3, 18),  // Good Friday (18/4/2025)
    new Date(2025, 3, 21),  // Easter Monday (21/4/2025)
    new Date(2025, 4, 1),   // Labour Day (1/5/2025)
    new Date(2025, 4, 5),   // The Birthday of the Buddha (5/5/2025)
    new Date(2025, 6, 1),   // HKSAR Establishment Day (1/7/2025)
    new Date(2025, 9, 1),   // National Day (1/10/2025)
    new Date(2025, 9, 7),   // The day following the Chinese Mid-Autumn Festival (7/10/2025) ⭐
    new Date(2025, 9, 29),  // Chung Yeung Festival (29/10/2025)
    new Date(2025, 11, 25)  // Christmas Day (25/12/2025)
  ];
  
  // Set all dates to noon to avoid timezone issues
  holidays2025.forEach(holiday => {
    holiday.setHours(12, 0, 0, 0);
  });
  
  return holidays2025;
}

/**
 * Universal holiday loader that tries XML first, then falls back to hardcoded holidays
 * @returns {Date[]} Array of holiday dates
 */
function loadHolidaysUniversal() {
  console.log('🌍 Universal holiday loader starting...');
  
  // Try loading from XML file first
  const xmlHolidays = loadExchangeHolidaysFromXMLFile();
  
  if (xmlHolidays && xmlHolidays.length > 0) {
    console.log(`✅ Successfully loaded ${xmlHolidays.length} holidays from XML`);
    return xmlHolidays;
  }
  
  // Fall back to hardcoded 2025 holidays
  console.log('🔄 XML loading failed, using hardcoded 2025 holidays');
  const fallbackHolidays = get2025HKEXHolidays();
  console.log(`✅ Using ${fallbackHolidays.length} hardcoded 2025 HKEX holidays`);
  
  return fallbackHolidays;
}

module.exports = {
  loadExchangeHolidaysFromXMLFile,
  parseXMLWithRegex,
  get2025HKEXHolidays,
  loadHolidaysUniversal
};