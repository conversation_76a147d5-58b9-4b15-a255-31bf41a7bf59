import './chart-styles.css';
import React, { useState, useRef, useEffect } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
// Simple ErrorBoundary for catching rendering errors in chart
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  static getDerivedStateFromError() {
    return { hasError: true };
  }
  componentDidCatch(error, info) {
    // You can log error here if needed
  }
  render() {
    if (this.state.hasError) {
      return this.props.fallback || null;
    }
    return this.props.children;
  }
}

/**
 * Time Decay Chart Component
 *
 * This component displays a chart showing how profit changes over time (days to expiry)
 * at a specific stock price.
 * Includes mouse wheel zoom support for the x-axis.
 */
const TimeDecayChart = ({
  positions,
  filteredPositions = [],
  selectedPrice,
  timeDecayChartData,
  volatility,
  riskFreeRate
}) => {
  // State to track zoom level and panning
  const [zoomLevel, setZoomLevel] = useState(1);
  const [zoomCenter, setZoomCenter] = useState(0.5); // 0 to 1, represents position in the data array

  // State for panning
  const [isPanning, setIsPanning] = useState(false);
  const [panStartX, setPanStartX] = useState(0);

  // Reference to the chart container
  const chartContainerRef = useRef(null);

  // Format date for display in YY-MM-DD format
  const formatDate = (dateStr) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);

    // Get year as 2-digit string
    const year = date.getFullYear().toString().slice(-2);

    // Get month and day with leading zeros
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    // Format as YY-MM-DD
    return `${year}-${month}-${day}`;
  };

  // Get visible data based on zoom level and center
  const getVisibleData = () => {
    if (!timeDecayChartData || !timeDecayChartData(selectedPrice)) {
      return [];
    }

    const fullData = timeDecayChartData(selectedPrice);

    // If no zoom or no data, return all data
    if (zoomLevel <= 1 || fullData.length === 0) {
      return fullData;
    }

    // Calculate visible window
    const windowSize = Math.max(2, Math.floor(fullData.length / zoomLevel));
    const halfWindow = windowSize / 2;

    // Calculate center index
    const centerIndex = Math.floor(zoomCenter * (fullData.length - 1));

    // Calculate start and end indices
    let startIndex = Math.max(0, centerIndex - Math.floor(halfWindow));
    let endIndex = Math.min(fullData.length - 1, centerIndex + Math.ceil(halfWindow));

    // Ensure we have at least windowSize elements
    if (endIndex - startIndex + 1 < windowSize) {
      if (startIndex === 0) {
        endIndex = Math.min(fullData.length - 1, startIndex + windowSize - 1);
      } else {
        startIndex = Math.max(0, endIndex - windowSize + 1);
      }
    }

    // Return the visible slice of data
    return fullData.slice(startIndex, endIndex + 1);
  };

  // Reset zoom to show all data
  const resetZoom = () => {
    setZoomLevel(1);
    setZoomCenter(0.5);
  };

  // Add event listeners when component mounts
  useEffect(() => {
    const chartContainer = chartContainerRef.current;
    if (!chartContainer) return;

    // Wheel event handler with passive: false to allow preventDefault
    const handleWheel = (e) => {
      e.preventDefault();
      e.stopPropagation();

      if (!timeDecayChartData || !timeDecayChartData(selectedPrice) || timeDecayChartData(selectedPrice).length === 0) {
        return;
      }

      // Calculate new zoom level - use a larger factor for more noticeable zoom
      const zoomFactor = e.deltaY > 0 ? 0.8 : 1.25; // Zoom out or in with stronger effect
      const newZoomLevel = Math.max(1, Math.min(10, zoomLevel * zoomFactor));

      // If we're zooming out to no zoom, just reset
      if (newZoomLevel <= 1) {
        setZoomLevel(1);
        setZoomCenter(0.5);
        return;
      }

      // Get mouse position relative to chart container
      const rect = chartContainer.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const containerWidth = rect.width;

      // Calculate new zoom center based on mouse position
      // If mouse is at left edge, center = 0, if at right edge, center = 1
      const newCenter = Math.max(0, Math.min(1, mouseX / containerWidth));

      // Update state
      setZoomCenter(newCenter);
      setZoomLevel(newZoomLevel);

      // Log for debugging
      console.log(`Zoom: level=${newZoomLevel.toFixed(2)}, center=${newCenter.toFixed(2)}`);

      return false; // Prevent default behavior
    };

    // Mouse down handler for panning
    const handleMouseDown = (e) => {
      // Only enable panning when zoomed in
      if (zoomLevel > 1) {
        setIsPanning(true);
        setPanStartX(e.clientX);

        // Change cursor to grabbing
        chartContainer.style.cursor = 'grabbing';
        document.body.style.userSelect = 'none'; // Prevent text selection during panning
      }
    };

    // Mouse move handler for panning
    const handleMouseMove = (e) => {
      if (isPanning && zoomLevel > 1) {
        const deltaX = e.clientX - panStartX;
        const containerWidth = chartContainer.getBoundingClientRect().width;

        // Calculate pan amount as a percentage of container width
        const panAmount = deltaX / containerWidth;

        // Adjust zoom center based on pan direction and amount
        // Negative deltaX (moving left) increases center value (moves chart right)
        const newCenter = Math.max(0, Math.min(1, zoomCenter - panAmount * 0.05));

        setZoomCenter(newCenter);
        setPanStartX(e.clientX);
      }
    };

    // Mouse up handler to end panning
    const handleMouseUp = () => {
      if (isPanning) {
        setIsPanning(false);
        chartContainer.style.cursor = 'grab';
        document.body.style.userSelect = '';
      }
    };

    // Mouse leave handler to end panning if cursor leaves the chart
    const handleMouseLeave = () => {
      if (isPanning) {
        setIsPanning(false);
        chartContainer.style.cursor = 'grab';
        document.body.style.userSelect = '';
      }
    };

    // Set initial cursor style
    if (zoomLevel > 1) {
      chartContainer.style.cursor = 'grab';
    }

    // Add event listeners
    chartContainer.addEventListener('wheel', handleWheel, { passive: false });
    chartContainer.addEventListener('mousewheel', handleWheel, { passive: false });
    chartContainer.addEventListener('DOMMouseScroll', handleWheel, { passive: false });

    chartContainer.addEventListener('mousedown', handleMouseDown);
    chartContainer.addEventListener('mousemove', handleMouseMove);
    chartContainer.addEventListener('mouseup', handleMouseUp);
    chartContainer.addEventListener('mouseleave', handleMouseLeave);

    // Global mouse up to handle case where mouse is released outside the chart
    document.addEventListener('mouseup', handleMouseUp);

    // Remove event listeners on cleanup
    return () => {
      chartContainer.removeEventListener('wheel', handleWheel);
      chartContainer.removeEventListener('mousewheel', handleWheel);
      chartContainer.removeEventListener('DOMMouseScroll', handleWheel);

      chartContainer.removeEventListener('mousedown', handleMouseDown);
      chartContainer.removeEventListener('mousemove', handleMouseMove);
      chartContainer.removeEventListener('mouseup', handleMouseUp);
      chartContainer.removeEventListener('mouseleave', handleMouseLeave);

      document.removeEventListener('mouseup', handleMouseUp);

      // Reset cursor and user-select
      document.body.style.userSelect = '';
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [zoomLevel, zoomCenter, isPanning, panStartX, timeDecayChartData, selectedPrice]);

  return (
    <>
      <h2 className="text-lg font-bold mb-3 text-gray-800">
        Time Decay Analysis
        <span className="ml-2 text-xs text-gray-500">
          @{selectedPrice ? selectedPrice.toLocaleString() : '0'}, IV: {volatility || 15}%, RFR: {riskFreeRate || 0}% ({filteredPositions.length > 0 ? filteredPositions.length : positions.length} positions)
        </span>
      </h2>
      <p className="mb-2 text-gray-600 text-xs">
        This chart shows how the profit/loss of your strategy changes over time at a specific stock price.
      </p>

      {/* Chart */}
      <div className="w-full h-80 border border-gray-200 rounded-lg p-4 bg-white relative no-scroll">
        {positions.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500 text-xs">Add positions to see the time decay analysis</p>
          </div>
        ) : timeDecayChartData(selectedPrice).length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500 text-xs">No positions match the current filter</p>
          </div>
        ) : (
          <ErrorBoundary fallback={
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500 text-xs">Chart rendering error. Please try refreshing the page.</p>
            </div>
          }>
            <div className="absolute top-2 right-2 z-10 flex space-x-2" style={{ display: zoomLevel > 1 ? 'block' : 'none' }}>
              <button
                onClick={resetZoom}
                className="px-2 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 text-xs rounded border border-gray-300"
                title="Reset zoom"
              >
                Reset Zoom
              </button>
            </div>
            <div className="absolute top-2 left-2 z-10" style={{ display: zoomLevel > 1 ? 'block' : 'none' }}>
              <span className="text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded">
                Drag to pan
              </span>
            </div>
            <div
              ref={chartContainerRef}
              style={{
                width: '100%',
                height: '100%',
                overflow: 'hidden',
                touchAction: 'none' // Prevent touch scrolling
              }}
              className={`chart-container ${zoomLevel > 1 ? 'can-pan' : ''} ${isPanning ? 'panning' : ''}`}
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={getVisibleData()}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  onMouseMove={(e) => {
                    if (e && e.activeTooltipIndex !== undefined) {
                      // This enables more responsive tooltips
                      // The actual handling is done by Recharts
                    }
                  }}
                >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  label={{ value: 'Date', position: 'insideBottom', offset: -5, fontSize: '10px' }}
                  tick={{ fontSize: '10px' }}
                  style={{ fontSize: '10px' }}
                  tickFormatter={formatDate}
                  type="category"
                />
              <YAxis
                label={{ value: 'PROFIT / LOSS', angle: -90, position: 'insideLeft', fontSize: '10px' }}
                tick={{ fontSize: '10px' }}
                style={{ fontSize: '10px' }}
              />
              <Tooltip
                formatter={(value, name) => ['$' + value, name]}
                labelFormatter={formatDate}
                labelStyle={{ fontSize: '10px' }}
                itemStyle={{ fontSize: '10px' }}
                wrapperStyle={{
                  fontSize: '10px',
                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: '4px',
                  padding: '8px',
                  color: 'white'
                }}
                cursor={{ stroke: 'rgba(100, 100, 100, 0.5)', strokeWidth: 1, strokeDasharray: '5 5' }}
                isAnimationActive={false}
              />
              <Legend
                wrapperStyle={{ fontSize: '10px' }}
                iconSize={8}
                payload={getVisibleData().length > 0 ?
                  // Create a custom payload that only includes positions with data
                  positions
                    .filter(position => {
                      // Check if this position has data in the current view
                      const dataPoint = getVisibleData()[0]; // Check first data point
                      return dataPoint && dataPoint[`position${position.id}`] !== undefined;
                    })
                    .map((position, index) => ({
                      id: position.id,
                      type: 'line',
                      value: `${position.type} ${position.strike} (${position.quantity > 0 ? 'Long' : 'Short'})`,
                      color: getPositionColor(index)
                    }))
                    .concat([{
                      id: 'total',
                      type: 'line',
                      value: 'Total P/L',
                      color: '#000000'
                    }])
                  : []
                }
              />
              {positions.map((position, index) => (
                <Line
                  key={position.id}
                  name={`${position.type} ${position.strike} (${position.quantity > 0 ? 'Long' : 'Short'})`}
                  type="monotone"
                  dataKey={`position${position.id}`}
                  stroke={getPositionColor(index)}
                  activeDot={{
                    r: 6,
                    stroke: '#ffffff',
                    strokeWidth: 2,
                    fill: getPositionColor(index)
                  }}
                  dot={false}
                  isAnimationActive={false}
                />
              ))}
              <Line
                name="Total P/L"
                type="monotone"
                dataKey="total"
                stroke="#000000"
                strokeWidth={2}
                activeDot={{
                  r: 6,
                  stroke: '#ffffff',
                  strokeWidth: 2,
                  fill: '#000000'
                }}
                dot={false}
                isAnimationActive={false}
              />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </ErrorBoundary>
        )}
      </div>
    </>
  );
};

// Helper function to get a color for each position
function getPositionColor(index) {
  const colors = [
    '#3b82f6', // blue
    '#f97316', // orange
    '#10b981', // green
    '#ef4444', // red
    '#8b5cf6', // purple
    '#f59e0b', // amber
    '#14b8a6', // teal
    '#ec4899', // pink
  ];

  return colors[index % colors.length];
}

export default TimeDecayChart;
