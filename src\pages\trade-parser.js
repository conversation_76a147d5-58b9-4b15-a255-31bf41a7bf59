import React from 'react';
import TradeParserClient from '../components/trade-parser/trade-parser-client';

/**
 * Trade Parser Page
 * 
 * This page allows users to paste their trade holdings, parse them,
 * preview the results, and upload them to Firestore.
 */
const TradeParserPage = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Trade Parser</h1>
      <p className="text-gray-600 mb-6">
        Paste your trade holdings from your trading platform, parse them into a structured format,
        and upload them to the database.
      </p>
      
      <TradeParserClient />
    </div>
  );
};

export default TradeParserPage;
