import React from 'react';
import AnalysisParameters from '../analysis-parameters';
import StrategyPriceChart from './StrategyPriceChart';
import StrategyTimeDecayChart from './StrategyTimeDecayChart';
import StrategyVolatilityChart from './StrategyVolatilityChart';

/**
 * Analysis Tab Component
 * 
 * Provides P&L analysis capabilities with simulation parameters and
 * three analysis views (Price, Time Decay, Volatility).
 */
const AnalysisTab = ({ strategyStore }) => {
  const {
    trades,
    simulationParams,
    updateSimulationParams,
    pnlResults,
    activeAnalysisView,
    setActiveAnalysisView,
    isCalculating,
    calculatePnL,
    getTradesSummary,
    strategyMetadata
  } = strategyStore();

  const tradesSummary = getTradesSummary();

  const analysisViews = [
    {
      id: 'price',
      name: 'Price Analysis',
      description: 'P&L at various stock price levels',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      )
    },
    {
      id: 'time',
      name: 'Time Decay',
      description: 'P&L over time to expiration',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      id: 'volatility',
      name: 'Volatility Analysis',
      description: 'P&L at various volatility levels',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l3-3 3 3v13M9 19h6M9 19l-2-2M15 19l2-2" />
        </svg>
      )
    }
  ];

  // Handle parameter changes
  const handleParameterChange = (paramName, value) => {
    updateSimulationParams({ [paramName]: value });
  };

  // Handle manual calculation trigger
  const handleCalculate = () => {
    calculatePnL();
  };

  // Get analysis data based on active view
  const getAnalysisData = () => {
    if (!pnlResults.calculatedAt) return [];

    switch (activeAnalysisView) {
      case 'price':
        return pnlResults.priceAnalysis || [];
      case 'time':
        return pnlResults.timeDecayAnalysis || [];
      case 'volatility':
        return pnlResults.volatilityAnalysis || [];
      default:
        return [];
    }
  };

  // Get maximum profit from current analysis
  const getMaxProfit = () => {
    const data = getAnalysisData();
    if (data.length === 0) return '0.00';
    const maxPnL = Math.max(...data.map(row => row.totalPnL));
    return maxPnL.toFixed(2);
  };

  // Get maximum loss from current analysis
  const getMaxLoss = () => {
    const data = getAnalysisData();
    if (data.length === 0) return '0.00';
    const minPnL = Math.min(...data.map(row => row.totalPnL));
    return minPnL.toFixed(2);
  };

  // Get breakeven count
  const getBreakevenCount = () => {
    const data = getAnalysisData();
    if (activeAnalysisView === 'price') {
      return data.filter(row => row.breakeven).length;
    }
    return data.filter(row => Math.abs(row.totalPnL) < 1).length;
  };

  if (trades.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-8">
        <svg className="h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No trades to analyze</h3>
        <p className="text-gray-500">
          Add trades to this strategy to enable P&L analysis.
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Analysis Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">P&L Analysis</h3>
            <p className="text-sm text-gray-500">
              Analyze strategy performance across different market scenarios
            </p>
          </div>
          
          <button
            onClick={handleCalculate}
            disabled={isCalculating}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 text-sm"
          >
            {isCalculating ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Calculating...
              </div>
            ) : (
              'Calculate P&L'
            )}
          </button>
        </div>

        {/* Strategy Summary */}
        <div className="grid grid-cols-4 gap-4 text-sm">
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-gray-500">Total Trades</div>
            <div className="font-medium">{tradesSummary.totalTrades}</div>
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-gray-500">Total Premium</div>
            <div className="font-medium">${tradesSummary.totalPremium.toFixed(2)}</div>
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-gray-500">Net Delta</div>
            <div className="font-medium">{tradesSummary.netDelta.toFixed(2)}</div>
          </div>
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-gray-500">Net Theta</div>
            <div className="font-medium">{tradesSummary.netTheta.toFixed(2)}</div>
          </div>
        </div>
      </div>

      {/* Simulation Parameters */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Simulation Parameters</h4>
        
        {/* Reuse existing analysis parameters component */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Stock Price
            </label>
            <input
              type="text"
              value={simulationParams.stockPrice}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '' || !isNaN(parseFloat(value))) {
                  handleParameterChange('stockPrice', value === '' ? 0 : parseFloat(value));
                }
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="e.g., 100.00"
            />
          </div>

          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Volatility (%)
            </label>
            <input
              type="text"
              value={simulationParams.volatility}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '' || !isNaN(parseFloat(value))) {
                  handleParameterChange('volatility', value === '' ? 0 : parseFloat(value));
                }
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="e.g., 25.0"
            />
          </div>

          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Risk-Free Rate (%)
            </label>
            <input
              type="text"
              value={simulationParams.riskFreeRate}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '' || !isNaN(parseFloat(value))) {
                  handleParameterChange('riskFreeRate', value === '' ? 0 : parseFloat(value));
                }
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="e.g., 2.5"
            />
          </div>
          
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Target Date
            </label>
            <input
              type="date"
              value={simulationParams.targetDate ? simulationParams.targetDate.toISOString().split('T')[0] : ''}
              onChange={(e) => handleParameterChange('targetDate', new Date(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
        </div>
      </div>

      {/* Analysis Views Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-4" aria-label="Analysis Views">
          {analysisViews.map((view) => (
            <button
              key={view.id}
              onClick={() => setActiveAnalysisView(view.id)}
              className={`
                flex items-center space-x-2 py-3 px-1 border-b-2 font-medium text-sm
                ${activeAnalysisView === view.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              {view.icon}
              <span>{view.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Analysis Content */}
      <div className="flex-1 overflow-auto p-4">
        {isCalculating ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="text-gray-600">Calculating P&L analysis...</span>
            </div>
          </div>
        ) : pnlResults.calculatedAt ? (
          <div className="space-y-4">
            {/* Analysis View Content */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h5 className="text-lg font-medium text-gray-900 mb-2">
                {analysisViews.find(v => v.id === activeAnalysisView)?.name}
              </h5>
              <p className="text-sm text-gray-500 mb-4">
                {analysisViews.find(v => v.id === activeAnalysisView)?.description}
              </p>
              
              {/* P&L Analysis Results */}
              <div className="space-y-6">
                {/* Chart Section */}
                <div className="mb-6">
                  {activeAnalysisView === 'price' && (
                    <StrategyPriceChart
                      priceAnalysisData={pnlResults.priceAnalysis}
                      simulationParams={simulationParams}
                      strategyName={strategyMetadata?.name}
                    />
                  )}
                  {activeAnalysisView === 'time' && (
                    <StrategyTimeDecayChart
                      timeDecayData={pnlResults.timeDecayAnalysis}
                      simulationParams={simulationParams}
                      strategyName={strategyMetadata?.name}
                    />
                  )}
                  {activeAnalysisView === 'volatility' && (
                    <StrategyVolatilityChart
                      volatilityAnalysisData={pnlResults.volatilityAnalysis}
                      simulationParams={simulationParams}
                      strategyName={strategyMetadata?.name}
                    />
                  )}
                </div>

                {/* Analysis Data Table */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        {activeAnalysisView === 'price' && (
                          <>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Stock Price
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Total P&L
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Market Value
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Status
                            </th>
                          </>
                        )}
                        {activeAnalysisView === 'time' && (
                          <>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Days to Expiry
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Total P&L
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Market Value
                            </th>
                          </>
                        )}
                        {activeAnalysisView === 'volatility' && (
                          <>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Volatility (%)
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Total P&L
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Market Value
                            </th>
                          </>
                        )}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getAnalysisData().map((row, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          {activeAnalysisView === 'price' && (
                            <>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                ${row.stockPrice}
                              </td>
                              <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                                row.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                ${row.totalPnL}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${row.totalMarketValue}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {row.breakeven ? '🎯 Breakeven' : row.totalPnL >= 0 ? '✅ Profit' : '❌ Loss'}
                              </td>
                            </>
                          )}
                          {activeAnalysisView === 'time' && (
                            <>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {row.daysToExpiry}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {row.date}
                              </td>
                              <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                                row.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                ${row.totalPnL}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${row.totalMarketValue}
                              </td>
                            </>
                          )}
                          {activeAnalysisView === 'volatility' && (
                            <>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {row.volatility}%
                              </td>
                              <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                                row.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'
                              }`}>
                                ${row.totalPnL}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${row.totalMarketValue}
                              </td>
                            </>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Summary Statistics */}
                <div className="grid grid-cols-3 gap-4 mt-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-sm text-blue-600 font-medium">Max Profit</div>
                    <div className="text-lg font-bold text-blue-900">
                      ${getMaxProfit()}
                    </div>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg">
                    <div className="text-sm text-red-600 font-medium">Max Loss</div>
                    <div className="text-lg font-bold text-red-900">
                      ${getMaxLoss()}
                    </div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-sm text-green-600 font-medium">Breakeven Points</div>
                    <div className="text-lg font-bold text-green-900">
                      {getBreakevenCount()}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Calculation Info */}
            <div className="text-xs text-gray-500">
              Last calculated: {pnlResults.calculatedAt.toLocaleString()}
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-center">
            <div>
              <svg className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to analyze</h3>
              <p className="text-gray-500 mb-4">
                Click "Calculate P&L" to generate analysis results.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalysisTab;
