<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A Systematic Framework for Trading HKEX Index Options</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .flow-arrow {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: #97C4E1;
            padding: 1rem 0;
        }
        .flow-arrow-horizontal::after {
            content: '\279C';
            transform: rotate(0deg);
        }
        .flow-arrow-vertical::after {
             content: '\279C';
            transform: rotate(90deg);
        }
    </style>
</head>
<body class="text-[#4A4A4A]">

    <div class="container mx-auto p-4 md:p-8">
        
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-extrabold text-[#00449E] mb-2">The Systematic Trader's Blueprint</h1>
            <p class="text-lg md:text-xl text-[#0063B2]">A Visual Guide to Rules-Based Option Trading on the HKEX</p>
        </header>

        <section id="pillars" class="mb-16">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-[#00449E]">The Three Pillars of Strategy Formulation</h2>
                <p class="mt-2 max-w-3xl mx-auto">A successful trading system is built on a foundation of objective analysis and disciplined execution. This framework rests on three non-negotiable pillars that guide every decision, removing emotion and subjectivity.</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div class="bg-white rounded-lg shadow-lg p-6 transform hover:scale-105 transition-transform duration-300">
                    <div class="text-5xl mb-4 text-[#0063B2]">🧭</div>
                    <h3 class="text-2xl font-bold text-[#00449E] mb-2">Pillar 1: Market Regime</h3>
                    <p>First, we objectively identify the market's trend and volatility. This determines which strategies are viable and which should be avoided.</p>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6 transform hover:scale-105 transition-transform duration-300">
                    <div class="text-5xl mb-4 text-[#0063B2]">🎯</div>
                    <h3 class="text-2xl font-bold text-[#00449E] mb-2">Pillar 2: Entry & Exit Signals</h3>
                    <p>Next, we define precise, data-driven rules for when to enter a trade and, more importantly, when to exit for profit or to manage risk.</p>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6 transform hover:scale-105 transition-transform duration-300">
                    <div class="text-5xl mb-4 text-[#0063B2]">🛠️</div>
                    <h3 class="text-2xl font-bold text-[#00449E] mb-2">Pillar 3: Optimal Construction</h3>
                    <p>Finally, we use quantitative metrics like Delta to construct trades with a consistent probabilistic edge and a favorable risk/reward profile.</p>
                </div>
            </div>
        </section>

        <section id="market-regime" class="mb-16">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-[#00449E]">Pillar 1: Reading the Market's Signals</h2>
                <p class="mt-2 max-w-3xl mx-auto">Before placing any trade, we classify the market into one of several regimes based on two key metrics: Trend and Volatility.</p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-2xl font-bold text-[#00449E] mb-2">Trend Analysis: The Dual SMA System</h3>
                    <p class="mb-4">We use the 50-day and 200-day Simple Moving Averages (SMAs) to define the primary trend. The relationship between the price, the 50 SMA, and the 200 SMA determines if the market is Bullish, Bearish, or Neutral.</p>
                    <div class="chart-container">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
                 <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-2xl font-bold text-[#00449E] mb-2">Volatility Analysis: IV Percentile</h3>
                    <p class="mb-4">We sell options when they are expensive and buy them when they are cheap. IV Percentile tells us the percentage of days in the past year that Implied Volatility was lower than the current level, giving a true sense of its relative value.</p>
                    <div class="chart-container">
                        <canvas id="ivPercentileChart"></canvas>
                    </div>
                </div>
            </div>
        </section>
        
        <section id="execution" class="mb-16">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-[#00449E]">Pillars 2 & 3: Precision Execution & Construction</h2>
                <p class="mt-2 max-w-3xl mx-auto">With the market regime identified, we apply a strict ruleset for entering, managing, and constructing trades.</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold text-[#00449E] mb-2">Entry: Timing with RSI</h3>
                    <p class="mb-4 text-sm">We use the Relative Strength Index (RSI) to time entries, entering on pullbacks in trends or at extremes in neutral markets.</p>
                    <div class="chart-container h-64 max-h-64">
                        <canvas id="rsiChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold text-[#00449E] mb-2">Management: Profit & Loss</h3>
                     <p class="mb-4 text-sm">Disciplined exits are key. We take profits early and cut losses systematically to protect capital.</p>
                     <div class="flex flex-col sm:flex-row justify-around items-center h-full">
                        <div class="text-center">
                            <p class="font-semibold">Profit Target</p>
                            <div class="w-32 h-32 mx-auto"><canvas id="profitTargetChart"></canvas></div>
                             <p class="text-xs mt-1">50% of Max Credit</p>
                        </div>
                        <div class="text-center mt-4 sm:mt-0">
                            <p class="font-semibold">Stop Loss</p>
                            <div class="w-32 h-32 mx-auto"><canvas id="stopLossChart"></canvas></div>
                             <p class="text-xs mt-1">200% of Credit Rec'd</p>
                        </div>
                     </div>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-xl font-bold text-[#00449E] mb-2">Construction: Strike Selection</h3>
                    <p class="mb-4 text-sm">We use Delta as a proxy for probability, consistently selecting strikes to achieve a statistical edge.</p>
                    <div class="h-full flex flex-col justify-center items-center space-y-4">
                        <div class="text-center p-3 bg-[#e7f0f7] rounded-lg w-full">
                            <p class="text-sm font-semibold text-[#0063B2]">Short Strike Delta</p>
                            <p class="text-3xl font-bold text-[#00449E]">15-30</p>
                            <p class="text-xs">For high-probability credit strategies.</p>
                        </div>
                         <div class="text-center p-3 bg-[#e7f0f7] rounded-lg w-full">
                            <p class="text-sm font-semibold text-[#0063B2]">Credit Target Rule</p>
                            <p class="text-3xl font-bold text-[#00449E]">≈ 1/3</p>
                            <p class="text-xs">Net credit vs. spread width.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="matrix" class="mb-16">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-[#00449E]">The Strategy Selection Matrix</h2>
                <p class="mt-2 max-w-3xl mx-auto">This flowchart is the core of the system. It connects our market analysis directly to an actionable, optimal strategy for the current environment.</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-xl p-6 md:p-8">
                <div class="text-center mb-6">
                     <div class="inline-block bg-[#00449E] text-white font-bold py-2 px-6 rounded-full shadow-md">START: Identify Market Regime</div>
                </div>

                <div class="hidden md:flex justify-center items-center">
                    <div class="flow-arrow flow-arrow-vertical"></div>
                </div>
                <div class="flex md:hidden justify-center items-center">
                    <div class="flow-arrow flow-arrow-vertical"></div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8 items-stretch">
                    <div class="bg-[#f0f4f8] rounded-lg p-4 text-center shadow">
                        <h4 class="font-bold text-[#0063B2]">1. Trend Analysis</h4>
                        <p class="text-sm">Is the trend Bullish, Bearish, or Neutral?</p>
                    </div>

                    <div class="hidden md:flex justify-center items-center flow-arrow flow-arrow-horizontal"></div>
                    <div class="flex md:hidden justify-center items-center flow-arrow flow-arrow-vertical"></div>

                    <div class="bg-[#f0f4f8] rounded-lg p-4 text-center shadow">
                        <h4 class="font-bold text-[#0063B2]">2. Volatility Analysis</h4>
                        <p class="text-sm">Is IV Percentile High (>70) or Low (<30)?</p>
                    </div>
                </div>

                <div class="hidden md:flex justify-center items-center">
                    <div class="flow-arrow flow-arrow-vertical"></div>
                </div>
                 <div class="flex md:hidden justify-center items-center">
                    <div class="flow-arrow flow-arrow-vertical"></div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8">
                    
                    <div class="border-2 border-[#0063B2] rounded-lg p-4">
                        <h4 class="font-bold text-center text-[#00449E]">High IV (>70)</h4>
                        <hr class="my-2 border-[#97C4E1]">
                        <ul class="space-y-2 text-sm">
                            <li><span class="font-semibold">Bull Trend:</span> Sell Bull Put Spread</li>
                            <li><span class="font-semibold">Neutral Trend:</span> Sell Iron Condor</li>
                            <li><span class="font-semibold">Bear Trend:</span> Sell Bear Call Spread</li>
                        </ul>
                    </div>

                    <div class="border-2 border-gray-300 rounded-lg p-4">
                        <h4 class="font-bold text-center text-gray-500">Medium IV (30-70)</h4>
                         <hr class="my-2 border-gray-300">
                        <div class="text-center text-gray-500 text-sm h-full flex items-center justify-center">
                            <p>Monitor market and wait for a clearer signal. No trade is a high-probability position.</p>
                        </div>
                    </div>

                     <div class="border-2 border-[#0063B2] rounded-lg p-4">
                        <h4 class="font-bold text-center text-[#00449E]">Low IV (<30)</h4>
                         <hr class="my-2 border-[#97C4E1]">
                        <ul class="space-y-2 text-sm">
                            <li><span class="font-semibold">Bull Trend:</span> Buy Bull Call Debit Spread</li>
                            <li><span class="font-semibold">Neutral Trend:</span> Buy Long Calendar Spread</li>
                            <li><span class="font-semibold">Bear Trend:</span> Buy Bear Put Debit Spread</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="strategy-details" class="mb-16">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-[#00449E]">A Closer Look at the Core Strategies</h2>
                <p class="mt-2 max-w-3xl mx-auto">Each strategy has a unique risk/reward profile, making it suitable for a specific market condition.</p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-2xl font-bold text-[#00449E] mb-2">Strategy I: Iron Condor</h3>
                    <p class="mb-4">The ideal strategy for neutral, range-bound markets with high IV. It profits from time decay as long as the index stays within a defined range.</p>
                    <div class="chart-container">
                        <canvas id="ironCondorChart"></canvas>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-2xl font-bold text-[#00449E] mb-2">Strategy II: Directional Credit Spreads</h3>
                    <p class="mb-4">Used in trending markets with high IV. A Bull Put Spread profits in an uptrend, while a Bear Call Spread profits in a downtrend. Both have a high probability of success.</p>
                    <div class="chart-container">
                        <canvas id="creditSpreadChart"></canvas>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-2xl font-bold text-[#00449E] mb-2">Strategy III: Long Calendar Spread</h3>
                    <p class="mb-4">Deployed in neutral markets with low IV. This trade profits from the passage of time and, crucially, an expansion in implied volatility.</p>
                    <div class="chart-container">
                        <canvas id="calendarSpreadChart"></canvas>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6 flex flex-col">
                    <h3 class="text-2xl font-bold text-[#00449E] mb-2">Advanced Applications</h3>
                    <p class="mb-4">These strategies cover other market conditions, completing the portfolio.</p>
                    <div class="flex-grow space-y-4 flex flex-col justify-center">
                        <div class="bg-[#f0f4f8] rounded-lg p-4 shadow-inner">
                            <h4 class="font-bold text-[#0063B2]">Strategy IV: Trend-Following Debit Spread</h4>
                            <p class="text-sm">For trending markets with LOW IV. A directional bet with defined risk, profiting from price movement rather than time decay.</p>
                        </div>
                        <div class="bg-[#f0f4f8] rounded-lg p-4 shadow-inner">
                            <h4 class="font-bold text-[#0063B2]">Strategy V: The Weekly "Theta Engine"</h4>
                            <p class="text-sm">An aggressive, high-frequency income strategy using weekly options to maximize theta decay. Requires smaller size and diligent management.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="index-comparison">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-[#00449E]">Index-Specific Considerations</h2>
                <p class="mt-2 max-w-3xl mx-auto">While the framework is universal, its application must be nuanced for each of the major HKEX indices due to their unique characteristics.</p>
            </div>
            <div class="bg-white rounded-lg shadow-lg p-6 overflow-x-auto">
                <table class="w-full text-left border-collapse">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 bg-[#00449E] text-white font-bold uppercase text-sm border-b border-gray-200">Index</th>
                            <th class="py-2 px-4 bg-[#00449E] text-white font-bold uppercase text-sm border-b border-gray-200">Primary Characteristic</th>
                            <th class="py-2 px-4 bg-[#00449E] text-white font-bold uppercase text-sm border-b border-gray-200">Volatility Profile</th>
                            <th class="py-2 px-4 bg-[#00449E] text-white font-bold uppercase text-sm border-b border-gray-200">Trading Consideration</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="hover:bg-[#f0f4f8]">
                            <td class="py-2 px-4 border-b border-gray-200 font-semibold text-[#0063B2]">Hang Seng Index (HSI)</td>
                            <td class="py-2 px-4 border-b border-gray-200">Broad market benchmark</td>
                            <td class="py-2 px-4 border-b border-gray-200">Relatively Lower</td>
                            <td class="py-2 px-4 border-b border-gray-200">Suitable for all core strategies; may allow for narrower spreads.</td>
                        </tr>
                        <tr class="hover:bg-[#f0f4f8]">
                            <td class="py-2 px-4 border-b border-gray-200 font-semibold text-[#0063B2]">HS China Ent. Index (HHI)</td>
                            <td class="py-2 px-4 border-b border-gray-200">Mainland China exposure</td>
                            <td class="py-2 px-4 border-b border-gray-200">Moderate; sensitive to policy news</td>
                            <td class="py-2 px-4 border-b border-gray-200">Be aware of mainland-specific event risk and news cycles.</td>
                        </tr>
                        <tr class="hover:bg-[#f0f4f8]">
                            <td class="py-2 px-4 border-b border-gray-200 font-semibold text-[#0063B2]">Hang Seng TECH Index (HTI)</td>
                            <td class="py-2 px-4 border-b border-gray-200">High-beta technology focus</td>
                            <td class="py-2 px-4 border-b border-gray-200">Inherently Higher</td>
                            <td class="py-2 px-4 border-b border-gray-200">Requires wider spreads and/or lower delta strikes to manage risk.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <footer class="text-center mt-16 text-gray-500 text-sm">
            <p>This infographic visualizes the framework detailed in the "Systematic Framework for Trading HKEX Index Options" report.</p>
            <p>All trading involves risk. This information is for educational purposes only and not financial advice.</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {

            const brandColors = {
                darkBlue: '#00449E',
                mediumBlue: '#0063B2',
                lightBlue: '#97C4E1',
                white: '#FFFFFF',
                darkGray: '#4A4A4A',
                success: '#28a745',
                danger: '#dc3545',
                neutral: '#ffc107'
            };

            const globalChartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: brandColors.darkGray,
                            font: {
                                family: 'Inter',
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            title: function(tooltipItems) {
                                const item = tooltipItems[0];
                                let label = item.chart.data.labels[item.dataIndex];
                                if (Array.isArray(label)) {
                                  return label.join(' ');
                                } else {
                                  return label;
                                }
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: brandColors.darkGray,
                            font: { family: 'Inter' }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    y: {
                        ticks: {
                            color: brandColors.darkGray,
                            font: { family: 'Inter' }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            };
            
            function wrapLabel(str, maxWidth) {
                if (str.length <= maxWidth) return str;
                const words = str.split(' ');
                let lines = [];
                let currentLine = '';
                for (const word of words) {
                    if ((currentLine + word).length > maxWidth && currentLine.length > 0) {
                        lines.push(currentLine.trim());
                        currentLine = '';
                    }
                    currentLine += word + ' ';
                }
                lines.push(currentLine.trim());
                return lines;
            }

            new Chart(document.getElementById('trendChart'), {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],
                    datasets: [
                        { label: 'Index Price', data: [100, 105, 115, 120, 110, 112, 118, 125], borderColor: brandColors.darkBlue, tension: 0.1, fill: false },
                        { label: '50-Day SMA', data: [null, 102, 108, 115, 115, 113, 114, 118], borderColor: brandColors.mediumBlue, tension: 0.1, borderDash: [5, 5] },
                        { label: '200-Day SMA', data: [105, 106, 107, 108, 109, 110, 111, 112], borderColor: brandColors.lightBlue, tension: 0.1, borderDash: [2, 2] }
                    ]
                },
                options: { ...globalChartOptions, plugins: { ...globalChartOptions.plugins, title: { display: true, text: 'Example Trend Identification', color: brandColors.darkGray } } }
            });

            new Chart(document.getElementById('ivPercentileChart'), {
                type: 'bar',
                data: {
                    labels: ['0-10', '10-20', '20-30', '30-40', '40-50', '50-60', '60-70', '70-80', '80-90', '90-100'],
                    datasets: [{
                        label: 'Days in IV Range',
                        data: [5, 15, 45, 60, 50, 35, 20, 15, 8, 2],
                        backgroundColor: brandColors.lightBlue,
                    }, {
                        type: 'line',
                        label: 'Current IV Percentile (82)',
                        data: [null, null, null, null, null, null, null, null, 15, null],
                        borderColor: brandColors.danger,
                        borderWidth: 3,
                        pointRadius: 8,
                        pointBackgroundColor: brandColors.danger,
                        fill: false
                    }]
                },
                options: { ...globalChartOptions, plugins: { ...globalChartOptions.plugins, title: { display: true, text: 'IV Percentile Distribution', color: brandColors.darkGray } } }
            });

            new Chart(document.getElementById('rsiChart'), {
                type: 'line',
                data: {
                    labels: ['T-10', 'T-9', 'T-8', 'T-7', 'T-6', 'T-5', 'T-4', 'T-3', 'T-2', 'T-1', 'Now'],
                    datasets: [{
                        label: '14-Day RSI',
                        data: [75, 72, 65, 60, 55, 48, 52, 58, 63, 59, 55],
                        borderColor: brandColors.mediumBlue,
                        backgroundColor: 'rgba(0, 99, 178, 0.1)',
                        fill: true
                    }]
                },
                options: { ...globalChartOptions,
                    plugins: { ...globalChartOptions.plugins, title: { display: true, text: 'RSI Entry Zones', color: brandColors.darkGray } },
                    scales: { y: { min: 0, max: 100, ticks: { stepSize: 10 } } },
                    annotation: {
                        annotations: {
                            box1: { type: 'box', yMin: 50, yMax: 60, backgroundColor: 'rgba(220, 53, 69, 0.1)', borderColor: brandColors.danger },
                            box2: { type: 'box', yMin: 40, yMax: 50, backgroundColor: 'rgba(40, 167, 69, 0.1)', borderColor: brandColors.success }
                        }
                    }
                }
            });

            new Chart(document.getElementById('profitTargetChart'), {
                type: 'doughnut',
                data: {
                    labels: ['Profit Taken', 'Remaining'],
                    datasets: [{ data: [50, 50], backgroundColor: [brandColors.success, brandColors.lightBlue], borderWidth: 2 }]
                },
                options: { responsive: true, maintainAspectRatio: true, plugins: { legend: { display: false } } }
            });

            new Chart(document.getElementById('stopLossChart'), {
                type: 'doughnut',
                data: {
                    labels: ['Credit Received', 'Loss Buffer'],
                    datasets: [{ data: [100, 100], backgroundColor: [brandColors.neutral, brandColors.danger], borderWidth: 2 }]
                },
                options: { responsive: true, maintainAspectRatio: true, plugins: { legend: { display: false } } }
            });

            const commonRiskProfileOptions = { ...globalChartOptions,
                 scales: {
                    x: { title: { display: true, text: 'Index Price at Expiration', color: brandColors.darkGray }, grid: { display: false } },
                    y: { title: { display: true, text: 'Profit / Loss', color: brandColors.darkGray }, ticks: { callback: (value) => '$' + value }, grid: { display: true } }
                }
            };
            
            new Chart(document.getElementById('ironCondorChart'), {
                type: 'line',
                data: {
                    labels: ['-3σ', 'Short Put', 'ATM', 'Short Call', '+3σ'],
                    datasets: [{
                        label: 'Profit/Loss',
                        data: [-100, -100, 50, -100, -100],
                        borderColor: brandColors.darkBlue,
                        backgroundColor: 'rgba(0, 99, 178, 0.1)',
                        fill: { target: { value: 0 }, above: 'rgba(40, 167, 69, 0.2)', below: 'rgba(220, 53, 69, 0.2)' },
                        tension: 0.1,
                    }]
                },
                options: { ...commonRiskProfileOptions, plugins: { ...commonRiskProfileOptions.plugins, title: { display: true, text: 'Iron Condor Risk Profile', color: brandColors.darkGray } } }
            });

            new Chart(document.getElementById('creditSpreadChart'), {
                type: 'line',
                data: {
                    labels: ['-3σ', 'Short Put', 'ATM', '+3σ'],
                    datasets: [{
                        label: 'Bull Put Spread P/L',
                        data: [-200, -200, 50, 50],
                        borderColor: brandColors.success,
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        fill: { target: { value: 0 }, above: 'rgba(40, 167, 69, 0.2)', below: 'rgba(220, 53, 69, 0.2)' },
                        tension: 0.1,
                    }]
                },
                 options: { ...commonRiskProfileOptions, plugins: { ...commonRiskProfileOptions.plugins, title: { display: true, text: 'Bull Put Spread Risk Profile', color: brandColors.darkGray } } }
            });
            
             new Chart(document.getElementById('calendarSpreadChart'), {
                type: 'line',
                data: {
                    labels: ['-2σ', '-1σ', 'ATM', '+1σ', '+2σ'],
                    datasets: [{
                        label: 'Profit/Loss',
                        data: [-20, 80, 100, 80, -20],
                        borderColor: brandColors.mediumBlue,
                        backgroundColor: 'rgba(0, 99, 178, 0.1)',
                        fill: { target: { value: 0 }, above: 'rgba(40, 167, 69, 0.2)', below: 'rgba(220, 53, 69, 0.2)' },
                        tension: 0.4,
                    }]
                },
                options: { ...commonRiskProfileOptions, plugins: { ...commonRiskProfileOptions.plugins, title: { display: true, text: 'Long Calendar Risk Profile', color: brandColors.darkGray } } }
            });
            
        });
    </script>
</body>
</html>
