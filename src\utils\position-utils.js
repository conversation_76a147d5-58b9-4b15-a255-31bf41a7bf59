// Import the robust Black-Scholes implementation
import { calculateOptionPrice } from './black-scholes-robust.js';

/**
 * Timezone-agnostic date to string conversion.
 * Avoids the timezone shift issues that occur with toISOString().split('T')[0]
 * @param {Date} date - The date to format
 * @returns {string} - Date string in YYYY-MM-DD format
 */
export function formatDateString(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Fetch exchange holidays from server API endpoint
 * This function is called from the browser to get holiday data
 */
export async function fetch_exchange_holidays() {
  try {
    // In browser environment, call server API to get holiday data
    const response = await fetch('/api/exchange-holidays/json');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.success && data.holidays) {
      // Convert ISO date strings back to Date objects and cache them
      const holidays = data.holidays.map(h => {
        const date = new Date(h.date + 'T12:00:00.000Z'); // Add noon UTC time to avoid timezone issues
        return date;
      });
      
      // Cache in localStorage with timestamp
      localStorage.setItem('hkex_holidays_json', JSON.stringify(data.holidays));
      localStorage.setItem('hkex_holidays_timestamp', Date.now().toString());
      console.log(`Holiday data fetched and cached: ${holidays.length} holidays`);

      return holidays;
    } else {
      throw new Error('Invalid response format from server');
    }

  } catch (error) {
    console.error('Error fetching exchange holidays:', error);
    return null;
  }
}

/**
 * Load exchange holidays from localStorage (browser) or file system (Node.js)
 * @returns {Date[]} Array of holiday dates
 */
export function loadExchangeHolidays() {
  // Check if we're in Node.js environment (no localStorage)
  if (typeof localStorage === 'undefined') {
    console.log('🔍 Node.js environment detected, loading from file system...');
    
    try {
      // Node.js environment - attempt to read from XML file directly
      // Note: This requires CommonJS environment or proper ES module setup
      let fs, config;
      
      try {
        // Try CommonJS require (most common case)
        fs = require('fs');
        config = require('./config.js');
      } catch (requireError) {
        // If require fails, fall back to the robust fallback system
        console.log('📄 Module loading not available, using fallback holidays');
        return getFallbackHolidays();
      }
      
      const xmlFilePath = config.getHolidayDataPath();
      
      if (fs.existsSync(xmlFilePath)) {
        console.log(`📁 Reading holiday data from: ${xmlFilePath}`);
        const xmlContent = fs.readFileSync(xmlFilePath, 'utf8');
        
        // Parse XML using regex pattern matching (more reliable than DOM in Node.js)
        const holidays = [];
        const datePattern = /(\d{1,2}\/\d{1,2}\/\d{4})/g;
        const dates = xmlContent.match(datePattern) || [];
        
        dates.forEach(dateStr => {
          try {
            const [day, month, year] = dateStr.split('/');
            const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0, 0);
            
            // Only add valid dates from 2024 onwards
            if (holidayDate.getFullYear() >= 2024) {
              holidays.push(holidayDate);
            }
          } catch (e) {
            console.warn('Could not parse date:', dateStr);
          }
        });
        
        // Remove duplicates
        const uniqueHolidays = holidays.filter((holiday, index, self) => 
          index === self.findIndex(h => h.getTime() === holiday.getTime())
        );
        
        if (uniqueHolidays.length > 0) {
          console.log(`✅ Loaded ${uniqueHolidays.length} holidays from XML file`);
          
          // Verify critical holiday is included
          const oct7_2025 = uniqueHolidays.find(h => 
            h.getFullYear() === 2025 && h.getMonth() === 9 && h.getDate() === 7
          );
          
          if (oct7_2025) {
            console.log('✅ 2025-10-07 verified in holiday list (Mid-Autumn Festival following day)');
          }
          
          return uniqueHolidays;
        }
      }
      
      console.log('📄 XML file not found or empty, using fallback holidays');
      return getFallbackHolidays();
      
    } catch (error) {
      console.warn('Could not load holidays from file, using fallback:', error.message);
      return getFallbackHolidays();
    }
  }

  try {
    // Browser environment - try to use cached JSON data first
    const jsonData = localStorage.getItem('hkex_holidays_json');
    const timestamp = localStorage.getItem('hkex_holidays_timestamp');
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    // If we have valid cached JSON data and it's fresh
    if (jsonData && timestamp && (now - parseInt(timestamp)) <= oneDay) {
      try {
        const holidayData = JSON.parse(jsonData);
        const holidays = holidayData.map(h => {
          const date = new Date(h.date + 'T12:00:00.000Z'); // Add noon UTC time
          return date;
        });
        
        console.log(`Loaded ${holidays.length} exchange holidays from cache (JSON)`);
        return holidays;
      } catch (parseError) {
        console.warn('Error parsing cached JSON holiday data:', parseError);
        // Fall through to XML parsing or fallback
      }
    }

    // Fallback to XML parsing for backward compatibility
    const xmlContent = localStorage.getItem('hkex_holidays_xml');
    
    // If no data or data is stale, return fallback holidays
    if (!xmlContent || !timestamp || (now - parseInt(timestamp)) > oneDay) {
      console.log('Holiday data is stale or missing, will use fallback');
      return getFallbackHolidays();
    }

    // Parse XML content using DOM (browser environment)
    const parser = new DOMParser();
    const doc = parser.parseFromString(xmlContent, 'text/xml');

    // Check for parsing errors
    const parseError = doc.querySelector('parsererror');
    if (parseError) {
      throw new Error('XML parsing error: ' + parseError.textContent);
    }

    const holidays = [];
    const rows = doc.querySelectorAll('tr');

    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length > 0) {
        // Get the first cell which should contain the date
        const dateText = cells[0].textContent.trim();

        // Extract date part (before any space)
        const datePart = dateText.split(' ')[0];

        // Parse date in DD/MM/YYYY format
        if (datePart && datePart.includes('/')) {
          try {
            const [day, month, year] = datePart.split('/');
            const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0, 0);
            
            // Only add valid dates from 2024 onwards (consistent with Node.js version)
            if (holidayDate.getFullYear() >= 2024) {
              holidays.push(holidayDate);
            }
          } catch (e) {
            console.warn('Could not parse holiday date:', datePart);
          }
        }
      }
    }

    // Remove duplicates (consistent with Node.js version)
    const uniqueHolidays = holidays.filter((holiday, index, self) => 
      index === self.findIndex(h => h.getTime() === holiday.getTime())
    );

    console.log(`Loaded ${uniqueHolidays.length} exchange holidays from cache (XML)`);
    return uniqueHolidays;

  } catch (error) {
    console.error('Error loading exchange holidays:', error);
    return getFallbackHolidays();
  }
}

/**
 * Get fallback holidays for the current year - calls server-utils in Node.js, returns local fallback in browser
 * @returns {Date[]} Array of common Hong Kong holidays
 */
function getFallbackHolidays() {
  // If in Node.js environment, try to use server-utils fallback
  if (typeof localStorage === 'undefined') {
    try {
      const serverUtils = require('../server-utils/exchange-holidays.js');
      return serverUtils.getFallbackHolidays();
    } catch (error) {
      console.warn('Could not load server-utils fallback, using local fallback');
    }
  }
  
  // Browser environment or server-utils fallback
  const currentYear = new Date().getFullYear();
  
  // If we're in 2025, use the actual 2025 HKEX holidays
  if (currentYear === 2025) {
    const holidays2025 = [
      new Date(2025, 0, 1),   // New Year's Day (1/1/2025)
      new Date(2025, 0, 29),  // Lunar New Year's Day (29/1/2025)
      new Date(2025, 0, 30),  // The second day of Lunar New Year (30/1/2025)
      new Date(2025, 0, 31),  // The third day of Lunar New Year (31/1/2025)
      new Date(2025, 3, 4),   // Ching Ming Festival (4/4/2025)
      new Date(2025, 3, 18),  // Good Friday (18/4/2025)
      new Date(2025, 3, 21),  // Easter Monday (21/4/2025)
      new Date(2025, 4, 1),   // Labour Day (1/5/2025)
      new Date(2025, 4, 5),   // The Birthday of the Buddha (5/5/2025)
      new Date(2025, 6, 1),   // HKSAR Establishment Day (1/7/2025)
      new Date(2025, 9, 1),   // National Day (1/10/2025)
      new Date(2025, 9, 7),   // The day following the Chinese Mid-Autumn Festival (7/10/2025) ⭐ THIS IS THE KEY DATE!
      new Date(2025, 9, 29),  // Chung Yeung Festival (29/10/2025)
      new Date(2025, 11, 25)  // Christmas Day (25/12/2025)
    ];
    
    // Set all dates to noon to avoid timezone issues
    holidays2025.forEach(holiday => {
      holiday.setHours(12, 0, 0, 0);
    });
    
    console.log(`Using 2025 HKEX fallback holidays (${holidays2025.length} holidays)`);
    return holidays2025;
  }
  
  // For other years, use generic fallback
  const fallbackHolidays = [
    new Date(currentYear, 0, 1),   // New Year's Day
    new Date(currentYear, 4, 1),   // Labour Day
    new Date(currentYear, 6, 1),   // HKSAR Establishment Day
    new Date(currentYear, 9, 1),   // National Day
    new Date(currentYear, 11, 25), // Christmas Day
    new Date(currentYear, 11, 26)  // Boxing Day
  ];

  // Set all dates to noon to avoid timezone issues
  fallbackHolidays.forEach(holiday => {
    holiday.setHours(12, 0, 0, 0);
  });

  console.log(`Using fallback holidays for ${currentYear} (${fallbackHolidays.length} holidays)`);
  return fallbackHolidays;
}
/**
 * Check if a date is a weekend (Saturday or Sunday)
 * @param {Date} date - The date to check
 * @returns {boolean} True if weekend, false otherwise
 */
function isWeekend(date) {
  const day = date.getDay();
  return day === 0 || day === 6; // Sunday = 0, Saturday = 6
}

/**
 * Check if a date is a stock exchange holiday
 * @param {Date} date - The date to check
 * @param {Date[]} holidays - Array of holiday dates
 * @returns {boolean} True if holiday, false otherwise
 */
function isHoliday(date, holidays) {
  return holidays.some(holiday => {
    return holiday.getFullYear() === date.getFullYear() &&
           holiday.getMonth() === date.getMonth() &&
           holiday.getDate() === date.getDate();
  });
}

/**
 * Calculate trading days between two dates, excluding weekends and exchange holidays
 * @param {Date} startDate - Start date (exclusive)
 * @param {Date} endDate - End date (inclusive)
 * @param {Date[]} holidays - Array of holiday dates
 * @returns {number} Number of trading days
 */
function calculateTradingDays(startDate, endDate, holidays) {
  let tradingDays = 0;
  const currentDate = new Date(startDate);
  currentDate.setDate(currentDate.getDate() + 1); // Start from next day

  while (currentDate <= endDate) {
    if (!isWeekend(currentDate) && !isHoliday(currentDate, holidays)) {
      tradingDays++;
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return tradingDays;
}

export function calculateDaysToExpiry(expiryDate, referenceDate = null) {
  if (!expiryDate) {
    return 0; // Return 0 days if no expiry date is set
  }

  // Use provided reference date or today's date
  const today = referenceDate ? new Date(referenceDate) : new Date();
  today.setHours(12, 0, 0, 0); // Set to noon for consistency

  let expiry;

  // Handle non-standard date formats (YY-MM or YY-MM-DD)
  if (expiryDate.length === 5 && expiryDate.includes('-')) {
    // Format: YY-MM (e.g., "25-05")
    const [year, month] = expiryDate.split('-').map(num => parseInt(num));
    // Assume 20xx for the year
    const fullYear = 2000 + year;
    // Convert to standard format YYYY-MM
    expiryDate = `${fullYear}-${month.toString().padStart(2, '0')}`;
  } else if (expiryDate.length === 8 && expiryDate.includes('-')) {
    // Format: YY-MM-DD (e.g., "25-05-23")
    const [year, month, day] = expiryDate.split('-').map(num => parseInt(num));
    // Assume 20xx for the year
    const fullYear = 2000 + year;
    // Convert to standard format YYYY-MM-DD
    expiryDate = `${fullYear}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  }

  if (expiryDate.length === 7) {
    // Monthly expiry format (YYYY-MM)
    // Get the last day of the month
    const [year, month] = expiryDate.split('-').map(num => parseInt(num));
    expiry = new Date(year, month - 1 + 1, 0, 12, 0, 0, 0); // Convert to 0-index, then add 1 to get next month, then get last day of current month
  } else {
    // Daily expiry format (YYYY-MM-DD)
    const [year, month, day] = expiryDate.split('-').map(num => parseInt(num));
    expiry = new Date(year, month - 1, day, 12, 0, 0, 0); // month is 0-indexed in JS Date constructor, set to noon
  }
  expiry.setHours(12, 0, 0, 0); // Ensure consistent time

  // For monthly options, adjust to second-to-last trading day (following HKEX convention)
  if (expiryDate.length === 7) {
    // Load exchange holidays
    const holidays = loadExchangeHolidays();

    // Find the second-to-last trading day of the month
    let lastTradingDay = new Date(expiry);
    let tradingDaysFound = 0;

    // Go backwards from the last day of the month to find trading days
    while (tradingDaysFound < 2 && lastTradingDay.getMonth() === expiry.getMonth()) {
      if (!isWeekend(lastTradingDay) && !isHoliday(lastTradingDay, holidays)) {
        tradingDaysFound++;
        if (tradingDaysFound === 2) {
          expiry = new Date(lastTradingDay);
          break;
        }
      }
      lastTradingDay.setDate(lastTradingDay.getDate() - 1);
    }
  }

  // Load exchange holidays for trading days calculation
  const holidays = loadExchangeHolidays();

  // Calculate trading days between today and expiry
  const tradingDaysToExpiry = calculateTradingDays(today, expiry, holidays);

  return tradingDaysToExpiry;
}

// Cache for premium calculations to avoid redundant calculations
const premiumCache = new Map();

/**
 * Clear the premium calculation cache
 * Call this function when system parameters change significantly
 * to ensure fresh calculations
 */
export function clearPremiumCache() {
  premiumCache.clear();
}

/**
 * Calculate the premium for an option position using the Black-Scholes model
 * with memoization to improve performance
 *
 * @param {Object} position - The option position
 * @param {number} stockPrice - Current stock price
 * @param {number} riskFreeRate - Risk-free interest rate (in percentage)
 * @param {number} volatility - Volatility (in percentage)
 * @returns {number} The calculated premium
 */
export function calculatePositionPremium(position, stockPrice, riskFreeRate, volatility) {
  // COMPREHENSIVE FIX: Ensure all parameters are properly handled

  // For Future trades, return the stock price
  if (position.type === 'Future') {
    return stockPrice || 0;
  }

  // Ensure we have valid parameters
  if (!position) {
    return 0;
  }

  // Ensure stock price is valid
  const effectiveStockPrice = typeof stockPrice === 'number' ? stockPrice : 0;

  // Ensure volatility is valid
  const effectiveVolatility = typeof volatility === 'number' ? volatility : 30; // Default to 30%

  // Ensure risk-free rate is valid
  const effectiveRiskFreeRate = typeof riskFreeRate === 'number' ? riskFreeRate : 2; // Default to 2%

  // Create a cache key based on the input parameters
  const cacheKey = `${position.type}_${position.strike}_${position.daysToExpiry}_${effectiveStockPrice}_${effectiveVolatility}_${effectiveRiskFreeRate}`;

  // Check if we have a cached result
  if (premiumCache.has(cacheKey)) {
    return premiumCache.get(cacheKey);
  }

  // Convert strike to number if it's a string
  let strikeValue;
  if (typeof position.strike === 'string') {
    if (position.strike === 'N/A') {
      strikeValue = effectiveStockPrice;
    } else {
      strikeValue = parseFloat(position.strike);
      if (isNaN(strikeValue)) {
        strikeValue = effectiveStockPrice;
      }
    }
  } else if (typeof position.strike === 'number') {
    strikeValue = position.strike;
  } else {
    strikeValue = effectiveStockPrice;
  }

  // Handle undefined or null days to expiry
  if (position.daysToExpiry === undefined || position.daysToExpiry === null) {
    // We'll try to calculate from ExpiryDate in the next block
  }

  // Convert negative days to expiry to zero (treat as already expired)
  if (position.daysToExpiry < 0) {
    position.daysToExpiry = 0;
  }

  // Calculate days to expiry if we have an expiry date
  let daysToExpiry = position.daysToExpiry;
  if (position.ExpiryDate) {
    // Only recalculate if daysToExpiry is not explicitly provided
    // This allows the adjusted daysToExpiry from positions-table to be used
    if (position.daysToExpiry === undefined || position.daysToExpiry === null) {
      daysToExpiry = calculateDaysToExpiry(position.ExpiryDate);
    }
  }

  // Handle options at or past expiry
  if (daysToExpiry <= 0) {
    // Create a cache key for intrinsic value calculation
    const intrinsicCacheKey = `intrinsic_${position.type}_${strikeValue}_${effectiveStockPrice}`;

    // Check if we have a cached intrinsic value
    if (premiumCache.has(intrinsicCacheKey)) {
      return premiumCache.get(intrinsicCacheKey);
    }

    // Calculate intrinsic value based on option type
    let intrinsicValue = 0;

    // Special case: At expiry, if stock price equals strike price, the option has no value
    if (Math.abs(effectiveStockPrice - strikeValue) < 0.01) {
      premiumCache.set(intrinsicCacheKey, 0);
      return 0;
    }

    if (position.type === 'Call') {
      intrinsicValue = Math.max(0, effectiveStockPrice - strikeValue);
    } else if (position.type === 'Put') {
      intrinsicValue = Math.max(0, strikeValue - effectiveStockPrice);
    }

    // Cache the intrinsic value
    premiumCache.set(intrinsicCacheKey, intrinsicValue);
    return intrinsicValue;
  }

  const timeToExpiry = Math.max(0, daysToExpiry) / 246; // Use 230 as trading days in a year to compensate for exchange holidays and IV bias
  const volatilityDecimal = effectiveVolatility / 100; // Convert percentage to decimal
  const riskFreeRateDecimal = effectiveRiskFreeRate / 100; // Convert percentage to decimal

  try {
    // Calculate the theoretical premium using Black-Scholes with validated parameters
    const premium = calculateOptionPrice(
      position.type,
      effectiveStockPrice,
      strikeValue,
      riskFreeRateDecimal,
      volatilityDecimal,
      timeToExpiry
    );

    // Check if the premium is a reasonable value
    if (!isFinite(premium) || isNaN(premium)) {
      return 0;
    }

    // Round to nearest integer for index options
    const roundedPremium = Math.round(premium);

    // Store the result in the cache (limit cache size to prevent memory issues)
    if (premiumCache.size > 1000) {
      // Clear the oldest entries if cache gets too large
      const keysToDelete = Array.from(premiumCache.keys()).slice(0, 100);
      keysToDelete.forEach(key => premiumCache.delete(key));
    }
    premiumCache.set(cacheKey, roundedPremium);

    return roundedPremium;
  } catch (error) {
    return 0;
  }
}

/**
 * Calculate the debit/credit for a position based on its premium and quantity
 *
 * @param {Object} position - The option position
 * @param {number} premium - The option premium
 * @returns {number} The calculated debit/credit
 */
export function calculateDebitCredit(position, premium) {
  // Debit/credit = quantity * premium
  // Positive quantity (long) = positive debit/credit (you pay)
  // Negative quantity (short) = negative debit/credit (you receive)
  return position.quantity * premium;
}

/**
 * Calculate the payoff for a position at a given stock price and time
 *
 * @param {Object} position - The option position
 * @param {number} stockPrice - The stock price to calculate payoff at
 * @param {number} daysRemaining - Days remaining until expiration
 * @param {number} riskFreeRate - Risk-free interest rate (in percentage)
 * @param {number} volatility - Volatility (in percentage)
 * @returns {number} The calculated payoff
 */
export function calculatePositionPayoff(position, stockPrice, daysRemaining, riskFreeRate, volatility) {
  // If we're past expiry, use expiry calculation
  if (daysRemaining <= 0) {
    return calculatePositionPayoffAtExpiry(position, stockPrice);
  }

  // Create a copy of the position with adjusted days to expiry for premium calculation
  const adjustedPosition = {
    ...position,
    daysToExpiry: daysRemaining
  };

  // Calculate premium using the same function used in positions-table.jsx
  // This ensures consistency between the table and chart calculations
  const premium = calculatePositionPremium(
    adjustedPosition,
    stockPrice,
    riskFreeRate,
    volatility
  );

  // Calculate market value (premium * quantity) - round to match positions-table.jsx
  const marketValue = Math.round(position.quantity * premium);

  // Calculate PnL as market value minus debit/credit
  const pnl = marketValue - position.debitCredit;

  // This matches the calculation in positions-table.jsx
  return pnl;
}

/**
 * Calculate the payoff for a position at expiration
 *
 * @param {Object} position - The option position
 * @param {number} stockPrice - The stock price at expiration
 * @returns {number} The calculated payoff at expiration
 */
export function calculatePositionPayoffAtExpiry(position, stockPrice) {
  // Create a copy of the position with days to expiry set to 0 for premium calculation
  const expiredPosition = {
    ...position,
    daysToExpiry: 0
  };

  // Calculate premium at expiry using the same function used in positions-table.jsx
  // This ensures consistency between the table and chart calculations
  const premium = calculatePositionPremium(
    expiredPosition,
    stockPrice,
    position.riskFreeRate || 0,
    position.volatility || 30
  );

  // Calculate market value (premium * quantity) - round to match positions-table.jsx
  const marketValue = Math.round(position.quantity * premium);

  // Calculate PnL as market value minus debit/credit
  const pnl = marketValue - position.debitCredit;

  // This matches the calculation in positions-table.jsx
  return pnl;
}

export function parseExpiryDate(expiryDate) {
  if (!expiryDate) return null;
  
  // If it's already a valid date string (YYYY-MM-DD format), return as-is
  if (expiryDate.length === 10 && expiryDate.includes('-')) {
    return expiryDate;
  }
  
  // Handle YY-MM-DD format (e.g., "25-06-15")
  if (expiryDate.length === 8 && expiryDate.includes('-')) {
    const [year, month, day] = expiryDate.split('-').map(num => parseInt(num));
    // Assume 20xx for the year
    const fullYear = 2000 + year;
    return `${fullYear}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  }
  
  // Handle YY-MM format (e.g., "25-06")
  if (expiryDate.length === 5 && expiryDate.includes('-')) {
    const [year, month] = expiryDate.split('-').map(num => parseInt(num));
    // Assume 20xx for the year
    const fullYear = 2000 + year;
    // Convert to end of month: YYYY-MM-DD format (last day of month)
    const lastDayOfMonth = new Date(fullYear, month, 0).getDate();
    return `${fullYear}-${month.toString().padStart(2, '0')}-${lastDayOfMonth.toString().padStart(2, '0')}`;
  }
  
  // Handle YYYY-MM format (e.g., "2025-06")
  if (expiryDate.length === 7 && expiryDate.includes('-')) {
    const [year, month] = expiryDate.split('-').map(num => parseInt(num));
    // Convert to end of month: YYYY-MM-DD format (last day of month)
    const lastDayOfMonth = new Date(year, month, 0).getDate();
    return `${year}-${month.toString().padStart(2, '0')}-${lastDayOfMonth.toString().padStart(2, '0')}`;
  }
  
  // For any other format, return as-is (weekly options or already valid dates)
  return expiryDate;
}
