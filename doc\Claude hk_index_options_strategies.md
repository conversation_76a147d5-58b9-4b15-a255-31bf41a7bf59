# Hong Kong Index Options Trading Strategies Framework

## Strategy Overview
This framework focuses on premium-collecting strategies for HSI, HHI, and HTI index options, designed to benefit from time decay while managing risk through defined market conditions and IV percentile analysis.

## Market Context Analysis
- **Current Environment**: Hong Kong markets showing volatility with HSI at ~23,893 points (June 2025)
- **Recent Performance**: HSI up 33.17% year-over-year but experiencing recent volatility
- **Options Landscape**: HKEX planning zero-day options by 2026, indicating growing derivatives sophistication
- **Volatility Characteristics**: Asian markets exhibit unique intraday and overnight gap behaviors

---

## Strategy 1: Adaptive Iron Condor (Non-Trending Markets)

### Core Concept
Sell both call and put spreads simultaneously when markets are range-bound with elevated IV percentiles.

### Entry Rules
- **IV Percentile Trigger**: Enter when VHSI (HSI Volatility Index) is above 70th percentile
- **Market Condition**: HSI trading within 5% range for past 10 trading days
- **Technical Setup**: Price oscillating between key support/resistance levels
- **Strike Selection**: 
  - Short strikes at 16-delta (approximately 1 standard deviation)
  - Long strikes 200-300 points away for HSI (adjust proportionally for HHI/HTI)

### Position Management
- **Profit Target**: 25-40% of premium collected
- **Loss Limit**: 200% of premium collected or breach of short strike
- **Time Exit**: Close at 7-10 days to expiration regardless of P&L
- **Delta Adjustment**: Roll tested side when delta exposure exceeds ±15

### Market Regime Adaptation
- **Trending Markets**: Reduce position size by 50% or avoid
- **High Volatility**: Widen spreads and reduce position size
- **Low Volatility**: Consider butterfly spreads instead

---

## Strategy 2: Trend-Following Put Credit Spreads (Bull Markets)

### Core Concept
Capitalize on upward trends by selling out-of-the-money put spreads below support levels.

### Entry Rules
- **Trend Confirmation**: HSI above 20-day and 50-day moving averages
- **Momentum Filter**: RSI(14) between 45-70 (avoid overbought conditions)
- **IV Percentile**: Enter when put IV percentile > 50th percentile
- **Support Level**: Short strike placed 3-5% below recent swing low
- **Strike Spacing**: 200-400 points for HSI

### Risk Management
- **Position Sizing**: Risk 1-2% of portfolio per trade
- **Stop Loss**: Close if HSI breaks below 20-day MA with volume confirmation
- **Profit Taking**: Close at 50% profit or 5 days before expiration
- **Rolling**: Roll down and out if untested and profitable

### Advanced Features
- **Volatility Skew Exploitation**: Focus on strikes where put skew is elevated
- **Earnings/Event Avoidance**: No positions 3 days before major economic announcements
- **Currency Hedge Consideration**: Monitor USD/HKD for additional risk factors

---

## Strategy 3: Volatility Crush Call Credit Spreads (Bear Markets)

### Core Concept
Sell call spreads during high IV periods in bearish environments, benefiting from both time decay and volatility contraction.

### Entry Rules
- **Bear Market Confirmation**: HSI below 20-day and 50-day MA with declining peaks
- **IV Trigger**: VHSI above 80th percentile (historically high volatility)
- **Technical Setup**: Failed breakout attempts or resistance rejections
- **Strike Selection**: Short calls at 30-delta, long calls 300-500 points higher
- **Timing**: Enter 15-25 days to expiration

### Execution Framework
- **Volatility Timing**: Enter after volatility spikes (VHSI jumps >20% in single day)
- **Market Structure**: Focus on lower highs pattern confirmation
- **Risk/Reward**: Target 3:1 reward-to-risk ratio minimum
- **Adjustment Protocol**: Convert to Iron Condor if market stabilizes

### Exit Strategy
- **Profit Target**: 60% of maximum profit potential
- **Loss Management**: Exit if HSI closes above short strike
- **Time Decay**: Hold until 5 days to expiration for maximum theta capture
- **Vol Environment**: Close early if VHSI drops below 30th percentile

---

## Strategy 4: Gamma-Scalping Short Straddles (Low IV Environments)

### Core Concept
Sell at-the-money straddles when IV is compressed, actively managing delta through underlying futures positions.

### Entry Conditions
- **IV Percentile**: VHSI below 25th percentile (historically low volatility)
- **Market State**: Low realized volatility over past 20 days
- **Time Frame**: 30-45 days to expiration for optimal theta/gamma balance
- **Strike Selection**: ATM strikes closest to previous day's closing price

### Delta Management Protocol
- **Hedge Triggers**: Hedge when delta exceeds ±25
- **Hedging Instrument**: Use HSI futures for precise delta neutrality
- **Rebalancing Frequency**: Daily delta assessment, hedge when necessary
- **Profit Mechanism**: Capture theta while maintaining delta neutrality through gamma scalping

### Risk Controls
- **Maximum Loss**: 300% of premium collected
- **Volatility Stop**: Exit if IV percentile exceeds 60th percentile
- **Time Management**: Close at 10 days to expiration
- **Gamma Risk**: Reduce position size as expiration approaches

---

## Strategy 5: Opportunistic Ratio Put Spreads (Market Extremes)

### Core Concept
Deploy 1x2 or 1x3 put ratio spreads during market panics, collecting premium while maintaining upside profit potential.

### Entry Triggers
- **Extreme Conditions**: HSI drops >5% in single session with elevated volume
- **Fear Gauge**: VHSI spikes above 90th percentile
- **Technical Oversold**: RSI(14) below 30 with price at major support
- **Sentiment**: High put/call ratio indicating excessive bearishness

### Structure Design
- **Ratio Selection**: 1x2 spread (buy 1, sell 2) for moderate crashes; 1x3 for severe crashes
- **Strike Positioning**: 
  - Long put: 10-15% OTM
  - Short puts: 20-25% OTM
- **Breakeven Management**: Ensure lower breakeven is at least 15% below current price

### Profit Maximization
- **Sweet Spot**: Maximum profit when HSI settles between short strikes
- **Upside Capture**: Profit from recovery bounce above long put strike
- **Downside Protection**: Limited risk above breakeven point
- **Time Decay**: Accelerated theta collection on short positions

### Risk Mitigation
- **Gap Risk**: Use stop-loss orders to prevent excessive losses
- **Position Sizing**: Smaller positions due to undefined risk below lower breakeven
- **Monitoring**: Daily assessment of market conditions and sentiment shifts
- **Exit Strategy**: Close positions when VIX normalizes or technical recovery confirmed

---

## Implementation Framework

### Backtesting Parameters
- **Data Requirements**: EOD prices for HSI, HHI, HTI with historical IV data
- **Black-Scholes Assumptions**: Use theoretical pricing with appropriate interest rates and dividends
- **Transaction Costs**: Include realistic commission and bid-ask spread assumptions
- **Position Sizing**: Risk-based position sizing with maximum 5% portfolio risk per strategy

### Market Regime Classification
- **Bull Market**: Price above long-term MA with positive momentum
- **Bear Market**: Price below long-term MA with negative momentum  
- **Sideways Market**: Price within defined range with low directional bias

### Performance Metrics
- **Risk-Adjusted Returns**: Sharpe ratio, Sortino ratio, maximum drawdown
- **Consistency**: Win rate, average win/loss ratio, profit factor
- **Market Sensitivity**: Performance across different volatility regimes
- **Correlation**: Strategy correlation to minimize portfolio risk

### Technology Stack
- **Data Source**: HKEX historical data, Bloomberg/Reuters feeds
- **Execution Platform**: Professional options trading platform with API connectivity
- **Risk Management**: Real-time position monitoring and automated alerts
- **Backtesting Engine**: Custom Python framework with vectorized calculations