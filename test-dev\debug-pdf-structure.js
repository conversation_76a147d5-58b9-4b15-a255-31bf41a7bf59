const fs = require('fs');
const path = require('path');
const { parseStatement } = require('../src/server-utils/statement-parser');

async function debugTextStructure() {
  try {
    console.log('Debug text structure...');
    
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    
    const PDFExtract = require('pdf.js-extract').PDFExtract;
    const pdfExtract = new PDFExtract();

    // Write buffer to temporary file for pdf.js-extract
    const tempFilePath = path.join(__dirname, '../temp_statement.pdf');
    fs.writeFileSync(tempFilePath, pdfBuffer);

    try {
      const extractOptions = {
        password: '6369'
      };

      const data = await new Promise((resolve, reject) => {
        pdfExtract.extract(tempFilePath, extractOptions, (err, data) => {
          if (err) reject(err);
          else resolve(data);
        });
      });

      console.log('Raw PDF extraction result:');
      console.log('Number of pages:', data.pages.length);
      
      if (data.pages.length > 0) {
        const firstPage = data.pages[0];
        console.log('First page content items:', firstPage.content.length);
        
        // Show first 20 content items with their positions
        console.log('\nFirst 20 content items:');
        for (let i = 0; i < Math.min(20, firstPage.content.length); i++) {
          const item = firstPage.content[i];
          console.log(`${i}: x=${item.x}, y=${item.y}, text="${item.str}"`);
        }
      }

      // Clean up temp file
      fs.unlinkSync(tempFilePath);

    } catch (extractError) {
      // Clean up temp file on error
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
      throw extractError;
    }
    
  } catch (error) {
    console.error('Debug failed:', error.message);
  }
}

debugTextStructure();
