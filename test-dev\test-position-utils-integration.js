/**
 * Test position-utils loadExchangeHolidays integration
 * This creates a bridge between ES modules and CommonJS to test the integration
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Position-Utils Integration with XML Loading...\n');

console.log('=== 1. Testing Current Position-Utils Behavior ===');

// Simulate the position-utils environment by manually calling the internal function
function simulatePositionUtilsNodeJS() {
  console.log('🔍 Simulating Node.js environment loading...');
  
  try {
    // This simulates the loadExchangeHolidaysNodeJSSync function
    const config = require('../src/utils/config.js');
    const xmlFilePath = config.getHolidayDataPath();
    
    console.log(`📁 Attempting to read: ${xmlFilePath}`);
    
    if (fs.existsSync(xmlFilePath)) {
      console.log('✅ XML file exists');
      const xmlContent = fs.readFileSync(xmlFilePath, 'utf8');
      
      // Parse XML using simple regex (same logic as position-utils)
      const holidays = [];
      const datePattern = /(\d{1,2}\/\d{1,2}\/\d{4})/g;
      const dates = xmlContent.match(datePattern) || [];
      
      console.log(`🔍 Found ${dates.length} date patterns`);
      
      dates.forEach(dateStr => {
        try {
          const [day, month, year] = dateStr.split('/');
          const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0, 0);
          
          // Only add valid dates from 2024 onwards
          if (holidayDate.getFullYear() >= 2024) {
            holidays.push(holidayDate);
          }
        } catch (e) {
          console.warn('Could not parse date:', dateStr);
        }
      });
      
      // Remove duplicates
      const uniqueHolidays = holidays.filter((holiday, index, self) => 
        index === self.findIndex(h => h.getTime() === holiday.getTime())
      );
      
      console.log(`✅ Parsed ${uniqueHolidays.length} unique holidays`);
      
      // Check for critical holiday
      const oct7_2025 = uniqueHolidays.find(h => 
        h.getFullYear() === 2025 && h.getMonth() === 9 && h.getDate() === 7
      );
      
      if (oct7_2025) {
        console.log('✅ 2025-10-07 found in position-utils style parsing');
        
        // Format properly for display
        function formatLocalDate(date) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        }
        
        console.log(`   Date: ${formatLocalDate(oct7_2025)}`);
        console.log(`   Day: ${oct7_2025.toLocaleDateString('en-US', { weekday: 'long' })}`);
      } else {
        console.log('❌ 2025-10-07 NOT found in position-utils style parsing');
      }
      
      return uniqueHolidays;
      
    } else {
      console.log('❌ XML file not found');
      return [];
    }
    
  } catch (error) {
    console.error('❌ Error in simulation:', error.message);
    return [];
  }
}

const simulatedHolidays = simulatePositionUtilsNodeJS();

console.log('\n=== 2. Comparing Server-Utils vs Position-Utils Style Loading ===');

// Load using server-utils for comparison
const exchangeHolidays = require('../src/server-utils/exchange-holidays.js');
const serverHolidays = exchangeHolidays.loadExchangeHolidaysFromFile();

console.log(`📊 Server-utils holidays: ${serverHolidays.length}`);
console.log(`📊 Position-utils style: ${simulatedHolidays.length}`);

// Check if they have the same dates
if (serverHolidays.length === simulatedHolidays.length) {
  console.log('✅ Same number of holidays loaded by both methods');
  
  // Check a few specific dates
  function formatLocalDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  
  console.log('\n📅 First 5 holidays comparison:');
  for (let i = 0; i < Math.min(5, serverHolidays.length); i++) {
    const serverDate = formatLocalDate(serverHolidays[i]);
    const positionDate = formatLocalDate(simulatedHolidays[i]);
    const match = serverDate === positionDate ? '✅' : '❌';
    console.log(`  ${i + 1}. Server: ${serverDate} | Position: ${positionDate} ${match}`);
  }
  
} else {
  console.log('❌ Different number of holidays loaded');
}

console.log('\n=== 3. Testing Trading Days Calculation ===');

// Test the critical functionality
function isHoliday(date, holidays) {
  return holidays.some(holiday => {
    return holiday.getFullYear() === date.getFullYear() &&
           holiday.getMonth() === date.getMonth() &&
           holiday.getDate() === date.getDate();
  });
}

function calculateTradingDays(startDate, endDate, holidays) {
  function isWeekend(date) {
    const day = date.getDay();
    return day === 0 || day === 6; // Sunday = 0, Saturday = 6
  }
  
  let tradingDays = 0;
  const currentDate = new Date(startDate);
  currentDate.setDate(currentDate.getDate() + 1); // Start from next day

  while (currentDate <= endDate) {
    if (!isWeekend(currentDate) && !isHoliday(currentDate, holidays)) {
      tradingDays++;
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return tradingDays;
}

// Test around 2025-10-07
const testStart = new Date(2025, 9, 6); // Oct 6, 2025
const testEnd = new Date(2025, 9, 7);   // Oct 7, 2025

const tradingDaysServer = calculateTradingDays(testStart, testEnd, serverHolidays);
const tradingDaysPosition = calculateTradingDays(testStart, testEnd, simulatedHolidays);

console.log(`📊 Trading days (server-utils): ${tradingDaysServer}`);
console.log(`📊 Trading days (position-utils style): ${tradingDaysPosition}`);

if (tradingDaysServer === 0 && tradingDaysPosition === 0) {
  console.log('✅ Both methods correctly identify 2025-10-07 as holiday (0 trading days)');
} else {
  console.log('❌ Inconsistent holiday detection between methods');
}

console.log('\n🎯 Position-Utils Integration Test Complete!');