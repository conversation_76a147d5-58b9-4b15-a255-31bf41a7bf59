import React, { useState } from 'react';
import useStrategyManagementStore from '../../store/useStrategyManagementStore';
import useStrategyWindowStore from '../../store/useStrategyWindowStore';
import StrategyCreateModal from './StrategyCreateModal';

/**
 * Strategy Management Table Component
 * 
 * This component displays the main table for managing strategies with features like:
 * - Sortable columns
 * - Search functionality
 * - Pagination
 * - Bulk actions
 * - Strategy creation
 */
const StrategyManagementTable = () => {
  const {
    getPaginatedStrategies,
    updateFilters,
    filters,
    deleteStrategy,
    deleteStrategies,
    isLoading
  } = useStrategyManagementStore();

  const { openWindow, isWindowOpen } = useStrategyWindowStore();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedStrategies, setSelectedStrategies] = useState(new Set());
  const [sortConfig, setSortConfig] = useState({
    key: 'createdAt',
    direction: 'desc'
  });

  const paginatedData = getPaginatedStrategies();
  const { strategies, totalCount, totalPages, currentPage } = paginatedData;

  // Handle search input
  const handleSearch = (e) => {
    updateFilters({ search: e.target.value, page: 1 });
  };

  // Handle sorting
  const handleSort = (column) => {
    const direction = sortConfig.key === column && sortConfig.direction === 'asc' ? 'desc' : 'asc';
    setSortConfig({ key: column, direction });
    updateFilters({ sortBy: column, sortOrder: direction, page: 1 });
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    updateFilters({ page: newPage });
  };

  // Handle strategy selection
  const handleStrategySelect = (strategyId) => {
    const newSelected = new Set(selectedStrategies);
    if (newSelected.has(strategyId)) {
      newSelected.delete(strategyId);
    } else {
      newSelected.add(strategyId);
    }
    setSelectedStrategies(newSelected);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedStrategies.size === strategies.length) {
      setSelectedStrategies(new Set());
    } else {
      setSelectedStrategies(new Set(strategies.map(s => s.id)));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedStrategies.size === 0) return;
    
    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedStrategies.size} selected strategies? This action cannot be undone.`
    );
    
    if (confirmed) {
      try {
        await deleteStrategies(Array.from(selectedStrategies));
        setSelectedStrategies(new Set());
      } catch (error) {
        console.error('Error deleting strategies:', error);
      }
    }
  };

  // Handle single strategy delete
  const handleDelete = async (strategyId) => {
    const confirmed = window.confirm(
      'Are you sure you want to delete this strategy? This action cannot be undone.'
    );
    
    if (confirmed) {
      try {
        await deleteStrategy(strategyId);
      } catch (error) {
        console.error('Error deleting strategy:', error);
      }
    }
  };

  // Handle strategy window opening
  const handleOpenStrategy = (strategyId) => {
    const strategy = strategies.find(s => s.id === strategyId);
    if (strategy) {
      const windowId = openWindow(strategyId, strategy);
      if (windowId) {
        console.log('Opened strategy window:', windowId, 'for strategy:', strategyId);
      } else {
        alert('Maximum number of strategy windows reached. Please close a window before opening another.');
      }
    }
  };

  // Format date for display
  const formatDate = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Get sort icon
  const getSortIcon = (column) => {
    if (sortConfig.key !== column) {
      return '↕️';
    }
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="strategy-management-table">
      {/* Table Header Actions */}
      <div className="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search strategies..."
              value={filters.search}
              onChange={handleSearch}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          
          {selectedStrategies.size > 0 && (
            <button
              onClick={handleBulkDelete}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              Delete Selected ({selectedStrategies.size})
            </button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowCreateModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Add Strategy
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input
                  type="checkbox"
                  checked={strategies.length > 0 && selectedStrategies.size === strategies.length}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('id')}
              >
                Strategy ID {getSortIcon('id')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('stockSymbol')}
              >
                Symbol {getSortIcon('stockSymbol')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('name')}
              >
                Name {getSortIcon('name')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('createdAt')}
              >
                Created {getSortIcon('createdAt')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('lastModified')}
              >
                Modified {getSortIcon('lastModified')}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {strategies.map((strategy) => (
              <tr key={strategy.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedStrategies.has(strategy.id)}
                    onChange={() => handleStrategySelect(strategy.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => handleOpenStrategy(strategy.id)}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    {strategy.id}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {strategy.stockSymbol}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {strategy.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(strategy.createdAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(strategy.lastModified)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleOpenStrategy(strategy.id)}
                      className="text-blue-600 hover:text-blue-900"
                      title="Open Strategy"
                    >
                      📊
                    </button>
                    <button
                      onClick={() => handleDelete(strategy.id)}
                      className="text-red-600 hover:text-red-900"
                      title="Delete Strategy"
                    >
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Empty State */}
        {strategies.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No strategies</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating your first strategy.</p>
            <div className="mt-6">
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Add Strategy
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((currentPage - 1) * filters.pageSize) + 1} to {Math.min(currentPage * filters.pageSize, totalCount)} of {totalCount} strategies
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="px-3 py-2 text-sm font-medium text-gray-700">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Create Strategy Modal */}
      <StrategyCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
    </div>
  );
};

export default StrategyManagementTable;
