import React from 'react';

/**
 * Trade Input Form Component
 * 
 * This component provides a form for users to paste their trade data.
 */
const TradeInputForm = ({ rawText, onRawTextChange, onParse, isParsing, onDeleteAllTrades, isDeleting }) => {
  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      onRawTextChange(text);
    } catch (err) {
      console.error('Failed to read clipboard contents: ', err);
      // Clipboard access might be denied or not available
    }
  };
  
  return (
    <div className="bg-white shadow-md rounded-lg p-4 mb-6">
      <div className="mb-4">
        <h2 className="text-xl font-bold mb-2">Input Trade Data</h2>
        <p className="text-sm text-gray-600 mb-4">
          Paste your trade data from your trading app into the text area below.
        </p>
        <textarea
          placeholder="Paste your trade data here... Example:
HHI7200R5	Index 2025-06 7200 Put	2@400.00	...
HSIK5	Index Future 2025-05	2@23037.00	..."
          value={rawText}
          onChange={(e) => onRawTextChange(e.target.value)}
          rows={10}
          className="w-full p-2 border border-gray-300 rounded text-sm min-h-[200px]"
        />
      </div>
      <div className="flex flex-col sm:flex-row gap-2">
        <button
          onClick={handlePaste}
          className="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50 w-full sm:w-auto"
        >
          Paste from Clipboard
        </button>
        <button
          onClick={onDeleteAllTrades}
          disabled={isDeleting}
          className={`px-4 py-2 rounded w-full sm:w-auto flex-grow flex items-center justify-center ${
            isDeleting
              ? 'bg-red-300 cursor-not-allowed'
              : 'bg-red-600 hover:bg-red-700 text-white'
          }`}
        >
          {isDeleting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Deleting...
            </>
          ) : (
            <>
              <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              Delete All Trades
            </>
          )}
        </button>
        <button
          onClick={onParse}
          disabled={isParsing || !rawText.trim()}
          className={`px-4 py-2 rounded w-full sm:w-auto flex-grow flex items-center justify-center ${
            isParsing || !rawText.trim()
              ? 'bg-blue-300 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isParsing ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Parsing...
            </>
          ) : (
            <>
              <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              Parse Trades
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default TradeInputForm;
