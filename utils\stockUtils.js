/**
 * Utility functions for fetching stock information using yahoo-finance2
 */
const yahooFinance = require('yahoo-finance2').default;

/**
 * Get information for a specific stock symbol
 * @param {string} symbol - The stock symbol to look up (e.g., '^HSI', '0700.HK', '9988.HK')
 * @returns {Promise<Object>} - Promise resolving to the symbol information
 */
const getSymbolInfo = async (symbol) => {
  try {
    // Validate input
    if (!symbol) {
      throw new Error('Symbol is required');
    }

    // Clean the symbol (remove any whitespace, etc.)
    const cleanSymbol = symbol.trim();

    console.log(`Fetching quote for symbol: ${cleanSymbol}`);

    // Use yahoo-finance2 to get quote information
    const result = await yahooFinance.quote(cleanSymbol);

    // Format the response
    const response = {
      error: false,
      fetchedAt: new Date().toISOString(),
      symbol: cleanSymbol,
      currency: result.currency,
      response: {
        regularMarketPrice: result.regularMarketPrice,
        regularMarketChange: result.regularMarketChange,
        regularMarketChangePercent: result.regularMarketChangePercent,
        regularMarketDayHigh: result.regularMarketDayHigh,
        regularMarketDayLow: result.regularMarketDayLow,
        regularMarketVolume: result.regularMarketVolume,
        marketCap: result.marketCap,
        shortName: result.shortName,
        longName: result.longName,
        exchange: result.exchange,
        quoteType: result.quoteType,
        symbol: result.symbol,
        updated: Date.now()
      }
    };

    return response;
  } catch (error) {
    console.error(`Error in getSymbolInfo for ${symbol}:`, error);
    return {
      error: true,
      message: error.message,
      symbol: symbol
    };
  }
};

/**
 * Get historical prices for a specific stock symbol
 * @param {string} symbol - The stock symbol to look up
 * @param {Date} startDate - Start date for historical data
 * @param {Date} endDate - End date for historical data
 * @param {string} frequency - Data frequency ('1d', '1wk', or '1mo')
 * @returns {Promise<Object>} - Promise resolving to the historical price data
 */
const getHistoricalPrices = async (symbol, startDate, endDate, frequency = '1d') => {
  try {
    // Validate inputs
    if (!symbol) {
      throw new Error('Symbol is required');
    }

    if (!startDate || !endDate) {
      throw new Error('Start date and end date are required');
    }

    // Map frequency to yahoo-finance2 interval
    let interval;
    switch (frequency) {
      case '1d':
        interval = '1d';
        break;
      case '1wk':
        interval = '1wk';
        break;
      case '1mo':
        interval = '1mo';
        break;
      default:
        throw new Error('Frequency must be one of: 1d, 1wk, 1mo');
    }

    // Clean the symbol
    const cleanSymbol = symbol.trim();

    console.log(`Fetching historical data for symbol: ${cleanSymbol}`);

    // Use yahoo-finance2 to get historical data
    const result = await yahooFinance.historical(cleanSymbol, {
      period1: startDate,
      period2: endDate,
      interval: interval
    });

    // Format the response
    const response = {
      error: false,
      fetchedAt: new Date().toISOString(),
      symbol: cleanSymbol,
      response: result.map(item => ({
        date: new Date(item.date).getTime() / 1000, // Convert to Unix timestamp in seconds
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        adjClose: item.adjClose,
        volume: item.volume
      }))
    };

    return response;
  } catch (error) {
    console.error(`Error fetching historical prices for ${symbol}:`, error);
    return {
      error: true,
      message: error.message,
      symbol: symbol
    };
  }
};

module.exports = {
  getSymbolInfo,
  getHistoricalPrices
};
