/**
 * Test script to debug why 2025-10-07 shows time decay when it should be a holiday
 */

// Parse the XML data to extract holidays
function parseHKEXHolidays() {
  const fs = require('fs');
  const path = require('path');
  
  try {
    const xmlPath = path.join(process.cwd(), 'output', 'hkex_holidays.xml');
    const xmlContent = fs.readFileSync(xmlPath, 'utf8');
    
    console.log('🔍 PARSING HKEX HOLIDAYS XML');
    console.log('=' .repeat(40));
    
    // Extract date patterns from XML
    const datePattern = /(\d{1,2}\/\d{1,2}\/\d{4})/g;
    const dates = xmlContent.match(datePattern);
    
    if (dates) {
      console.log(`📅 Found ${dates.length} dates in XML`);
      
      // Look specifically for October 2025 dates
      const october2025Dates = dates.filter(date => date.includes('/2025'));
      
      console.log('\n🎯 OCTOBER 2025 HOLIDAYS:');
      october2025Dates.forEach(date => {
        if (date.includes('/10/2025')) {
          console.log(`  - ${date}`);
        }
      });
      
      // Check specifically for 2025-10-07
      const targetDate = '7/10/2025';
      const isHoliday = dates.includes(targetDate);
      
      console.log(`\n❓ Is 2025-10-07 a holiday according to XML?`);
      console.log(`   Looking for: ${targetDate}`);
      console.log(`   Found: ${isHoliday ? '✅ YES' : '❌ NO'}`);
      
      if (isHoliday) {
        console.log(`   🎊 Holiday confirmed: "The day following the Chinese Mid-Autumn Festival"`);
      }
      
      return dates;
    }
    
  } catch (error) {
    console.error('Error parsing XML:', error.message);
    return [];
  }
}

// Test the calculateDaysToExpiry function specifically
function testCalculateDaysToExpiry() {
  console.log('\n🎯 TESTING calculateDaysToExpiry FOR 2025-10-07');
  console.log('=' .repeat(50));
  
  try {
    // Since position-utils is ES6 module, we need to test differently
    // Let's read the file and check the logic
    const fs = require('fs');
    const path = require('path');
    
    const utilsPath = path.join(process.cwd(), 'src', 'utils', 'position-utils.js');
    const utilsContent = fs.readFileSync(utilsPath, 'utf8');
    
    console.log('📁 Reading position-utils.js to analyze holiday logic...');
    
    // Check if it contains references to holiday detection
    const hasHolidayLogic = utilsContent.includes('isHongKongExchangeHoliday') ||
                           utilsContent.includes('loadExchangeHolidays') ||
                           utilsContent.includes('getFallbackHolidays');
    
    console.log(`🔍 Contains holiday logic: ${hasHolidayLogic ? '✅ YES' : '❌ NO'}`);
    
    // Look for hardcoded 2025 dates
    const dateMatches = utilsContent.match(/new Date\(2025[^)]+\)/g);
    if (dateMatches) {
      console.log('\n📅 Found hardcoded 2025 dates:');
      dateMatches.forEach(date => {
        console.log(`   ${date}`);
        if (date.includes('9, 17') || date.includes('10, 7')) {
          console.log(`   🎯 Found reference to October dates!`);
        }
      });
    }
    
    // Check if XML loading is implemented
    const hasXMLLoading = utilsContent.includes('hkex_holidays.xml') ||
                         utilsContent.includes('/api/exchange-holidays');
    
    console.log(`🔗 Has XML loading: ${hasXMLLoading ? '✅ YES' : '❌ NO'}`);
    
  } catch (error) {
    console.error('Error reading position-utils:', error.message);
  }
}

// Test the Black-Scholes calculation logic
function testBlackScholesCalculation() {
  console.log('\n⚫ TESTING BLACK-SCHOLES CALCULATION LOGIC');
  console.log('=' .repeat(40));
  
  // Mock position data similar to what would cause the issue
  const mockPosition = {
    trade: {
      ticker: 'HSI26200',
      type: 'Call',
      strike: 26200,
      quantity: -4,
      ExpiryDate: '2025-10-17'
    }
  };
  
  const stockPrice = 26000;
  const testDates = ['2025-10-06', '2025-10-07', '2025-10-08'];
  
  console.log('🧮 Analyzing days to expiry for consecutive dates:');
  
  // Simple days calculation without holiday consideration
  testDates.forEach(dateStr => {
    const currentDate = new Date(dateStr);
    const expiryDate = new Date('2025-10-17');
    
    // Calculate calendar days
    const timeDiff = expiryDate.getTime() - currentDate.getTime();
    const calendarDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
    
    // Calculate trading days (excluding weekends only)
    let tradingDays = 0;
    const tempDate = new Date(currentDate);
    
    while (tempDate < expiryDate) {
      const dayOfWeek = tempDate.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not weekend
        tradingDays++;
      }
      tempDate.setDate(tempDate.getDate() + 1);
    }
    
    console.log(`\n📅 ${dateStr}:`);
    console.log(`   Calendar days: ${calendarDays}`);
    console.log(`   Trading days (weekends only): ${tradingDays}`);
    console.log(`   Day of week: ${currentDate.toLocaleDateString('en-US', { weekday: 'long' })}`);
    
    if (dateStr === '2025-10-07') {
      console.log(`   🎯 This should be a HOLIDAY, so trading days should equal 2025-10-06`);
    }
  });
}

// Check the XML content for specific holiday
function checkXMLForSpecificHoliday() {
  console.log('\n📋 DETAILED XML ANALYSIS FOR 2025-10-07');
  console.log('=' .repeat(45));
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    const xmlPath = path.join(process.cwd(), 'output', 'hkex_holidays.xml');
    const xmlContent = fs.readFileSync(xmlPath, 'utf8');
    
    // Look for Mid-Autumn Festival references
    const midAutumnLines = xmlContent.split('\n').filter(line => 
      line.toLowerCase().includes('mid-autumn') ||
      line.includes('7/10/2025') ||
      line.includes('following the chinese')
    );
    
    console.log('🔍 Lines containing Mid-Autumn or 7/10/2025:');
    midAutumnLines.forEach((line, index) => {
      console.log(`   ${index + 1}: ${line.trim()}`);
    });
    
    // Check the exact structure around this date
    if (xmlContent.includes('7/10/2025')) {
      console.log('\n✅ 2025-10-07 IS in the XML file');
      
      // Find the context around this date
      const lines = xmlContent.split('\n');
      const targetLineIndex = lines.findIndex(line => line.includes('7/10/2025'));
      
      if (targetLineIndex !== -1) {
        console.log('\n📄 Context around 2025-10-07:');
        for (let i = Math.max(0, targetLineIndex - 2); i <= Math.min(lines.length - 1, targetLineIndex + 2); i++) {
          const marker = i === targetLineIndex ? '>>> ' : '    ';
          console.log(`${marker}${i}: ${lines[i].trim()}`);
        }
      }
    } else {
      console.log('\n❌ 2025-10-07 is NOT in the XML file');
    }
    
  } catch (error) {
    console.error('Error analyzing XML:', error.message);
  }
}

// Run all tests
function runAllTests() {
  console.log('🚀 DEBUGGING 2025-10-07 THETA CALCULATION ISSUE');
  console.log('=' .repeat(60));
  
  parseHKEXHolidays();
  testCalculateDaysToExpiry();
  testBlackScholesCalculation();
  checkXMLForSpecificHoliday();
  
  console.log('\n📋 INVESTIGATION SUMMARY:');
  console.log('1. ✅ Check if 2025-10-07 is in HKEX holidays XML');
  console.log('2. 🔍 Verify position-utils holiday detection logic');
  console.log('3. 🧮 Test calculateDaysToExpiry for consecutive days');
  console.log('4. ⚫ Analyze Black-Scholes time decay calculation');
  console.log('\n🎯 NEXT STEPS:');
  console.log('- If XML contains the date but calculation is wrong: Fix holiday loading');
  console.log('- If XML missing the date: Update XML generation');
  console.log('- If calculation ignores holidays: Fix calculateDaysToExpiry logic');
}

// Export for testing
if (require.main === module) {
  runAllTests();
}

module.exports = {
  parseHKEXHolidays,
  testCalculateDaysToExpiry,
  testBlackScholesCalculation,
  checkXMLForSpecificHoliday
};