/**
 * Comprehensive test for refactored holiday loading system
 * Tests the new environment-aware API integration between position-utils and server-utils
 */

const path = require('path');

// Test configuration
console.log('🧪 Testing refactored holiday loading system...\n');

async function testRefactoredSystem() {
  const startTime = Date.now();
  
  try {
    // 1. Test configuration system
    console.log('=== 1. Testing Configuration System ===');
    const config = require('../src/utils/config.js');
    
    const outputPath = config.getOutputPath();
    const holidayPath = config.getHolidayDataPath();
    
    console.log(`📁 Output path: ${outputPath}`);
    console.log(`🎄 Holiday data path: ${holidayPath}`);
    
    // Ensure directory exists
    const createdDir = config.ensureOutputDirectory();
    console.log(`✅ Output directory ensured: ${createdDir}\n`);
    
    // 2. Test server-utils API
    console.log('=== 2. Testing Server-Utils Holiday API ===');
    const { getExchangeHolidays, getFallbackHolidays } = require('../src/server-utils/exchange-holidays.js');
    
    const serverHolidays = getExchangeHolidays();
    console.log(`📅 Server-utils loaded ${serverHolidays.length} holidays`);
    
    // Check for critical 2025-10-07 holiday
    const critical2025Holiday = serverHolidays.find(h => 
      h.getFullYear() === 2025 && h.getMonth() === 9 && h.getDate() === 7
    );
    
    if (critical2025Holiday) {
      console.log('✅ Critical holiday 2025-10-07 found in server-utils');
    } else {
      console.log('❌ Critical holiday 2025-10-07 NOT found in server-utils');
    }
    
    // 3. Test position-utils Node.js integration
    console.log('\n=== 3. Testing Position-Utils Node.js Integration ===');
    
    // Import position-utils in Node.js environment
    const positionUtils = require('../src/utils/position-utils.js');
    
    const positionHolidays = positionUtils.loadExchangeHolidays();
    console.log(`📅 Position-utils loaded ${positionHolidays.length} holidays`);
    
    // Check for critical 2025-10-07 holiday
    const criticalPositionHoliday = positionHolidays.find(h => 
      h.getFullYear() === 2025 && h.getMonth() === 9 && h.getDate() === 7
    );
    
    if (criticalPositionHoliday) {
      console.log('✅ Critical holiday 2025-10-07 found in position-utils');
    } else {
      console.log('❌ Critical holiday 2025-10-07 NOT found in position-utils');
    }
    
    // 4. Test consistency between APIs
    console.log('\n=== 4. Testing API Consistency ===');
    
    if (serverHolidays.length === positionHolidays.length) {
      console.log('✅ Holiday count matches between server-utils and position-utils');
    } else {
      console.log(`❌ Holiday count mismatch: server=${serverHolidays.length}, position=${positionHolidays.length}`);
    }
    
    // Check if all dates match
    let allDatesMatch = true;
    for (let i = 0; i < Math.min(serverHolidays.length, positionHolidays.length); i++) {
      if (serverHolidays[i].getTime() !== positionHolidays[i].getTime()) {
        allDatesMatch = false;
        console.log(`❌ Date mismatch at index ${i}: server=${serverHolidays[i].toISOString().split('T')[0]}, position=${positionHolidays[i].toISOString().split('T')[0]}`);
      }
    }
    
    if (allDatesMatch && serverHolidays.length === positionHolidays.length) {
      console.log('✅ All holiday dates match between APIs');
    }
    
    // 5. Test trading days calculation
    console.log('\n=== 5. Testing Trading Days Calculation ===');
    
    const testDate = new Date(2025, 9, 7, 12, 0, 0, 0); // 2025-10-07 at noon
    const referenceDate = new Date(2025, 9, 6, 12, 0, 0, 0); // 2025-10-06 at noon
    
    const daysToExpiry = positionUtils.calculateDaysToExpiry('2025-10-07', referenceDate);
    console.log(`📊 Trading days from 2025-10-06 to 2025-10-07: ${daysToExpiry}`);
    
    if (daysToExpiry === 0) {
      console.log('✅ 2025-10-07 correctly treated as holiday (0 trading days)');
    } else {
      console.log(`❌ 2025-10-07 incorrectly treated as trading day (${daysToExpiry} days)`);
    }
    
    // 6. Test Black-Scholes integration
    console.log('\n=== 6. Testing Black-Scholes Integration ===');
    
    const testPosition = {
      type: 'Call',
      strike: 20000,
      quantity: 1,
      ExpiryDate: '2025-10-07',
      debitCredit: 500
    };
    
    const stockPrice = 20100;
    const riskFreeRate = 2;
    const volatility = 30;
    
    const premium = positionUtils.calculatePositionPremium(testPosition, stockPrice, riskFreeRate, volatility);
    console.log(`💰 Option premium on 2025-10-07 (holiday): ${premium}`);
    
    // 7. Performance test
    console.log('\n=== 7. Performance Test ===');
    
    const iterations = 100;
    const perfStartTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      positionUtils.loadExchangeHolidays();
    }
    
    const perfEndTime = Date.now();
    const avgTime = (perfEndTime - perfStartTime) / iterations;
    
    console.log(`🚀 Average load time: ${avgTime.toFixed(2)}ms (${iterations} iterations)`);
    
    if (avgTime < 5) {
      console.log('✅ Performance: Excellent (< 5ms)');
    } else if (avgTime < 50) {
      console.log('✅ Performance: Good (< 50ms)');
    } else {
      console.log('⚠️ Performance: Needs improvement (> 50ms)');
    }
    
    // 8. Test environment variables
    console.log('\n=== 8. Testing Environment Configuration ===');
    
    const originalOutputPath = process.env.OUTPUT_PATH;
    
    // Test with custom output path
    process.env.OUTPUT_PATH = 'test-output';
    delete require.cache[require.resolve('../src/utils/config.js')];
    const testConfig = require('../src/utils/config.js');
    
    const customOutputPath = testConfig.getOutputPath();
    console.log(`🔧 Custom output path: ${customOutputPath}`);
    
    if (customOutputPath.includes('test-output')) {
      console.log('✅ Environment variable OUTPUT_PATH working correctly');
    } else {
      console.log('❌ Environment variable OUTPUT_PATH not working');
    }
    
    // Restore original environment
    if (originalOutputPath) {
      process.env.OUTPUT_PATH = originalOutputPath;
    } else {
      delete process.env.OUTPUT_PATH;
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    console.log(`\n🎯 Total test time: ${totalTime}ms`);
    console.log('✅ Refactored holiday loading system test completed successfully!');
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run the test
if (require.main === module) {
  testRefactoredSystem().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testRefactoredSystem };