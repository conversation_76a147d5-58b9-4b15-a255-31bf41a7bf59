import React, { useState, useEffect, useMemo } from 'react';
import useDatabaseStore from '../../store/useDatabaseStore';
import DatabaseOfflineIndicator from '../database-offline-indicator';

/**
 * All Trades List Component
 * 
 * This component displays all trades from the database without any filtering.
 */
const AllTradesList = () => {
  // Access database status context
  const { isOffline } = useDatabaseStore();

  // State for trades
  const [trades, setTrades] = useState([]);
  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  // State for error message
  const [error, setError] = useState(null);
  // State for stocks
  const [stocks, setStocks] = useState([]);
  // State for expiry dates
  const [expiryDates, setExpiryDates] = useState([]);
  // State for selected filters
  const [selectedStock, setSelectedStock] = useState('ALL');
  const [selectedExpiry, setSelectedExpiry] = useState('ALL');

  // Filter trades based on selected stock and expiry
  const filteredTrades = useMemo(() => {
    return trades.filter(trade => {
      // Extract stock from ticker (first 3 characters) if no stock field
      const tradeStock = trade.stock || (trade.ticker ? trade.ticker.substring(0, 3) : null);
      const stockMatch = selectedStock === 'ALL' || tradeStock === selectedStock;
      const expiryMatch = selectedExpiry === 'ALL' || trade.ExpiryDate === selectedExpiry;
      return stockMatch && expiryMatch;
    });
  }, [trades, selectedStock, selectedExpiry]);

  // Fetch all stocks
  useEffect(() => {
    const fetchStocks = async () => {
      try {
        setIsLoading(true);
        
        // Get all stocks from the API
        const response = await fetch('/api/firebase-stocks');
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('All stocks:', data.stocks);
        setStocks(data.stocks || []);
        
        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOnline();
      } catch (error) {
        console.error('Error fetching stocks:', error);
        setError('Failed to fetch stocks');
        
        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOffline(error);
      }
    };

    fetchStocks();
  }, []);

  // Fetch all trades for each stock and expiry date
  useEffect(() => {
    const fetchAllTrades = async () => {
      if (stocks.length === 0) {
        setIsLoading(false);
        return;
      }

      try {
        const allTrades = [];
        const allExpiryDates = new Set();
        
        // For each stock, fetch the expiry dates
        for (const stockCode of stocks) {
          try {
            const expiryResponse = await fetch(`/api/firebase-expiry-dates?stock=${stockCode}`);
            
            if (!expiryResponse.ok) {
              console.error(`Error fetching expiry dates for stock ${stockCode}`);
              continue;
            }
            
            const expiryData = await expiryResponse.json();
            console.log(`Expiry dates for ${stockCode}:`, expiryData.expiryDates);
            
            // Add expiry dates to the set
            expiryData.expiryDates.forEach(date => allExpiryDates.add(date));
            
            // For each expiry date, fetch the trades
            for (const expiryDate of expiryData.expiryDates) {
              try {
                const tradesResponse = await fetch(`/api/firebase-trades-by-expiry?expiryDate=${expiryDate}&stock=${stockCode}`);
                
                if (!tradesResponse.ok) {
                  console.error(`Error fetching trades for stock ${stockCode} and expiry ${expiryDate}`);
                  continue;
                }
                
                const tradesData = await tradesResponse.json();
                console.log(`Trades for ${stockCode} with expiry ${expiryDate}:`, tradesData.trades);
                
                // Add trades to the array
                allTrades.push(...tradesData.trades);
              } catch (error) {
                console.error(`Error fetching trades for stock ${stockCode} and expiry ${expiryDate}:`, error);
              }
            }
          } catch (error) {
            console.error(`Error fetching expiry dates for stock ${stockCode}:`, error);
          }
        }
        
        console.log('All trades:', allTrades);
        console.log('All expiry dates:', Array.from(allExpiryDates));
        
        // Sort expiry dates
        const sortedDates = Array.from(allExpiryDates).sort((a, b) => {
          const dateA = new Date(a.includes('-') && a.length === 7 ? `${a}-01` : a);
          const dateB = new Date(b.includes('-') && b.length === 7 ? `${b}-01` : b);
          
          if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
            return dateA.getTime() - dateB.getTime();
          }
          return a.localeCompare(b);
        });
        
        setExpiryDates(sortedDates);
        setTrades(allTrades);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching all trades:', error);
        setError('Failed to fetch trades');
        setIsLoading(false);
      }
    };

    fetchAllTrades();
  }, [stocks]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const parts = dateString.split('-');
    if (parts.length >= 3) {
      const month = parseInt(parts[1]);
      const day = parseInt(parts[2]);
      return `${month}/${day}`;
    }
    return dateString;
  };

  return (
    <div className="space-y-8">
      <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold text-gray-800">All Trades (No Filtering)</h2>
          <DatabaseOfflineIndicator />
        </div>

        {isLoading ? (
          <div className="text-center py-4">
            <p className="text-gray-500">Loading trades...</p>
          </div>
        ) : error ? (
          <div className="text-center py-4">
            <p className="text-red-500">{error}</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <div className="p-4 border-b border-gray-200">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Stock</label>
                  <select
                    value={selectedStock}
                    onChange={(e) => setSelectedStock(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  >
                    <option value="ALL">All Stocks</option>
                    {stocks.map((stock) => (
                      <option key={stock} value={stock}>{stock}</option>
                    ))}
                  </select>
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                  <select
                    value={selectedExpiry}
                    onChange={(e) => setSelectedExpiry(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  >
                    <option value="ALL">All Expiry Dates</option>
                    {Array.from(expiryDates).sort().map((date) => (
                      <option key={date} value={date}>{date}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            <div className="h-[600px] overflow-y-auto border rounded-md">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ticker
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Stock
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expiry
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Strike
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Uploaded At
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTrades.length === 0 ? (
                    <tr>
                      <td colSpan="7" className="px-3 py-4 text-center text-sm text-gray-500">
                        No trades found matching the selected filters.
                      </td>
                    </tr>
                  ) : (
                    filteredTrades.map((trade) => (
                      <tr key={trade.id} className={
                        trade.type === 'Future' || trade.type === 'future' || 
                        (trade.ticker && trade.ticker.length <= 5 && trade.strike === 'N/A') ? 'bg-yellow-50' :
                        trade.type === 'Call' || trade.type === 'call' ? 'bg-cyan-50' :
                        trade.type === 'Put' || trade.type === 'put' ? 'bg-red-50' :
                        'bg-gray-50'
                      }>
                        <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900" title={trade.ticker}>
                          {trade.ticker}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500" title={trade.stock}>
                          {trade.stock}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.type}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.ExpiryDate || trade.expiry}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.strike === 'N/A' || (trade.ticker && trade.ticker.length <= 5) ? 'N/A' : trade.strike}
                        </td>
                        <td className={`px-3 py-2 whitespace-nowrap text-sm text-right ${trade.quantity < 0 ? 'text-red-500' : 'text-green-600'}`}>
                          {trade.quantity}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {trade.premium != null ? trade.premium.toFixed(2) : 'N/A'}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.created_at ? new Date(trade.created_at).toLocaleDateString() : 'N/A'}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Showing {filteredTrades.length} trade{filteredTrades.length === 1 ? "" : "s"} (Total: {trades.length}).
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AllTradesList;
