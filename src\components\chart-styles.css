/* Chart container styles */
.chart-container {
  /* Prevent scrolling */
  touch-action: none;
  -ms-touch-action: none;
  -webkit-overflow-scrolling: none;
  overflow: hidden;
}

/* No scroll class */
.no-scroll {
  overflow: hidden;
  touch-action: none;
}

/* Prevent text selection in charts */
.chart-container * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Cursor styles for panning */
.chart-container.can-pan {
  cursor: grab;
}

.chart-container.panning {
  cursor: grabbing;
}

/* Disable text selection during panning */
.panning-active {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}
