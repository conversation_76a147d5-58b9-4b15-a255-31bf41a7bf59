import React, { useState, useEffect } from 'react';

/**
 * Future Trades Debug Component
 * 
 * This component specifically looks for future trades in the database and displays detailed debugging information.
 */
const FutureTradesDebug = () => {
  // State for raw API response
  const [apiResponse, setApiResponse] = useState(null);
  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  // State for error message
  const [error, setError] = useState(null);
  // State for debug logs
  const [logs, setLogs] = useState([]);

  // Add a log message
  const addLog = (message) => {
    setLogs(prevLogs => [...prevLogs, { time: new Date().toISOString(), message }]);
  };

  // Fetch data directly from the server
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        addLog('Starting debug process');
        
        // Step 1: Get all stocks
        addLog('Step 1: Fetching all stocks');
        const stocksResponse = await fetch('/api/firebase-stocks');
        
        if (!stocksResponse.ok) {
          throw new Error(`HTTP error! Status: ${stocksResponse.status}`);
        }
        
        const stocksData = await stocksResponse.json();
        addLog(`Found ${stocksData.stocks.length} stocks: ${stocksData.stocks.join(', ')}`);
        
        // Step 2: Check for HTI stock
        if (!stocksData.stocks.includes('HTI')) {
          addLog('ERROR: HTI stock not found in the list of stocks');
          throw new Error('HTI stock not found');
        }
        
        addLog('HTI stock found in the list of stocks');
        
        // Step 3: Get expiry dates for HTI
        addLog('Step 3: Fetching expiry dates for HTI');
        const expiryResponse = await fetch('/api/firebase-expiry-dates?stock=HTI');
        
        if (!expiryResponse.ok) {
          throw new Error(`HTTP error! Status: ${expiryResponse.status}`);
        }
        
        const expiryData = await expiryResponse.json();
        addLog(`Found ${expiryData.expiryDates.length} expiry dates for HTI: ${expiryData.expiryDates.join(', ')}`);
        
        // Step 4: Check for 2025-06 expiry date
        if (!expiryData.expiryDates.includes('2025-06')) {
          addLog('ERROR: 2025-06 expiry date not found for HTI');
          throw new Error('2025-06 expiry date not found for HTI');
        }
        
        addLog('2025-06 expiry date found for HTI');
        
        // Step 5: Get trades for HTI with expiry 2025-06
        addLog('Step 5: Fetching trades for HTI with expiry 2025-06');
        const tradesResponse = await fetch('/api/firebase-trades-by-expiry?expiryDate=2025-06&stock=HTI');
        
        if (!tradesResponse.ok) {
          throw new Error(`HTTP error! Status: ${tradesResponse.status}`);
        }
        
        const tradesData = await tradesResponse.json();
        addLog(`Found ${tradesData.trades.length} trades for HTI with expiry 2025-06`);
        
        // Step 6: Check for future trades
        const futureTrades = tradesData.trades.filter(trade => 
          trade.type === 'Future' || 
          trade.type === 'future' || 
          trade.strike === 'N/A' || 
          (trade.ticker && trade.ticker.length <= 5)
        );
        
        addLog(`Found ${futureTrades.length} future trades for HTI with expiry 2025-06`);
        
        if (futureTrades.length === 0) {
          addLog('ERROR: No future trades found for HTI with expiry 2025-06');
        } else {
          addLog('Future trades found for HTI with expiry 2025-06:');
          futureTrades.forEach(trade => {
            addLog(`- Ticker: ${trade.ticker}, Type: ${trade.type}, Strike: ${trade.strike}`);
          });
        }
        
        // Step 7: Check for HTIM5 ticker
        const htim5Trades = tradesData.trades.filter(trade => trade.ticker === 'HTIM5');
        
        addLog(`Found ${htim5Trades.length} trades with ticker HTIM5`);
        
        if (htim5Trades.length === 0) {
          addLog('ERROR: No trades found with ticker HTIM5');
        } else {
          addLog('Trades found with ticker HTIM5:');
          htim5Trades.forEach(trade => {
            addLog(`- Type: ${trade.type}, Strike: ${trade.strike}, Expiry: ${trade.expiry || trade.ExpiryDate}`);
          });
        }
        
        // Step 8: Check all trades in the database
        addLog('Step 8: Checking all trades in the database');
        
        // For each stock, get all expiry dates and trades
        const allTrades = [];
        
        for (const stock of stocksData.stocks) {
          addLog(`Checking stock: ${stock}`);
          
          const stockExpiryResponse = await fetch(`/api/firebase-expiry-dates?stock=${stock}`);
          
          if (!stockExpiryResponse.ok) {
            addLog(`Error fetching expiry dates for stock ${stock}`);
            continue;
          }
          
          const stockExpiryData = await stockExpiryResponse.json();
          addLog(`Found ${stockExpiryData.expiryDates.length} expiry dates for ${stock}`);
          
          for (const expiryDate of stockExpiryData.expiryDates) {
            addLog(`Checking expiry date: ${expiryDate} for stock: ${stock}`);
            
            const stockTradesResponse = await fetch(`/api/firebase-trades-by-expiry?expiryDate=${expiryDate}&stock=${stock}`);
            
            if (!stockTradesResponse.ok) {
              addLog(`Error fetching trades for stock ${stock} and expiry ${expiryDate}`);
              continue;
            }
            
            const stockTradesData = await stockTradesResponse.json();
            addLog(`Found ${stockTradesData.trades.length} trades for ${stock} with expiry ${expiryDate}`);
            
            allTrades.push(...stockTradesData.trades);
          }
        }
        
        // Check for future trades in all trades
        const allFutureTrades = allTrades.filter(trade => 
          trade.type === 'Future' || 
          trade.type === 'future' || 
          trade.strike === 'N/A' || 
          (trade.ticker && trade.ticker.length <= 5)
        );
        
        addLog(`Found ${allFutureTrades.length} future trades in the entire database`);
        
        if (allFutureTrades.length === 0) {
          addLog('ERROR: No future trades found in the entire database');
        } else {
          addLog('Future trades found in the database:');
          allFutureTrades.forEach(trade => {
            addLog(`- Ticker: ${trade.ticker}, Type: ${trade.type}, Strike: ${trade.strike}, Expiry: ${trade.expiry || trade.ExpiryDate}`);
          });
        }
        
        // Save the raw API response for display
        setApiResponse(tradesData);
        setIsLoading(false);
      } catch (error) {
        console.error('Error during debug process:', error);
        addLog(`ERROR: ${error.message}`);
        setError(error.message);
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="space-y-8">
      <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
        <h2 className="text-lg font-bold text-gray-800 mb-4">Future Trades Debug</h2>
        
        {isLoading ? (
          <div className="text-center py-4">
            <p className="text-gray-500">Running diagnostics...</p>
          </div>
        ) : error ? (
          <div className="text-center py-4">
            <p className="text-red-500">Error: {error}</p>
          </div>
        ) : (
          <div>
            <h3 className="text-md font-semibold mb-2">Debug Logs:</h3>
            <div className="bg-gray-100 p-4 rounded mb-4 h-[300px] overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="mb-1">
                  <span className="text-gray-500 text-xs">{new Date(log.time).toLocaleTimeString()}</span>
                  <span className={`ml-2 text-sm ${log.message.startsWith('ERROR') ? 'text-red-500 font-bold' : ''}`}>
                    {log.message}
                  </span>
                </div>
              ))}
            </div>
            
            <h3 className="text-md font-semibold mb-2">Raw API Response for HTI 2025-06:</h3>
            <div className="bg-gray-100 p-4 rounded mb-4 h-[300px] overflow-y-auto">
              <pre className="text-xs">{JSON.stringify(apiResponse, null, 2)}</pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FutureTradesDebug;
