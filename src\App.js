import {
  BrowserRouter as Router,
  Routes,
  Route
} from 'react-router-dom';
// import OptionsStrategyAnalyzer from './components/options-analyzer';
// import OptionsTradesQueryPage from './pages/options-trades-query';
import TradeListPage from './pages/trade-list';
import TradeParserPage from './pages/trade-parser';
import StrategyPlannerPage from './pages/strategy-planner';
import StatementImportPage from './pages/statement-import';
// import APITestPage from './pages/api-test';
// import DirectTestPage from './pages/direct-test';
import AnalysisPage from './pages/analysis';
import GlobalErrorNotification from './components/global-error-notification';

// App Layout component
const AppLayout = () => {
  return (
    <div className="App bg-gray-50 min-h-screen py-8">
      <div className="container mx-auto px-4">
        {/* Global Error Notification */}
        <GlobalErrorNotification />

        <nav className="bg-white shadow-md rounded-lg mb-6 p-4">
          <ul className="flex space-x-6">
            <li>
              <a
                href="/"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = '/';
                }}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Home
              </a>
            </li>
            {/* <li>
              <a
                href="/trades-query"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = '/trades-query';
                }}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Trades Query
              </a>
            </li> */}
            <li>
              <a
                href="/trade-list"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = '/trade-list';
                }}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Trade List
              </a>
            </li>
            <li>
              <a
                href="/trade-parser"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = '/trade-parser';
                }}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Trade Parser
              </a>
            </li>
            <li>
              <a
                href="/strategy-planner"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = '/strategy-planner';
                }}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Strategy Planner
              </a>
            </li>
            <li>
              <a
                href="/statement-import"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = '/statement-import';
                }}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Statement Import
              </a>
            </li>
            {/* <li>
              <a
                href="/analysis"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = '/analysis';
                }}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Analysis
              </a>
            </li> */}
          </ul>
        </nav>

        <Routes>
          {/* <Route path="/" element={<OptionsStrategyAnalyzer />} /> */}
          <Route path="/" element={<AnalysisPage />} />
          {/* <Route path="/trades-query" element={<OptionsTradesQueryPage />} /> */}
          <Route path="/trade-list" element={<TradeListPage />} />
          <Route path="/trade-parser" element={<TradeParserPage />} />
          <Route path="/strategy-planner" element={<StrategyPlannerPage />} />
          <Route path="/statement-import" element={<StatementImportPage />} />
          {/* <Route path="/analysis" element={<AnalysisPage />} /> */}
          {/* <Route path="/api-test" element={<APITestPage />} /> */}
          {/* <Route path="/direct-test" element={<DirectTestPage />} /> */}
        </Routes>
      </div>
    </div>
  );
};

function App() {
  return (
    <Router>
      <AppLayout />
    </Router>
  );
}

export default App;