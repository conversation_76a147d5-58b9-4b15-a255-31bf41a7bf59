import React from 'react';
import useStrategyWindowStore from '../../store/useStrategyWindowStore';
import StrategyWindow from './StrategyWindow';

/**
 * Strategy Window Manager Component
 * 
 * Manages and renders all open strategy windows. Provides window
 * management controls and handles window lifecycle.
 */
const StrategyWindowManager = () => {
  const {
    openWindows,
    closeAllWindows,
    cascadeWindows,
    tileWindows,
    getWindowStats
  } = useStrategyWindowStore();

  const stats = getWindowStats();

  if (openWindows.length === 0) {
    return null;
  }

  return (
    <>
      {/* Window Management Controls */}
      {openWindows.length > 1 && (
        <div className="fixed top-4 right-4 z-40 bg-white border border-gray-300 rounded-lg shadow-lg p-3">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-600">
              {stats.openCount}/{stats.maxWindows} windows
            </span>
            
            <div className="border-l border-gray-300 pl-2 flex space-x-1">
              <button
                onClick={cascadeWindows}
                className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
                title="Cascade Windows"
              >
                📋
              </button>
              
              <button
                onClick={tileWindows}
                className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded"
                title="Tile Windows"
              >
                ⊞
              </button>
              
              <button
                onClick={closeAllWindows}
                className="px-2 py-1 text-xs bg-red-100 hover:bg-red-200 text-red-700 rounded"
                title="Close All Windows"
              >
                ✕
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Minimized Windows Bar */}
      {openWindows.some(w => w.isMinimized) && (
        <div className="fixed bottom-4 left-4 right-4 z-30">
          <div className="bg-white border border-gray-300 rounded-lg shadow-lg p-2">
            <div className="flex items-center space-x-2 overflow-x-auto">
              <span className="text-xs text-gray-600 whitespace-nowrap">Minimized:</span>
              {openWindows
                .filter(w => w.isMinimized)
                .map(window => (
                  <MinimizedWindowButton key={window.id} window={window} />
                ))}
            </div>
          </div>
        </div>
      )}

      {/* Strategy Windows */}
      {openWindows.map(window => (
        <StrategyWindow key={window.id} windowId={window.id} />
      ))}
    </>
  );
};

/**
 * Minimized Window Button Component
 */
const MinimizedWindowButton = ({ window }) => {
  const { restoreWindow, closeWindow } = useStrategyWindowStore();

  const handleRestore = () => {
    restoreWindow(window.id);
  };

  const handleClose = (e) => {
    e.stopPropagation();
    closeWindow(window.id);
  };

  return (
    <div className="flex items-center bg-gray-100 rounded px-2 py-1 text-xs whitespace-nowrap">
      <button
        onClick={handleRestore}
        className="flex items-center space-x-1 hover:text-blue-600"
        title={`Restore ${window.strategyName}`}
      >
        <span>📊</span>
        <span className="max-w-24 truncate">
          {window.strategyName || window.strategyId}
        </span>
        {window.stockSymbol && (
          <span className="text-gray-500">({window.stockSymbol})</span>
        )}
      </button>
      
      <button
        onClick={handleClose}
        className="ml-1 text-gray-400 hover:text-red-600"
        title="Close"
      >
        ✕
      </button>
    </div>
  );
};

export default StrategyWindowManager;
