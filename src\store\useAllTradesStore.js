// Zustand store for all trades fetched from Firestore
import { create } from 'zustand';

const useAllTradesStore = create((set, get) => ({ // Added get to access current state if needed
  allTrades: [],
  stocks: [], // Added stocks state
  expiryDates: [], // Added expiryDates state
  isLoading: true, // Added isLoading state
  error: null, // Added error state

  // Defensive: Only update if trades array reference or contents actually changed
  setAllTrades: (trades) => set(state => {
    if (
      state.allTrades === trades ||
      (Array.isArray(state.allTrades) && Array.isArray(trades) &&
        state.allTrades.length === trades.length &&
        state.allTrades.every((t, i) => t === trades[i])
      )
    ) {
      return {};
    }
    return { allTrades: trades };
  }),

  // Function to fetch all data
  fetchAllTradesData: async () => {
    set({ isLoading: true, error: null });
    try {
      // 1. Fetch all unique stock codes
      const stocksResponse = await fetch('/api/firebase-stocks');
      if (!stocksResponse.ok) throw new Error(`HTTP error! Status: ${stocksResponse.status} fetching stocks`);
      const stocksData = await stocksResponse.json();
      const uniqueStocks = stocksData.stocks || [];
      set({ stocks: uniqueStocks });

      if (uniqueStocks.length === 0) {
        set({ allTrades: [], expiryDates: [], isLoading: false });
        return;
      }

      // 2. Fetch all trades and expiry dates for these stocks
      const allTradesArr = [];
      const allExpiryDatesSet = new Set();

      for (const stockCode of uniqueStocks) {
        try {
          const expiryResponse = await fetch(`/api/firebase-expiry-dates?stock=${stockCode}`);
          if (!expiryResponse.ok) {
            console.warn(`Failed to fetch expiry dates for ${stockCode}: ${expiryResponse.status}`);
            continue; // Skip this stock if expiry dates can't be fetched
          }
          const expiryData = await expiryResponse.json();
          const stockExpiryDates = expiryData.expiryDates || [];
          stockExpiryDates.forEach(date => allExpiryDatesSet.add(date));

          for (const expiryDate of stockExpiryDates) {
            try {
              const tradesResponse = await fetch(`/api/firebase-trades-by-expiry?expiryDate=${expiryDate}&stock=${stockCode}`);
              if (!tradesResponse.ok) {
                console.warn(`Failed to fetch trades for ${stockCode} on ${expiryDate}: ${tradesResponse.status}`);
                continue; // Skip this stock/expiry if trades can't be fetched
              }
              const tradesData = await tradesResponse.json();
              if (tradesData.trades && tradesData.trades.length > 0) {
                allTradesArr.push(...tradesData.trades);
              }
            } catch (tradeError) {
              console.error(`Error fetching trades for ${stockCode} on ${expiryDate}:`, tradeError);
            }
          }
        } catch (expiryError) {
          console.error(`Error fetching expiry dates for ${stockCode}:`, expiryError);
        }
      }

      const sortedDates = Array.from(allExpiryDatesSet).sort((a, b) => {
        // Handle YYYY-MM and YYYY-MM-DD formats for sorting
        const dateA = new Date(a.includes('-') && a.length === 7 ? `${a}-01` : a);
        const dateB = new Date(b.includes('-') && b.length === 7 ? `${b}-01` : b);
        if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
          return dateA.getTime() - dateB.getTime();
        }
        return a.localeCompare(b); // Fallback for non-standard dates
      });

      set({ 
        allTrades: allTradesArr, 
        expiryDates: sortedDates, 
        isLoading: false, 
        error: null 
      });

    } catch (error) {
      console.error("Failed to fetch all trades data:", error);
      set({ error: error.message || 'Failed to fetch data', isLoading: false, stocks: [], allTrades: [], expiryDates: [] });
    }
  },
}));

export default useAllTradesStore;
