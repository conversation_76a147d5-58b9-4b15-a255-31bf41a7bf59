import { create } from 'zustand';
import { StrategyTypes } from '../types/strategy';

/**
 * Strategy Management Store
 * 
 * This Zustand store manages the list of strategies and provides CRUD operations.
 * It serves as the main data source for the Strategy Management Table.
 */
const useStrategyManagementStore = create((set, get) => ({
  // State
  strategies: [],
  isLoading: false,
  error: null,
  filters: {
    search: '',
    stockSymbol: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    page: 1,
    pageSize: 20
  },

  // Actions
  setLoading: (loading) => set({ isLoading: loading }),
  
  setError: (error) => set({ error }),
  
  clearError: () => set({ error: null }),

  // Load all strategies
  loadStrategies: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await fetch('/api/strategies');
      if (!response.ok) {
        throw new Error(`Failed to load strategies: ${response.statusText}`);
      }
      
      const strategies = await response.json();
      
      // Convert date strings to Date objects
      const processedStrategies = strategies.map(strategy => ({
        ...strategy,
        createdAt: new Date(strategy.createdAt),
        lastModified: new Date(strategy.lastModified)
      }));
      
      set({ strategies: processedStrategies, isLoading: false });
    } catch (error) {
      console.error('Error loading strategies:', error);
      set({ error: error.message, isLoading: false });
    }
  },

  // Create new strategy
  createStrategy: async (strategyData) => {
    set({ isLoading: true, error: null });
    
    try {
      const newStrategy = {
        ...StrategyTypes.DEFAULT_STRATEGY,
        ...strategyData,
        id: StrategyTypes.generateStrategyId(),
        createdAt: new Date(),
        lastModified: new Date()
      };
      
      const response = await fetch('/api/strategies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newStrategy),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create strategy: ${response.statusText}`);
      }
      
      const createdStrategy = await response.json();
      
      // Convert date strings to Date objects
      const processedStrategy = {
        ...createdStrategy,
        createdAt: new Date(createdStrategy.createdAt),
        lastModified: new Date(createdStrategy.lastModified)
      };
      
      set(state => ({
        strategies: [processedStrategy, ...state.strategies],
        isLoading: false
      }));
      
      return processedStrategy;
    } catch (error) {
      console.error('Error creating strategy:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Update existing strategy
  updateStrategy: async (strategyId, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedData = {
        ...updates,
        lastModified: new Date()
      };
      
      const response = await fetch(`/api/strategies/${strategyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedData),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update strategy: ${response.statusText}`);
      }
      
      const updatedStrategy = await response.json();
      
      // Convert date strings to Date objects
      const processedStrategy = {
        ...updatedStrategy,
        createdAt: new Date(updatedStrategy.createdAt),
        lastModified: new Date(updatedStrategy.lastModified)
      };
      
      set(state => ({
        strategies: state.strategies.map(strategy =>
          strategy.id === strategyId ? processedStrategy : strategy
        ),
        isLoading: false
      }));
      
      return processedStrategy;
    } catch (error) {
      console.error('Error updating strategy:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Delete strategy
  deleteStrategy: async (strategyId) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await fetch(`/api/strategies/${strategyId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete strategy: ${response.statusText}`);
      }
      
      set(state => ({
        strategies: state.strategies.filter(strategy => strategy.id !== strategyId),
        isLoading: false
      }));
    } catch (error) {
      console.error('Error deleting strategy:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Delete multiple strategies
  deleteStrategies: async (strategyIds) => {
    set({ isLoading: true, error: null });
    
    try {
      const deletePromises = strategyIds.map(id =>
        fetch(`/api/strategies/${id}`, { method: 'DELETE' })
      );
      
      const responses = await Promise.all(deletePromises);
      
      // Check if all deletions were successful
      const failedDeletions = responses.filter(response => !response.ok);
      if (failedDeletions.length > 0) {
        throw new Error(`Failed to delete ${failedDeletions.length} strategies`);
      }
      
      set(state => ({
        strategies: state.strategies.filter(strategy => !strategyIds.includes(strategy.id)),
        isLoading: false
      }));
    } catch (error) {
      console.error('Error deleting strategies:', error);
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Update filters
  updateFilters: (newFilters) => {
    set(state => ({
      filters: { ...state.filters, ...newFilters }
    }));
  },

  // Get filtered and sorted strategies
  getFilteredStrategies: () => {
    const { strategies, filters } = get();
    
    let filtered = [...strategies];
    
    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(strategy =>
        strategy.name.toLowerCase().includes(searchLower) ||
        strategy.stockSymbol.toLowerCase().includes(searchLower) ||
        strategy.description?.toLowerCase().includes(searchLower) ||
        strategy.id.toLowerCase().includes(searchLower)
      );
    }
    
    // Apply stock symbol filter
    if (filters.stockSymbol) {
      filtered = filtered.filter(strategy =>
        strategy.stockSymbol === filters.stockSymbol
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[filters.sortBy];
      const bValue = b[filters.sortBy];
      
      if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
    
    return filtered;
  },

  // Get paginated strategies
  getPaginatedStrategies: () => {
    const filtered = get().getFilteredStrategies();
    const { filters } = get();
    
    const startIndex = (filters.page - 1) * filters.pageSize;
    const endIndex = startIndex + filters.pageSize;
    
    return {
      strategies: filtered.slice(startIndex, endIndex),
      totalCount: filtered.length,
      totalPages: Math.ceil(filtered.length / filters.pageSize),
      currentPage: filters.page,
      pageSize: filters.pageSize
    };
  },

  // Validate strategy data
  validateStrategy: (strategyData) => {
    const errors = {};
    
    if (!strategyData.name || strategyData.name.trim().length === 0) {
      errors.name = 'Strategy name is required';
    } else if (strategyData.name.length > StrategyTypes.VALIDATION_RULES.NAME_MAX_LENGTH) {
      errors.name = `Strategy name must be less than ${StrategyTypes.VALIDATION_RULES.NAME_MAX_LENGTH} characters`;
    }
    
    if (!strategyData.stockSymbol || strategyData.stockSymbol.trim().length === 0) {
      errors.stockSymbol = 'Stock symbol is required';
    } else if (strategyData.stockSymbol.length > StrategyTypes.VALIDATION_RULES.STOCK_SYMBOL_MAX_LENGTH) {
      errors.stockSymbol = `Stock symbol must be less than ${StrategyTypes.VALIDATION_RULES.STOCK_SYMBOL_MAX_LENGTH} characters`;
    }
    
    if (strategyData.description && strategyData.description.length > StrategyTypes.VALIDATION_RULES.DESCRIPTION_MAX_LENGTH) {
      errors.description = `Description must be less than ${StrategyTypes.VALIDATION_RULES.DESCRIPTION_MAX_LENGTH} characters`;
    }
    
    // Check for duplicate names
    const { strategies } = get();
    const existingStrategy = strategies.find(s => 
      s.name.toLowerCase() === strategyData.name.toLowerCase() && 
      s.id !== strategyData.id
    );
    if (existingStrategy) {
      errors.name = 'A strategy with this name already exists';
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
}));

export default useStrategyManagementStore;
