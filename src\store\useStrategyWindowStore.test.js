import { act, renderHook } from '@testing-library/react';
import useStrategyWindowStore from './useStrategyWindowStore';

// Mock document.documentElement for viewport size
Object.defineProperty(document.documentElement, 'clientWidth', {
  writable: true,
  value: 1200,
});

Object.defineProperty(document.documentElement, 'clientHeight', {
  writable: true,
  value: 800,
});

describe('useStrategyWindowStore - Tile Functionality', () => {
  let store;

  beforeEach(() => {
    // Reset store before each test
    const { result } = renderHook(() => useStrategyWindowStore());
    store = result.current;
    
    // Clear any existing windows
    act(() => {
      store.closeAllWindows();
    });
  });

  test('should maximize single window when tileOrMaximize is called', () => {
    // Open a single window
    let windowId;
    act(() => {
      windowId = store.openWindow('strategy1', { name: 'Test Strategy 1' });
    });

    // Get initial window state
    const initialWindow = store.getWindow(windowId);
    expect(initialWindow.isMaximized).toBe(false);

    // Call tileOrMaximize - should maximize the single window
    act(() => {
      store.tileOrMaximize(windowId);
    });

    // Check that window is maximized
    const maximizedWindow = store.getWindow(windowId);
    expect(maximizedWindow.isMaximized).toBe(true);
    expect(maximizedWindow.size.width).toBe(1160); // 1200 - 40 padding
    expect(maximizedWindow.size.height).toBe(760); // 800 - 40 padding
    expect(maximizedWindow.position.x).toBe(20);
    expect(maximizedWindow.position.y).toBe(20);
  });

  test('should restore maximized window when tileOrMaximize is called again', () => {
    // Open and maximize a window
    let windowId;
    act(() => {
      windowId = store.openWindow('strategy1', { name: 'Test Strategy 1' });
      store.maximizeWindow(windowId);
    });

    const maximizedWindow = store.getWindow(windowId);
    expect(maximizedWindow.isMaximized).toBe(true);

    // Call tileOrMaximize again - should restore
    act(() => {
      store.tileOrMaximize(windowId);
    });

    const restoredWindow = store.getWindow(windowId);
    expect(restoredWindow.isMaximized).toBe(false);
    expect(restoredWindow.originalState).toBeUndefined();
  });

  test('should tile multiple windows when tileOrMaximize is called', () => {
    // Open multiple windows
    let windowId1, windowId2;
    act(() => {
      windowId1 = store.openWindow('strategy1', { name: 'Test Strategy 1' });
      windowId2 = store.openWindow('strategy2', { name: 'Test Strategy 2' });
    });

    // Call tileOrMaximize - should tile both windows
    act(() => {
      store.tileOrMaximize(windowId1);
    });

    const window1 = store.getWindow(windowId1);
    const window2 = store.getWindow(windowId2);

    // Both windows should be tiled (not maximized)
    expect(window1.isMaximized).toBe(false);
    expect(window2.isMaximized).toBe(false);

    // Windows should be positioned side by side (2 columns, 1 row for 2 windows)
    expect(window1.position.x).toBe(20); // First column
    expect(window2.position.x).toBe(600); // Second column (1200-40)/2 = 580 width + 20 padding
    expect(window1.position.y).toBe(20); // Same row
    expect(window2.position.y).toBe(20); // Same row

    // Windows should have equal sizes
    expect(window1.size.width).toBe(590); // (1200-40)/2 - 10 = 590
    expect(window2.size.width).toBe(590);
    expect(window1.size.height).toBe(770); // 800-40-10 = 750
    expect(window2.size.height).toBe(770);
  });

  test('should tile four windows in 2x2 grid', () => {
    // Open four windows
    let windowIds = [];
    act(() => {
      for (let i = 1; i <= 4; i++) {
        windowIds.push(store.openWindow(`strategy${i}`, { name: `Test Strategy ${i}` }));
      }
    });

    // Call tileOrMaximize
    act(() => {
      store.tileOrMaximize(windowIds[0]);
    });

    const windows = windowIds.map(id => store.getWindow(id));

    // All windows should be tiled in 2x2 grid
    // Top row
    expect(windows[0].position.x).toBe(20);
    expect(windows[0].position.y).toBe(20);
    expect(windows[1].position.x).toBe(600);
    expect(windows[1].position.y).toBe(20);
    
    // Bottom row
    expect(windows[2].position.x).toBe(20);
    expect(windows[2].position.y).toBe(400);
    expect(windows[3].position.x).toBe(600);
    expect(windows[3].position.y).toBe(400);

    // All windows should have same size
    windows.forEach(window => {
      expect(window.size.width).toBe(590);
      expect(window.size.height).toBe(390);
      expect(window.isMaximized).toBe(false);
    });
  });

  test('should not tile minimized windows', () => {
    // Open three windows and minimize one
    let windowIds = [];
    act(() => {
      for (let i = 1; i <= 3; i++) {
        windowIds.push(store.openWindow(`strategy${i}`, { name: `Test Strategy ${i}` }));
      }
      store.minimizeWindow(windowIds[2]); // Minimize third window
    });

    // Call tileOrMaximize
    act(() => {
      store.tileOrMaximize(windowIds[0]);
    });

    const window1 = store.getWindow(windowIds[0]);
    const window2 = store.getWindow(windowIds[1]);
    const window3 = store.getWindow(windowIds[2]);

    // Only visible windows should be tiled
    expect(window1.isMaximized).toBe(false);
    expect(window2.isMaximized).toBe(false);
    expect(window3.isMinimized).toBe(true);

    // First two windows should be tiled side by side
    expect(window1.position.x).toBe(20);
    expect(window2.position.x).toBe(600);
    expect(window1.position.y).toBe(20);
    expect(window2.position.y).toBe(20);
  });

  test('should handle maximizeWindow function correctly', () => {
    // Open a window
    let windowId;
    act(() => {
      windowId = store.openWindow('strategy1', { name: 'Test Strategy 1' });
    });

    const originalWindow = store.getWindow(windowId);
    const originalPosition = { ...originalWindow.position };
    const originalSize = { ...originalWindow.size };

    // Maximize the window
    act(() => {
      store.maximizeWindow(windowId);
    });

    const maximizedWindow = store.getWindow(windowId);
    expect(maximizedWindow.isMaximized).toBe(true);
    expect(maximizedWindow.originalState).toEqual({
      position: originalPosition,
      size: originalSize
    });

    // Restore the window
    act(() => {
      store.restoreWindowSize(windowId);
    });

    const restoredWindow = store.getWindow(windowId);
    expect(restoredWindow.isMaximized).toBe(false);
    expect(restoredWindow.position).toEqual(originalPosition);
    expect(restoredWindow.size).toEqual(originalSize);
    expect(restoredWindow.originalState).toBeUndefined();
  });
});
