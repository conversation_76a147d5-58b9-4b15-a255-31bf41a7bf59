/**
 * Robust Black-Scholes Option Pricing Model
 *
 * This module provides functions to calculate option prices using the Black-Scholes model
 * with enhanced handling of edge cases, extreme values, and invalid inputs.
 */



// Standard normal cumulative distribution function (<PERSON> et al. rational approximation)
function normalCDF(x) {
  const sign = x < 0 ? -1 : 1;
  x = Math.abs(x);
  if (x > 37) {
    return sign > 0 ? 1 : 0;
  }
  const exp_val = Math.exp(-0.5 * x * x);
  let cdf;
  if (x < 7.07106781186547) {
    const num = ((((((0.0352624965998911 * x + 0.700383064443688) * x +
                    6.37396220353165) * x + 33.912866078383) * x +
                   112.079291497871) * x + 221.213596169931) * x +
                  220.206867912376);
    const den = (((((((0.0883883476483184 * x + 1.75566716318264) * x +
                     16.064177579207) * x + 86.7807322029461) * x +
                    296.564248779674) * x + 637.333633378831) * x +
                   793.826512519948) * x + 440.413735824752);
    cdf = 1 - exp_val * num / den;
  } else {
    const num = (((((0.00565362490251678 * x + 0.256345893712771) * x +
                   4.42670760699551) * x + 34.2421788644669) * x +
                  122.706522983818) * x + 183.471030643998);
    const den = ((((((0.0151339245098523 * x + 0.683471827167415) * x +
                    12.4194596690635) * x + 107.780399473888) * x +
                   495.225979108646) * x + 1214.46988320507) * x +
                  1324.14529748068);
    cdf = 1 - exp_val * num / den;
  }
  return sign > 0 ? cdf : 1 - cdf;
}

// Standard normal probability density function
function normalPDF(x) {
  // Handle edge cases
  if (x === null || x === undefined || isNaN(x)) return 0;
  if (x === Infinity || x === -Infinity) return 0;

  return Math.exp(-0.5 * x * x) / Math.sqrt(2 * Math.PI);
}

/**
 * Validate and sanitize input parameters
 *
 * @param {string} type - Option type
 * @param {number} S - Current stock price
 * @param {number} K - Strike price
 * @param {number} r - Risk-free interest rate
 * @param {number} v - Volatility
 * @param {number} T - Time to expiration
 * @returns {Object} Object containing validated parameters and validation status
 */
function validateInputs(type, S, K, r, v, T) {
  const result = {
    isValid: true,
    message: '',
    params: {
      type: type,
      S: S,
      K: K,
      r: r,
      v: v,
      T: T
    }
  };

  // Validate option type
  if (!type) {
    result.params.type = 'Call'; // Default to Call if not specified
    result.message += 'Option type not specified, defaulting to Call. ';
  } else if (typeof type !== 'string') {
    result.params.type = 'Call'; // Default to Call if not a string
    result.message += `Invalid option type (${type}), defaulting to Call. `;
  } else {
    // Normalize option type (case-insensitive)
    const normalizedType = type.trim().toLowerCase();
    if (normalizedType === 'call' || normalizedType === 'c') {
      result.params.type = 'Call';
    } else if (normalizedType === 'put' || normalizedType === 'p') {
      result.params.type = 'Put';
    } else {
      result.params.type = 'Call'; // Default to Call for unrecognized types
      result.message += `Unrecognized option type (${type}), defaulting to Call. `;
    }
  }

  // Validate stock price (S)
  if (S === null || S === undefined || isNaN(S)) {
    result.params.S = 0;
    result.message += 'Stock price is invalid, defaulting to 0. ';
  } else if (S < 0) {
    result.params.S = 0;
    result.message += 'Negative stock price, defaulting to 0. ';
  } else if (!isFinite(S)) {
    result.params.S = Number.MAX_SAFE_INTEGER;
    result.message += 'Infinite stock price, using maximum safe value. ';
  }

  // Validate strike price (K)
  if (K === 'N/A') {
    // Special case for Future trades
    result.params.K = result.params.S; // Use stock price for 'N/A' strikes
    result.message += 'Strike is "N/A" (likely a Future), using stock price. ';
  } else if (K === null || K === undefined || isNaN(K)) {
    result.params.K = result.params.S; // Default to current stock price
    result.message += 'Strike price is invalid, defaulting to current stock price. ';
  } else if (K < 0) {
    result.params.K = 0;
    result.message += 'Negative strike price, defaulting to 0. ';
  } else if (!isFinite(K)) {
    result.params.K = Number.MAX_SAFE_INTEGER;
    result.message += 'Infinite strike price, using maximum safe value. ';
  }

  // Validate risk-free rate (r)
  if (r === null || r === undefined || isNaN(r)) {
    result.params.r = 0;
    result.message += 'Risk-free rate is invalid, defaulting to 0. ';
  } else if (!isFinite(r)) {
    if (r > 0) {
      result.params.r = 1; // Cap at 100%
      result.message += 'Infinite positive risk-free rate, capping at 100%. ';
    } else {
      result.params.r = -1; // Floor at -100%
      result.message += 'Infinite negative risk-free rate, flooring at -100%. ';
    }
  } else if (r > 1) {
    result.params.r = 1; // Cap at 100%
    result.message += `Risk-free rate (${r}) exceeds 100%, capping at 100%. `;
  } else if (r < -1) {
    result.params.r = -1; // Floor at -100%
    result.message += `Risk-free rate (${r}) below -100%, flooring at -100%. `;
  }

  // Validate volatility (v)
  if (v === null || v === undefined || isNaN(v)) {
    result.params.v = 0.2; // Default to 20%
    result.message += 'Volatility is invalid, defaulting to 20%. ';
  } else if (v < 0) {
    result.params.v = 0;
    result.message += 'Negative volatility, defaulting to 0. ';
  } else if (!isFinite(v)) {
    result.params.v = 2; // Cap at 200%
    result.message += 'Infinite volatility, capping at 200%. ';
  } else if (v > 2) {
    result.params.v = 2; // Cap at 200%
    result.message += `Volatility (${v}) exceeds 200%, capping at 200%. `;
  }

  // Validate time to expiration (T)
  if (T === null || T === undefined || isNaN(T)) {
    result.params.T = 0;
    result.message += 'Time to expiration is invalid, defaulting to 0. ';
  } else if (T < 0) {
    result.params.T = 0;
    result.message += 'Negative time to expiration, defaulting to 0. ';
  } else if (!isFinite(T)) {
    result.params.T = 30; // Cap at 30 years
    result.message += 'Infinite time to expiration, capping at 30 years. ';
  } else if (T > 30) {
    result.params.T = 30; // Cap at 30 years
    result.message += `Time to expiration (${T}) exceeds 30 years, capping at 30 years. `;
  }

  return result;
}

/**
 * Calculate d1 and d2 parameters for Black-Scholes with edge case handling
 *
 * @param {number} S - Current stock price
 * @param {number} K - Strike price
 * @param {number} r - Risk-free interest rate (in decimal form, e.g., 0.05 for 5%)
 * @param {number} v - Volatility (in decimal form, e.g., 0.2 for 20%)
 * @param {number} T - Time to expiration (in years)
 * @returns {Object} Object containing d1 and d2 values
 */
function calculateD1D2(S, K, r, v, T) {
  // Handle special cases
  if (T <= 0) return { d1: 0, d2: 0 };
  if (v <= 0) return { d1: S > K ? Infinity : -Infinity, d2: S > K ? Infinity : -Infinity };
  if (S <= 0 && K <= 0) return { d1: 0, d2: 0 };
  if (S <= 0) return { d1: -Infinity, d2: -Infinity };
  if (K <= 0) return { d1: Infinity, d2: Infinity };

  try {
    const d1 = (Math.log(S / K) + (r + 0.5 * v * v) * T) / (v * Math.sqrt(T));
    const d2 = d1 - v * Math.sqrt(T);

    // Check for NaN or Infinity
    if (!isFinite(d1) || !isFinite(d2)) {
      if (S > K) return { d1: Infinity, d2: Infinity };
      if (S < K) return { d1: -Infinity, d2: -Infinity };
      return { d1: 0, d2: 0 };
    }

    return { d1, d2 };
  } catch (error) {
    // Fallback for any unexpected errors
    return { d1: 0, d2: 0 };
  }
}

/**
 * Calculate call option price using Black-Scholes model with edge case handling
 *
 * @param {number} S - Current stock price
 * @param {number} K - Strike price
 * @param {number} r - Risk-free interest rate (in decimal form, e.g., 0.05 for 5%)
 * @param {number} v - Volatility (in decimal form, e.g., 0.2 for 20%)
 * @param {number} T - Time to expiration (in years)
 * @returns {number} Call option price
 */
function calculateCallPrice(S, K, r, v, T) {
  // Handle special cases
  if (T <= 0) {
    // Ensure S and K are numbers
    const stockPrice = typeof S === 'string' ? parseFloat(S) : S;
    const strikePrice = typeof K === 'string' ? parseFloat(K) : K;

    // Special case: At expiry, if stock price equals strike price, the option has no value
    if (Math.abs(stockPrice - strikePrice) < 0.01) {
      return 0;
    }

    const intrinsicValue = Math.max(0, stockPrice - strikePrice);
    return intrinsicValue; // At expiration, only intrinsic value remains
  }
  if (S <= 0) return 0; // Worthless if stock price is zero
  if (K <= 0) return S; // Worth full stock price if strike is zero
  if (v <= 0) return Math.max(0, S - K * Math.exp(-r * T)); // Zero volatility

  try {
    const { d1, d2 } = calculateD1D2(S, K, r, v, T);

    // Handle extreme d1/d2 values
    if (d1 === Infinity || d2 === Infinity) return S;
    if (d1 === -Infinity || d2 === -Infinity) return 0;

    const callPrice = S * normalCDF(d1) - K * Math.exp(-r * T) * normalCDF(d2);

    // Ensure non-negative price
    return Math.max(0, callPrice);
  } catch (error) {
    // Fallback for any unexpected errors
    return Math.max(0, S - K); // Return intrinsic value as fallback
  }
}

/**
 * Calculate put option price using Black-Scholes model with edge case handling
 *
 * @param {number} S - Current stock price
 * @param {number} K - Strike price
 * @param {number} r - Risk-free interest rate (in decimal form, e.g., 0.05 for 5%)
 * @param {number} v - Volatility (in decimal form, e.g., 0.2 for 20%)
 * @param {number} T - Time to expiration (in years)
 * @returns {number} Put option price
 */
function calculatePutPrice(S, K, r, v, T) {
  // Handle special cases
  if (T <= 0) {
    // Ensure S and K are numbers
    const stockPrice = typeof S === 'string' ? parseFloat(S) : S;
    const strikePrice = typeof K === 'string' ? parseFloat(K) : K;

    // Special case: At expiry, if stock price equals strike price, the option has no value
    if (Math.abs(stockPrice - strikePrice) < 0.01) {
      return 0;
    }

    const intrinsicValue = Math.max(0, strikePrice - stockPrice);
    return intrinsicValue; // At expiration, only intrinsic value remains
  }
  if (S <= 0) return K * Math.exp(-r * T); // Worth discounted strike if stock price is zero
  if (K <= 0) return 0; // Worthless if strike is zero
  if (v <= 0) return Math.max(0, K * Math.exp(-r * T) - S); // Zero volatility

  try {
    const { d1, d2 } = calculateD1D2(S, K, r, v, T);

    // Handle extreme d1/d2 values
    if (d1 === Infinity || d2 === Infinity) return 0;
    if (d1 === -Infinity || d2 === -Infinity) return K * Math.exp(-r * T);

    const putPrice = K * Math.exp(-r * T) * normalCDF(-d2) - S * normalCDF(-d1);

    // Ensure non-negative price
    return Math.max(0, putPrice);
  } catch (error) {
    // Fallback for any unexpected errors
    return Math.max(0, K - S); // Return intrinsic value as fallback
  }
}

/**
 * Calculate option price based on type (call or put) with robust error handling
 *
 * @param {string} type - Option type ('Call' or 'Put')
 * @param {number} S - Current stock price
 * @param {number} K - Strike price
 * @param {number} r - Risk-free interest rate (in decimal form, e.g., 0.05 for 5%)
 * @param {number} v - Volatility (in decimal form, e.g., 0.2 for 20%)
 * @param {number} T - Time to expiration (in years)
 * @returns {number} Option price
 */
function calculateOptionPrice(type, S, K, r, v, T) {
  // Validate and sanitize inputs
  const validation = validateInputs(type, S, K, r, v, T);

  // Use validated parameters
  const { type: validType, S: validS, K: validK, r: validR, v: validV, T: validT } = validation.params;

  try {
    if (validType === 'Call') {
      return calculateCallPrice(validS, validK, validR, validV, validT);
    } else {
      return calculatePutPrice(validS, validK, validR, validV, validT);
    }
  } catch (error) {
    // Fallback to intrinsic value
    if (validType === 'Call') {
      return Math.max(0, validS - validK);
    } else {
      return Math.max(0, validK - validS);
    }
  }
}

// Export functions as ES modules
export {
  normalCDF,
  normalPDF,
  calculateCallPrice,
  calculatePutPrice,
  calculateOptionPrice
};
