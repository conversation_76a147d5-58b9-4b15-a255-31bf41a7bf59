import React, { useState, useEffect } from 'react';
import useDatabaseStore from '../../store/useDatabaseStore';
import DatabaseOfflineIndicator from '../database-offline-indicator';

/**
 * Fixed Trade List Component
 * 
 * This component displays trades from the fixed API endpoint.
 */
const FixedTradeList = () => {
  // Access database status context
  const { isOffline } = useDatabaseStore();

  // State for trades
  const [trades, setTrades] = useState([]);
  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  // State for error message
  const [error, setError] = useState(null);
  // State for stocks
  const [stocks, setStocks] = useState([]);
  // State for selected stock
  const [selectedStock, setSelectedStock] = useState('HTI');
  // State for expiry dates
  const [expiryDates, setExpiryDates] = useState([]);
  // State for selected expiry date
  const [selectedExpiryDate, setSelectedExpiryDate] = useState('2025-06');

  // Fetch all stocks
  useEffect(() => {
    const fetchStocks = async () => {
      try {
        setIsLoading(true);
        
        // Get all stocks from the API
        const response = await fetch('/api/firebase-stocks');
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        setStocks(data.stocks || []);
        
        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOnline();
      } catch (error) {
        console.error('Error fetching stocks:', error);
        setError('Failed to fetch stocks');
        
        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOffline(error);
      }
    };

    fetchStocks();
  }, []);

  // Fetch expiry dates when stock changes
  useEffect(() => {
    const fetchExpiryDates = async () => {
      if (!selectedStock) {
        setExpiryDates([]);
        setSelectedExpiryDate('');
        setTrades([]);
        return;
      }

      try {
        setIsLoading(true);
        
        // Get expiry dates for the selected stock
        const response = await fetch(`/api/firebase-expiry-dates?stock=${selectedStock}`);
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Sort expiry dates
        const sortedDates = [...data.expiryDates].sort((a, b) => {
          const dateA = new Date(a.includes('-') && a.length === 7 ? `${a}-01` : a);
          const dateB = new Date(b.includes('-') && b.length === 7 ? `${b}-01` : b);
          
          if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
            return dateA.getTime() - dateB.getTime();
          }
          return a.localeCompare(b);
        });
        
        setExpiryDates(sortedDates);
        
        // If 2025-06 is in the list, select it by default
        if (sortedDates.includes('2025-06')) {
          setSelectedExpiryDate('2025-06');
        } else {
          setSelectedExpiryDate(sortedDates[0] || '');
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching expiry dates:', error);
        setError('Failed to fetch expiry dates');
        setIsLoading(false);
      }
    };

    fetchExpiryDates();
  }, [selectedStock]);

  // Fetch trades when expiry date changes
  useEffect(() => {
    const fetchTrades = async () => {
      if (!selectedStock || !selectedExpiryDate) {
        setTrades([]);
        return;
      }

      try {
        setIsLoading(true);
        
        // Get trades for the selected stock and expiry date
        const response = await fetch(`/api/firebase-trades-by-expiry?expiryDate=${selectedExpiryDate}&stock=${selectedStock}`);
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Trades from API:', data.trades);
        
        setTrades(data.trades || []);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching trades:', error);
        setError('Failed to fetch trades');
        setIsLoading(false);
      }
    };

    fetchTrades();
  }, [selectedExpiryDate, selectedStock]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const parts = dateString.split('-');
    if (parts.length >= 3) {
      const month = parseInt(parts[1]);
      const day = parseInt(parts[2]);
      return `${month}/${day}`;
    }
    return dateString;
  };

  return (
    <div className="space-y-8">
      <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold text-gray-800">Fixed Trade List</h2>
          <DatabaseOfflineIndicator />
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="bg-blue-100 p-2 rounded">
            <div className="font-bold text-xs mb-1">Stock</div>
            <select
              className="w-full p-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              value={selectedStock}
              onChange={(e) => setSelectedStock(e.target.value)}
              disabled={isLoading || isOffline}
            >
              <option value="">Select a stock</option>
              {stocks.map(stock => (
                <option key={stock} value={stock}>{stock}</option>
              ))}
            </select>
          </div>

          <div className="bg-blue-100 p-2 rounded">
            <div className="font-bold text-xs mb-1">Expiry Date</div>
            <select
              className="w-full p-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              value={selectedExpiryDate}
              onChange={(e) => setSelectedExpiryDate(e.target.value)}
              disabled={!selectedStock || isLoading || isOffline}
            >
              <option value="">Select an expiry date</option>
              {expiryDates.map(date => (
                <option key={date} value={date}>{formatDate(date)}</option>
              ))}
            </select>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-4">
            <p className="text-gray-500">Loading trades...</p>
          </div>
        ) : error ? (
          <div className="text-center py-4">
            <p className="text-red-500">{error}</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <div className="h-[400px] overflow-y-auto border rounded-md">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ticker
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expiry
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Strike
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {trades.length === 0 ? (
                    <tr>
                      <td colSpan="6" className="px-3 py-4 text-center text-sm text-gray-500">
                        No trades found for the selected filters.
                      </td>
                    </tr>
                  ) : (
                    trades.map((trade) => (
                      <tr key={trade.id} className={
                        trade.type === 'Future' || trade.type === 'future' ? 'bg-yellow-50' :
                        trade.type === 'Call' || trade.type === 'call' ? 'bg-cyan-50' :
                        trade.type === 'Put' || trade.type === 'put' ? 'bg-red-50' :
                        'bg-gray-50'
                      }>
                        <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                          {trade.ticker}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.type}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.ExpiryDate || trade.expiry}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.strike === 'N/A' || trade.type === 'Future' || trade.type === 'future' ? 'N/A' : trade.strike}
                        </td>
                        <td className={`px-3 py-2 whitespace-nowrap text-sm text-right ${trade.quantity < 0 ? 'text-red-500' : 'text-green-600'}`}>
                          {trade.quantity}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {trade.premium != null ? trade.premium.toFixed(2) : 'N/A'}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Showing {trades.length} trade{trades.length === 1 ? "" : "s"}.
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FixedTradeList;
