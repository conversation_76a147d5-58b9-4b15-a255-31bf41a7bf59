const statementParser = require('../src/server-utils/statement-parser');
const fs = require('fs');
const path = require('path');

async function testFixedWidthParsing() {
  console.log('🧪 TESTING FIXED-WIDTH PARSING APPROACH');
  console.log('=======================================');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    const password = '6369';
    
    console.log('📄 Parsing PDF...');
    const statement = await statementParser.parseStatement(pdfBuffer, password);
    
    console.log('\n📊 RESULTS SUMMARY:');
    console.log('Total trades extracted:', statement.tradeConfirmation.length);
    console.log('Expected: 13 trades');
    console.log(`Status: ${statement.tradeConfirmation.length === 13 ? '✅ PASS' : '❌ FAIL'}`);
    
    console.log('\n📋 DETAILED TRADE ANALYSIS:');
    console.log('=' .repeat(100));
    
    statement.tradeConfirmation.forEach((trade, idx) => {
      console.log(`\nTrade ${idx + 1}:`);
      console.log(`  📅 TRADE_DATE: ${trade.date}`);
      console.log(`  ⏰ N (Extended Hours): ${trade.extendedHours || 'blank'}`);
      console.log(`  🔢 ORDER_NO: ${trade.orderNo}`);
      console.log(`  🏛️ MARKET: ${trade.market}`);
      console.log(`  🔧 INSTRUMENT: ${trade.instrument}`);
      console.log(`  📆 EXPIRY: ${trade.expiry}`);
      console.log(`  📊 STATUS: ${trade.status}`);
      console.log(`  🔺 QTY: ${trade.qty || 'blank'}`);
      console.log(`  💰 STRIKE_PRICE: ${trade.strikePrice}`);
      console.log(`  📊 TYPE: ${trade.optionType || 'blank'}`);
      console.log(`  💵 PREMIUM: ${trade.premium}`);
      console.log(`  📈 EXCHANGE_FEE: ${trade.exchangeFee}`);
      console.log(`  🏦 COMMISSION: ${trade.commission}`);
    });
    
    console.log('\n🎯 FIELD VALIDATION:');
    console.log('=' .repeat(50));
    
    // Validate description parsing
    const descriptionTests = [
      { orderNo: '640201', expectedInstrument: 'HH', expectedExpiry: '05 SEP 25', expectedStatus: 'N' },
      { orderNo: '647740', expectedInstrument: 'HH', expectedExpiry: 'AUG 25', expectedStatus: 'C' },
      { orderNo: '648361', expectedInstrument: 'HS', expectedExpiry: 'DEC 25', expectedStatus: 'N' }
    ];
    
    let validationErrors = 0;
    
    descriptionTests.forEach(test => {
      const trade = statement.tradeConfirmation.find(t => t.orderNo === test.orderNo);
      if (trade) {
        if (trade.instrument !== test.expectedInstrument) {
          console.log(`❌ Trade ${test.orderNo}: Expected instrument ${test.expectedInstrument}, got ${trade.instrument}`);
          validationErrors++;
        }
        if (trade.expiry !== test.expectedExpiry) {
          console.log(`❌ Trade ${test.orderNo}: Expected expiry ${test.expectedExpiry}, got ${trade.expiry}`);
          validationErrors++;
        }
        if (trade.status !== test.expectedStatus) {
          console.log(`❌ Trade ${test.orderNo}: Expected status ${test.expectedStatus}, got ${trade.status}`);
          validationErrors++;
        }
      } else {
        console.log(`❌ Trade ${test.orderNo} not found`);
        validationErrors++;
      }
    });
    
    // Validate QTY columns (positive for buy, negative for sell)
    const qtyTests = [
      { orderNo: '640201', expectedQty: 2 }, // Buy trade
      { orderNo: '647740', expectedQty: -4 }, // Sell trade
      { orderNo: '664050', expectedQty: -4 } // Sell trade
    ];
    
    qtyTests.forEach(test => {
      const trade = statement.tradeConfirmation.find(t => t.orderNo === test.orderNo);
      if (trade) {
        if (trade.qty !== test.expectedQty) {
          console.log(`❌ Trade ${test.orderNo}: Expected qty ${test.expectedQty}, got ${trade.qty}`);
          validationErrors++;
        }
      }
    });
    
    console.log(`\n📊 VALIDATION SUMMARY:`);
    console.log(`Validation errors: ${validationErrors}`);
    console.log(`Status: ${validationErrors === 0 ? '✅ ALL VALIDATIONS PASSED' : '❌ SOME VALIDATIONS FAILED'}`);
    
    if (validationErrors === 0 && statement.tradeConfirmation.length === 13) {
      console.log('\n🎉 FIXED-WIDTH PARSING SUCCESS! ✅');
      console.log('✅ Correct trade count (13)');
      console.log('✅ Description fields properly parsed (instrument, expiry, status)');
      console.log('✅ QTY columns correctly reflect trade action (buy/sell)');
      console.log('✅ All other fields accurately extracted');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

testFixedWidthParsing();
