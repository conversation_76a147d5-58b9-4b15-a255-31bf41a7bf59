import { create } from 'zustand';

const useDatabaseStore = create((set, get) => ({
  isOffline: false,
  errorNotification: {
    show: false,
    title: '',
    message: '',
    details: null,
    type: 'error',
  },
  setDatabaseOffline: (error) => {
    set({ isOffline: true });
    set((state) => {
      if (state.errorNotification.show) {
        return {};
      }
      return {
        errorNotification: {
          show: true,
          title: 'Database Connection Error',
          message: 'Error: Unable to connect to the database',
          details: error ? error.message : 'Unknown database error',
          type: 'error',
        },
      };
    });
  },
  setDatabaseOnline: () => set({ isOffline: false }),
  closeErrorNotification: () => set((state) => ({
    errorNotification: { ...state.errorNotification, show: false },
  })),
}));

export default useDatabaseStore;
