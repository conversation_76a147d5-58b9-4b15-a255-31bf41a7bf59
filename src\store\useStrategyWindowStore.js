import { create } from 'zustand';
import { StrategyTypes } from '../types/strategy';

/**
 * Strategy Window Manager Store
 * 
 * This Zustand store manages multiple strategy windows, their positions,
 * states, and provides window management functionality.
 */
const useStrategyWindowStore = create((set, get) => ({
  // State
  openWindows: [],
  activeWindowId: null,
  maxWindows: StrategyTypes.VALIDATION_RULES.MAX_CONCURRENT_WINDOWS,

  // Window management actions
  openWindow: (strategyId, strategyData) => {
    const { openWindows, maxWindows } = get();
    
    // Check if window is already open
    const existingWindow = openWindows.find(w => w.strategyId === strategyId);
    if (existingWindow) {
      // Bring existing window to front
      set({ activeWindowId: existingWindow.id });
      return existingWindow.id;
    }
    
    // Check window limit
    if (openWindows.length >= maxWindows) {
      console.warn(`Maximum of ${maxWindows} strategy windows allowed`);
      return null;
    }
    
    // Calculate position for new window (cascade effect)
    const baseX = 100;
    const baseY = 100;
    const offset = openWindows.length * 30;
    
    const newWindow = {
      ...StrategyTypes.DEFAULT_WINDOW,
      id: StrategyTypes.generateWindowId(),
      strategyId,
      strategyName: strategyData?.name || `Strategy ${strategyId}`,
      stockSymbol: strategyData?.stockSymbol || '',
      position: {
        x: baseX + offset,
        y: baseY + offset
      },
      size: {
        width: 900,
        height: 700
      },
      isMinimized: false,
      zIndex: Date.now() // Use timestamp for z-index ordering
    };
    
    set(state => ({
      openWindows: [...state.openWindows, newWindow],
      activeWindowId: newWindow.id
    }));
    
    return newWindow.id;
  },

  closeWindow: (windowId) => {
    const { openWindows, activeWindowId } = get();
    
    set(state => {
      const updatedWindows = state.openWindows.filter(w => w.id !== windowId);
      
      // If closing the active window, set new active window
      let newActiveId = state.activeWindowId;
      if (activeWindowId === windowId) {
        newActiveId = updatedWindows.length > 0 ? updatedWindows[updatedWindows.length - 1].id : null;
      }
      
      return {
        openWindows: updatedWindows,
        activeWindowId: newActiveId
      };
    });
  },

  closeAllWindows: () => {
    set({ openWindows: [], activeWindowId: null });
  },

  setActiveWindow: (windowId) => {
    const { openWindows } = get();
    const window = openWindows.find(w => w.id === windowId);
    
    if (window) {
      // Update z-index to bring to front
      set(state => ({
        openWindows: state.openWindows.map(w => 
          w.id === windowId 
            ? { ...w, zIndex: Date.now() }
            : w
        ),
        activeWindowId: windowId
      }));
    }
  },

  updateWindowPosition: (windowId, position) => {
    set(state => ({
      openWindows: state.openWindows.map(w =>
        w.id === windowId ? { ...w, position } : w
      )
    }));
  },

  updateWindowSize: (windowId, size) => {
    set(state => ({
      openWindows: state.openWindows.map(w =>
        w.id === windowId ? { ...w, size } : w
      )
    }));
  },

  minimizeWindow: (windowId) => {
    set(state => ({
      openWindows: state.openWindows.map(w =>
        w.id === windowId ? { ...w, isMinimized: true } : w
      )
    }));
  },

  restoreWindow: (windowId) => {
    set(state => ({
      openWindows: state.openWindows.map(w =>
        w.id === windowId ? { ...w, isMinimized: false } : w
      )
    }));
    
    // Set as active when restored
    get().setActiveWindow(windowId);
  },

  toggleMinimize: (windowId) => {
    const { openWindows } = get();
    const window = openWindows.find(w => w.id === windowId);
    
    if (window) {
      if (window.isMinimized) {
        get().restoreWindow(windowId);
      } else {
        get().minimizeWindow(windowId);
      }
    }
  },

  // Utility functions
  getWindow: (windowId) => {
    const { openWindows } = get();
    return openWindows.find(w => w.id === windowId);
  },

  getWindowByStrategyId: (strategyId) => {
    const { openWindows } = get();
    return openWindows.find(w => w.strategyId === strategyId);
  },

  isWindowOpen: (strategyId) => {
    return get().getWindowByStrategyId(strategyId) !== undefined;
  },

  getActiveWindow: () => {
    const { openWindows, activeWindowId } = get();
    return openWindows.find(w => w.id === activeWindowId);
  },

  // Window arrangement utilities
  cascadeWindows: () => {
    const { openWindows } = get();
    const baseX = 100;
    const baseY = 100;
    const offsetX = 30;
    const offsetY = 30;
    
    set(state => ({
      openWindows: state.openWindows.map((window, index) => ({
        ...window,
        position: {
          x: baseX + (index * offsetX),
          y: baseY + (index * offsetY)
        }
      }))
    }));
  },

  tileWindows: () => {
    const { openWindows } = get();
    const visibleWindows = openWindows.filter(w => !w.isMinimized);
    if (visibleWindows.length === 0) return;

    // Calculate grid layout
    const cols = Math.ceil(Math.sqrt(visibleWindows.length));
    const rows = Math.ceil(visibleWindows.length / cols);

    // Get actual viewport size
    const viewportWidth = document.documentElement.clientWidth || 1200;
    const viewportHeight = document.documentElement.clientHeight || 800;

    // Leave some padding around the edges
    const padding = 40;
    const workingWidth = viewportWidth - padding;
    const workingHeight = viewportHeight - padding;

    const windowWidth = Math.floor(workingWidth / cols);
    const windowHeight = Math.floor(workingHeight / rows);

    let visibleIndex = 0;
    set(state => ({
      openWindows: state.openWindows.map((window) => {
        if (window.isMinimized) {
          return window; // Don't change minimized windows
        }

        const col = visibleIndex % cols;
        const row = Math.floor(visibleIndex / cols);
        visibleIndex++;

        return {
          ...window,
          position: {
            x: col * windowWidth + (padding / 2),
            y: row * windowHeight + (padding / 2)
          },
          size: {
            width: windowWidth - 10,
            height: windowHeight - 10
          },
          isMinimized: false,
          isMaximized: false,
          originalState: undefined
        };
      })
    }));
  },

  maximizeWindow: (windowId) => {
    const { openWindows } = get();
    const window = openWindows.find(w => w.id === windowId);

    if (window) {
      // Store original size and position for potential restore
      const originalState = {
        position: window.position,
        size: window.size
      };

      // Maximize to full viewport (with some padding)
      const viewportWidth = document.documentElement.clientWidth || 1200;
      const viewportHeight = document.documentElement.clientHeight || 800;

      set(state => ({
        openWindows: state.openWindows.map(w =>
          w.id === windowId ? {
            ...w,
            position: { x: 20, y: 20 },
            size: {
              width: viewportWidth - 40,
              height: viewportHeight - 40
            },
            originalState: originalState,
            isMaximized: true
          } : w
        )
      }));
    }
  },

  restoreWindowSize: (windowId) => {
    const { openWindows } = get();
    const window = openWindows.find(w => w.id === windowId);

    if (window && window.originalState) {
      set(state => ({
        openWindows: state.openWindows.map(w =>
          w.id === windowId ? {
            ...w,
            position: window.originalState.position,
            size: window.originalState.size,
            originalState: undefined,
            isMaximized: false
          } : w
        )
      }));
    }
  },

  tileOrMaximize: (windowId) => {
    const { openWindows } = get();
    const visibleWindows = openWindows.filter(w => !w.isMinimized);

    if (visibleWindows.length === 1) {
      // Only one window visible - toggle maximize/restore
      const window = visibleWindows[0];
      if (window.isMaximized) {
        get().restoreWindowSize(windowId);
      } else {
        get().maximizeWindow(windowId);
      }
    } else {
      // Multiple windows - tile them all (this will also restore any maximized windows)
      get().tileWindows();
    }
  },

  // Window state persistence (for future enhancement)
  saveWindowLayout: () => {
    const { openWindows } = get();
    const layout = openWindows.map(w => ({
      strategyId: w.strategyId,
      position: w.position,
      size: w.size,
      isMinimized: w.isMinimized
    }));
    
    localStorage.setItem('strategyWindowLayout', JSON.stringify(layout));
  },

  restoreWindowLayout: () => {
    try {
      const saved = localStorage.getItem('strategyWindowLayout');
      if (saved) {
        const layout = JSON.parse(saved);
        // This would need to be implemented with strategy data loading
        console.log('Saved layout found:', layout);
        return layout;
      }
    } catch (error) {
      console.error('Error restoring window layout:', error);
    }
    return null;
  },

  // Debug utilities
  getWindowStats: () => {
    const { openWindows, maxWindows } = get();
    return {
      openCount: openWindows.length,
      maxWindows,
      availableSlots: maxWindows - openWindows.length,
      windowIds: openWindows.map(w => w.id),
      strategyIds: openWindows.map(w => w.strategyId)
    };
  }
}));

export default useStrategyWindowStore;
