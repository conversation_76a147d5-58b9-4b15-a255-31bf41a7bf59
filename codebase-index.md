# Codebase Index

This document provides an overview of the essential program artifacts in the Options Strategy Analyzer application.

## Core Components

| File | Description |
|------|-------------|
| `src/App.jsx` | Main application component that handles routing and overall layout |
| `src/index.jsx` | Application entry point that renders the App component |
| `server.js` | Express backend server that provides API endpoints for database operations |

## Options Analysis Components

| File | Description |
|------|-------------|
| `src/components/options-analyzer/index.jsx` | Main container for the options strategy analyzer |
| `src/components/options-analyzer/PositionTable.jsx` | Table for managing options positions (add, remove, edit) |
| `src/components/options-analyzer/Sliders.jsx` | Controls for adjusting analysis parameters (stock price, volatility, etc.) |
| `src/components/positions-table.jsx` | Enhanced table for displaying and managing positions with market value and PnL |
| `src/components/strategy-chart-fixed.jsx` | Chart.js implementation of the profit/loss visualization with zoom preservation |
| `src/components/TimeDecayChart.jsx` | Chart showing how option values change over time |

## Data Management

| File | Description |
|------|-------------|
| `src/utils/black-scholes.js` | Implementation of the Black-Scholes model for options pricing |
| `src/utils/position-utils.js` | Utility functions for position calculations and management |
| `src/utils/firebase-position-utils.js` | Functions for saving and retrieving positions from Firebase |
| `src/context/DatabaseContext.js` | Context provider for database connection status |

## Server-Side Utilities

| File | Description |
|------|-------------|
| `utils/firebase.ts` | Server-side Firebase Firestore initialization and admin SDK setup |
| `utils/stockUtils.js` | Server-side utilities for fetching stock data from Yahoo Finance |
| `utils/trade-parser.js` | Server-side utilities for parsing trade data from various formats |

## Trade Query Components

| File | Description |
|------|-------------|
| `src/components/options-trades-query.jsx` | Component for querying and displaying trades by expiry date |
| `src/components/trade-list/trade-list.jsx` | Component for displaying all trades with filtering capabilities |
| `src/components/trade-parser/trade-parser.jsx` | Component for parsing and uploading trades |

## API Integration

| File | Description |
|------|-------------|
| `src/utils/firebase.js` | Client-side Firebase Firestore initialization and utility functions |
| `src/utils/yahoo-finance.js` | Integration with Yahoo Finance API for stock data |

## UI Components

| File | Description |
|------|-------------|
| `src/components/database-offline-indicator.jsx` | Visual indicator for database connection status |
| `src/components/ticker.jsx` | Component for displaying real-time stock information |
| `src/components/chart-styles.css` | Styling for chart components |

## Configuration Files

| File | Description |
|------|-------------|
| `package.json` | Project dependencies and scripts |
| `tailwind.config.js` | Tailwind CSS configuration |
| `.env` | Environment variables for database connection and API keys |
| `firebase.json` | Firebase service account key |

## Documentation

| File | Description |
|------|-------------|
| `README.md` | Project overview and setup instructions |
| `memory-bank/projectbrief.md` | Foundation document defining core requirements and goals |
| `memory-bank/productContext.md` | Description of why the project exists and problems it solves |
| `memory-bank/systemPatterns.md` | System architecture and key technical decisions |
| `memory-bank/techContext.md` | Technologies used and technical constraints |
| `memory-bank/progress.md` | Current status, what works, and what's left to build |
| `memory-bank/activeContext.md` | Current work focus and recent changes |
| `hardcoded.md` | Documentation of approved hard-coded values |
| `codebase-index.md` | This file - index of essential program artifacts |

## Database Integration

| File | Description |
|------|-------------|
| `memory-bank/database-integration.md` | Documentation of PostgreSQL database integration |
| `memory-bank/firebase-integration.md` | Documentation of Firebase Firestore integration |

## Obsolete Code

The `obsolete/` directory contains deprecated code that is no longer in use but kept for reference:

| Category | Description |
|----------|-------------|
| Next.js Files | Files from previous Next.js implementation (next.config.js, .next/, etc.) |
| Recharts Components | Previous chart implementations using Recharts before migration to Chart.js |
| Legacy API Endpoints | Outdated API implementations |
| Test Files | Unused or outdated test files |
