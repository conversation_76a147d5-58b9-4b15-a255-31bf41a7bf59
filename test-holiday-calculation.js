// Test file to verify holiday-aware days to expiry calculation
import { calculateDaysToExpiry, loadExchangeHolidays } from './src/utils/position-utils.js';

console.log('Testing holiday-aware calculateDaysToExpiry function...');

// Test 1: Basic calculation without holidays
console.log('\n=== Test 1: Basic calculation ===');
const today = '2025-06-30';
const expiry1 = '2025-07-15';
const days1 = calculateDaysToExpiry(expiry1, today);
console.log(`Days from ${today} to ${expiry1}: ${days1} trading days`);

// Test 2: Monthly expiry calculation
console.log('\n=== Test 2: Monthly expiry ===');
const expiry2 = '2025-07'; // Monthly expiry
const days2 = calculateDaysToExpiry(expiry2, today);
console.log(`Days from ${today} to ${expiry2} (monthly): ${days2} trading days`);

// Test 3: Load holidays and show them
console.log('\n=== Test 3: Holiday data ===');
const holidays = loadExchangeHolidays();
console.log(`Loaded ${holidays.length} holidays:`);
holidays.slice(0, 10).forEach(holiday => {
  console.log(`  - ${holiday.toDateString()}`);
});
if (holidays.length > 10) {
  console.log(`  ... and ${holidays.length - 10} more holidays`);
}

// Test 4: Compare with and without holidays
console.log('\n=== Test 4: Weekend exclusion test ===');
const fridayDate = '2025-07-04'; // Friday
const mondayExpiry = '2025-07-07'; // Monday
const weekendDays = calculateDaysToExpiry(mondayExpiry, fridayDate);
console.log(`Trading days from Friday ${fridayDate} to Monday ${mondayExpiry}: ${weekendDays} (should be 1, excluding weekend)`);

// Test 5: Test with known holiday period
console.log('\n=== Test 5: Holiday period test ===');
const beforeHoliday = '2024-12-20';
const afterHoliday = '2025-01-03';
const holidayPeriodDays = calculateDaysToExpiry(afterHoliday, beforeHoliday);
console.log(`Trading days from ${beforeHoliday} to ${afterHoliday}: ${holidayPeriodDays} (should exclude Christmas/New Year holidays)`);

console.log('\nTest completed!');
