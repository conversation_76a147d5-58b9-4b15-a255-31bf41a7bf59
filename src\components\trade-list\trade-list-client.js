import React, { useState, useEffect } from 'react';
import useDatabaseStore from '../../store/useDatabaseStore';
import TradeDataTable from './trade-data-table';
import DatabaseOfflineIndicator from '../database-offline-indicator';
import { getUnderlyingSymbol } from '../../utils/trade-parser';

/**
 * Trade List Client Component
 *
 * This component fetches and displays all trades from Firestore with filtering capabilities.
 * It allows filtering by underlying stock symbol and expiry date.
 */
const TradeListClient = () => {
  // Access database status context
  const { isOffline } = useDatabaseStore();

  // State for all trades (used when setting filtered trades)
  const [, setAllTrades] = useState([]);
  // State for filtered trades
  const [filteredTrades, setFilteredTrades] = useState([]);
  // State for unique stocks
  const [stocks, setStocks] = useState([]);
  // State for selected stock
  const [selectedStock, setSelectedStock] = useState('ALL');
  // State for unique expiry dates
  const [expiryDates, setExpiryDates] = useState([]);
  // State for selected expiry date
  const [selectedExpiryDate, setSelectedExpiryDate] = useState('ALL');
  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  // State for error message
  const [error, setError] = useState(null);

  // Fetch all stocks from Firestore
  useEffect(() => {
    const fetchStocks = async () => {
      if (isOffline) {
        setError('database_offline');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch('/api/firebase-stocks');

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        setStocks(data.stocks || []);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching stocks:', error);

        // Set a special error code for database connection errors
        if (error.message === 'database_connection_error' ||
            error.message.includes('database') ||
            error.message.includes('connection') ||
            error.message.includes('ECONNREFUSED') ||
            error.message.includes('timeout') ||
            error.message.includes('firestore') ||
            error.message.includes('firebase')) {
          setError('database_offline');
        } else {
          setError(error.message);
        }
        setIsLoading(false);
      }
    };

    fetchStocks();
  }, [isOffline]);

  // Handle fetch errors
  const handleFetchError = (error) => {
    if (error.message === 'database_connection_error' ||
        error.message.includes('database') ||
        error.message.includes('connection') ||
        error.message.includes('ECONNREFUSED') ||
        error.message.includes('timeout') ||
        error.message.includes('firestore') ||
        error.message.includes('firebase')) {
      setError('database_offline');
    } else {
      setError(error.message);
    }
    setIsLoading(false);
  };

  // Fetch expiry dates for the selected stock
  useEffect(() => {
    const fetchExpiryDates = async () => {
      // Reset expiry dates when stock changes
      setExpiryDates([]);

      if (isOffline) {
        return;
      }

      // Don't fetch if no stock is selected or if ALL is selected
      if (!selectedStock || selectedStock === 'ALL') {
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch(`/api/firebase-expiry-dates?stock=${selectedStock}`);

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        if (data.expiryDates && data.expiryDates.length > 0) {
          // Sort expiry dates
          const sortedDates = [...data.expiryDates].sort((a, b) => {
            const dateA = new Date(a.includes('-') && a.length === 7 ? `${a}-01` : a);
            const dateB = new Date(b.includes('-') && b.length === 7 ? `${b}-01` : b);

            if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
              return dateA.getTime() - dateB.getTime();
            }
            return a.localeCompare(b);
          });

          setExpiryDates(sortedDates);
        } else {
          setError('no_expiry_dates');
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching expiry dates:', error);

        // Set a special error code for database connection errors
        if (error.message === 'database_connection_error' ||
            error.message.includes('database') ||
            error.message.includes('connection') ||
            error.message.includes('ECONNREFUSED') ||
            error.message.includes('timeout') ||
            error.message.includes('firestore') ||
            error.message.includes('firebase')) {
          setError('database_offline');
        } else {
          setError(error.message);
        }
        setIsLoading(false);
      }
    };

    fetchExpiryDates();
  }, [selectedStock, isOffline]);

  // Fetch all trades from Firestore
  useEffect(() => {
    const fetchAllTrades = async () => {
      if (isOffline) {
        setError('database_offline');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch('/api/firebase-all-trades');

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        const trades = data.trades || [];

        // Process trades to identify futures and set stock field
        const processedTrades = trades.map((trade) => {
          const baseStock = trade.stock || getUnderlyingSymbol(trade.ticker);
          if (trade.strike === 'N/A' || (trade.ticker && trade.ticker.length <= 5 && !trade.strike)) {
            return {
              ...trade,
              type: 'Future',
              stock: baseStock
            };
          }
          return {
            ...trade,
            stock: baseStock
          };
        });

        console.log('Processed trades:', processedTrades);
        setAllTrades(processedTrades);
        
        // Apply filters
        if (selectedStock === 'ALL' && selectedExpiryDate === 'ALL') {
          setFilteredTrades(processedTrades);
        } else {
          const filtered = processedTrades.filter(trade => {
            const stockMatch = selectedStock === 'ALL' || trade.stock === selectedStock;
            const expiryMatch = selectedExpiryDate === 'ALL' || trade.expiry === selectedExpiryDate;
            return stockMatch && expiryMatch;
          });
          setFilteredTrades(filtered);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching trades:', error);
        handleFetchError(error);
      }
    };

    fetchAllTrades();
  }, [isOffline, selectedStock, selectedExpiryDate]);

  // Clear filters
  const clearFilters = () => {
    setSelectedStock('ALL');
    setSelectedExpiryDate('ALL');
    setFilteredTrades([]);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const parts = dateString.split('-');
    if (parts.length >= 3) {
      const month = parseInt(parts[1]);
      const day = parseInt(parts[2]);
      return `${month}/${day}`;
    }
    return dateString;
  };

  return (
    <div className="space-y-8">
      <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold text-gray-800">Trade List</h2>
          <DatabaseOfflineIndicator />
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-blue-100 p-2 rounded">
            <div className="font-bold text-xs mb-1">Stock</div>
            <select
              className={`w-full p-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500 ${error === 'database_offline' ? 'bg-gray-100 cursor-not-allowed' : ''}`}
              value={selectedStock}
              onChange={(e) => setSelectedStock(e.target.value)}
              disabled={error === 'database_offline' || isLoading}
            >
              <option value="ALL">All Stocks</option>
              {stocks.length > 0 ? (
                stocks.map(stock => (
                  <option key={stock} value={stock}>{stock}</option>
                ))
              ) : error === 'database_offline' ? (
                <option value="" disabled>Offline</option>
              ) : (
                <option value="" disabled>No stocks available</option>
              )}
            </select>
          </div>

          <div className="bg-blue-100 p-2 rounded">
            <div className="font-bold text-xs mb-1">Expiry Date</div>
            <select
              className={`w-full p-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500 ${error === 'database_offline' || !selectedStock ? 'bg-gray-100 cursor-not-allowed' : ''}`}
              value={selectedExpiryDate}
              onChange={(e) => setSelectedExpiryDate(e.target.value)}
              disabled={error === 'database_offline' || (selectedStock !== 'ALL' && !selectedStock) || isLoading}
            >
              <option value="ALL">All Expiry Dates</option>
              {expiryDates.length > 0 ? (
                expiryDates.map(date => (
                  <option key={date} value={date}>{formatDate(date)}</option>
                ))
              ) : error === 'database_offline' ? (
                <option value="" disabled>Offline</option>
              ) : !selectedStock ? (
                <option value="" disabled>Select a stock first</option>
              ) : isLoading ? (
                <option value="" disabled>Loading...</option>
              ) : error === 'no_expiry_dates' ? (
                <option value="" disabled>No valid expiry dates for {selectedStock}</option>
              ) : (
                <option value="" disabled>No expiry dates available</option>
              )}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={clearFilters}
              className="w-full p-1 text-xs bg-gray-200 hover:bg-gray-300 rounded"
              disabled={(selectedStock === 'ALL' && selectedExpiryDate === 'ALL')}
            >
              Clear Filters
            </button>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-4">
            <p className="text-gray-500">Loading trades...</p>
          </div>
        ) : error && error !== 'database_offline' ? (
          <div className="text-center py-4">
            <p className="text-red-500">{error}</p>
          </div>
        ) : (
          <TradeDataTable trades={filteredTrades} />
        )}
      </div>
    </div>
  );
};

export default TradeListClient;
