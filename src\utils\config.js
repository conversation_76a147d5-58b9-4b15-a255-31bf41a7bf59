const path = require('path');

/**
 * Configuration utility for environment-based path management
 * Reads paths from environment variables with sensible defaults
 */

/**
 * Get the output directory path from environment or default
 * @returns {string} Absolute path to output directory
 */
function getOutputPath() {
  const outputPath = process.env.OUTPUT_PATH || 'output';
  return path.resolve(process.cwd(), outputPath);
}

/**
 * Get the holiday data XML file path
 * @returns {string} Absolute path to holiday XML file
 */
function getHolidayDataPath() {
  const outputDir = getOutputPath();
  return path.join(outputDir, 'hkex_holidays.xml');
}

/**
 * Get the backup holiday data XML file path
 * @returns {string} Absolute path to backup holiday XML file
 */
function getHolidayDataBackupPath() {
  const outputDir = getOutputPath();
  return path.join(outputDir, 'hkex_holidays.xml.backup');
}

/**
 * Ensure output directory exists
 * @returns {string} Path to the created/existing output directory
 */
function ensureOutputDirectory() {
  const fs = require('fs');
  const outputDir = getOutputPath();
  
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log('Created output directory:', outputDir);
  }
  
  return outputDir;
}

module.exports = {
  getOutputPath,
  getHolidayDataPath,
  getHolidayDataBackupPath,
  ensureOutputDirectory
};