// Simple test script to verify the fix for premium calculation at expiry
import { calculatePositionPremium } from './position-utils.js';

// Test cases
const testCases = [
  {
    name: 'Call option at expiry (in the money)',
    position: {
      type: 'Call',
      strike: 22000,
      daysToExpiry: 0, // At expiry
    },
    stockPrice: 22500,
    riskFreeRate: 2,
    volatility: 30,
    expectedResult: 500 // 22500 - 22000 = 500
  },
  {
    name: 'Call option at expiry (out of the money)',
    position: {
      type: 'Call',
      strike: 23000,
      daysToExpiry: 0, // At expiry
    },
    stockPrice: 22500,
    riskFreeRate: 2,
    volatility: 30,
    expectedResult: 0
  },
  {
    name: 'Put option at expiry (in the money)',
    position: {
      type: 'Put',
      strike: 23000,
      daysToExpiry: 0, // At expiry
    },
    stockPrice: 22500,
    riskFreeRate: 2,
    volatility: 30,
    expectedResult: 500 // 23000 - 22500 = 500
  },
  {
    name: 'Put option at expiry (out of the money)',
    position: {
      type: 'Put',
      strike: 22000,
      daysToExpiry: 0, // At expiry
    },
    stockPrice: 22500,
    riskFreeRate: 2,
    volatility: 30,
    expectedResult: 0
  }
];

// Run tests
testCases.forEach(testCase => {
  const result = calculatePositionPremium(
    testCase.position,
    testCase.stockPrice,
    testCase.riskFreeRate,
    testCase.volatility
  );

  console.log(`Test: ${testCase.name}`);
  console.log(`Expected: ${testCase.expectedResult}, Actual: ${result}`);
  console.log(`Result: ${result === testCase.expectedResult ? 'PASS' : 'FAIL'}`);
  console.log('-----------------------------------');
});
