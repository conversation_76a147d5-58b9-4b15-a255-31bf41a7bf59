import React, { useState, useMemo, useEffect } from 'react'; // Added useEffect
import useAllTradesStore from '../store/useAllTradesStore';
import useAnalysisStore from '../store/useAnalysisStore';
import usePnlChartStore from '../store/usePnlChartStore'; // Import the new store
import usePnLAtVariousPriceStore from '../store/usePnLAtVariousPriceStore'; // Import enhanced price store
// Ensure this path is correct and both functions are exported
import { calculatePositionPremium, calculateDaysToExpiry } from '../utils/position-utils';

/**
 * PnLAtVariousPriceTable
 *
 * Iterates through trades from useAllTradesStore for a selected stock,
 * calculates predicted P&L at various stock prices based on analysis parameters,
 * and displays the results in a table.
 */
const PnLAtVariousPriceTable = () => {  // Get data from stores
  const { allTrades, isLoading: tradesLoading, error: tradesError } = useAllTradesStore();
  const {
    targetStockPrice,
    volatility,
    riskFreeRate,
    daysToVisualize,
    symbol: selectedStockSymbol,
    isLoading: analysisLoading,
  } = useAnalysisStore();

  // Get setters from PnlChartStore
  const { setData: setPnlChartData, setLoading: setPnlChartLoading, setError: setPnlChartError } = usePnlChartStore();
  // Get enhanced price points from store
  const { pnlData } = usePnLAtVariousPriceStore();

  // Use enhanced price points from store (with fallback to empty array)
  const calculatedPricePoints = useMemo(() => {
    if (!targetStockPrice || typeof targetStockPrice !== 'number' || targetStockPrice <= 0) return [];
    
    // Get enhanced price points from store data, or fallback to hardcoded if store hasn't calculated yet
    const storePoints = pnlData?.pricePoints || [];
    
    if (storePoints && storePoints.length > 0) {
      return storePoints;
    }
      // Fallback to original 5-point system if store data isn't available
    const step = 100;
    return [
      targetStockPrice - 2 * step,
      targetStockPrice - 1 * step,
      targetStockPrice,
      targetStockPrice + 1 * step,
      targetStockPrice + 2 * step,
    ].filter(price => price > 0);
  }, [targetStockPrice, pnlData?.pricePoints]);

  // Calculate position data (memoized)
  const calculatedPositionData = useMemo(() => {
    if (
      !selectedStockSymbol ||
      !allTrades ||
      allTrades.length === 0 ||
      !targetStockPrice ||
      typeof volatility !== 'number' ||
      typeof riskFreeRate !== 'number' ||
      typeof daysToVisualize !== 'number' || // daysToVisualize is used to set the simulation date
      calculatedPricePoints.length === 0
    ) {
      return [];
    }

    const relevantTrades = allTrades.filter(trade => {
      const tradeStock = trade.stock || (trade.ticker ? trade.ticker.substring(0, 3).toUpperCase() : null);
      return tradeStock === selectedStockSymbol.toUpperCase();
    });

    const today = new Date();
    const simulationTargetDate = new Date(today);
    simulationTargetDate.setDate(today.getDate() + (daysToVisualize || 0)); // Date for which we are predicting

    return relevantTrades.map(trade => {
      const positionPnlData = {
        id: trade.id || `${trade.ticker}-${trade.ExpiryDate || trade.expiry}-${Math.random()}`,
        trade: { ...trade },
      };

      const tradeActualExpiry = trade.ExpiryDate || trade.expiry;
      let daysRemainingForOption;
      if (tradeActualExpiry) {
        // Calculate days from the simulationTargetDate to the trade's actual expiry date
        daysRemainingForOption = calculateDaysToExpiry(tradeActualExpiry, simulationTargetDate);
      } else {
        daysRemainingForOption = 0; // Default if no expiry date on trade
      }

      calculatedPricePoints.forEach(simulatedStockPrice => {
        const positionForCalc = {
          type: trade.type,
          strike: trade.strike, // Will be parsed to numeric below if option
          // Use the calculated days remaining from the simulation date to the option's expiry
          daysToExpiry: daysRemainingForOption, 
        };

        let numericStrike = parseFloat(trade.strike);
        if (trade.type?.toLowerCase() !== 'future' && isNaN(numericStrike)) {
           positionPnlData[`premium_${simulatedStockPrice}`] = 'N/A';
           positionPnlData[`pnl_${simulatedStockPrice}`] = 'N/A';
           return; 
        }
        if (trade.type?.toLowerCase() !== 'future') {
            positionForCalc.strike = numericStrike;
        }


        const newPremium = calculatePositionPremium(
          positionForCalc,
          simulatedStockPrice,
          riskFreeRate, // Expected as percentage e.g. 2.5 for 2.5%
          volatility    // Expected as percentage e.g. 15 for 15%
        );
        positionPnlData[`premium_${simulatedStockPrice}`] = newPremium;

        let pnl = 'N/A';
        const originalPremium = parseFloat(trade.premium);
        if (!isNaN(originalPremium) && typeof trade.quantity === 'number' && newPremium !== 'N/A') {
          pnl = (newPremium - originalPremium) * trade.quantity;
        }
        positionPnlData[`pnl_${simulatedStockPrice}`] = pnl;
      });
      return positionPnlData;
    });
  }, [
    allTrades,
    selectedStockSymbol,
    targetStockPrice,
    volatility,
    riskFreeRate,
    daysToVisualize, 
    calculatedPricePoints,
  ]);

  // Effect to update PnlChartStore when calculatedPositionData or calculatedPricePoints change
  useEffect(() => {
    if (tradesLoading || analysisLoading) {
      setPnlChartLoading(true);
      return;
    }
    if (tradesError) {
      setPnlChartError(tradesError);
      return;
    }
    if (!selectedStockSymbol || !targetStockPrice || calculatedPricePoints.length === 0) {
      // Don't set error, but clear data or set loading if appropriate
      // For now, just ensure chart store reflects that data isn't ready
      setPnlChartData([], []); 
      return;
    }
    setPnlChartData(calculatedPositionData, calculatedPricePoints);
  }, [calculatedPositionData, calculatedPricePoints, tradesLoading, analysisLoading, tradesError, selectedStockSymbol, targetStockPrice, setPnlChartData, setPnlChartLoading, setPnlChartError]);

  // UI Filters (can operate on calculatedPositionData)
  const [displayExpiryFilter, setDisplayExpiryFilter] = useState('');

  // Filter data for display based on UI filters
  const displayData = useMemo(() => {
    if (!calculatedPositionData) return [];
    return calculatedPositionData.filter(d => {
      const expiryMatch = !displayExpiryFilter || d.trade.ExpiryDate === displayExpiryFilter || (d.trade.expiry === displayExpiryFilter);
      // Stock symbol is already filtered by selectedStockSymbol during calculation
      return expiryMatch;
    });
  }, [calculatedPositionData, displayExpiryFilter]);

  // Unique expiries for filter dropdowns, derived from the trades of the selected stock
  const allDisplayExpiries = useMemo(() => {
    if (!selectedStockSymbol || !allTrades) return [];
    const relevantTrades = allTrades.filter(trade => {
        const tradeStock = trade.stock || (trade.ticker ? trade.ticker.substring(0, 3).toUpperCase() : null);
        return tradeStock === selectedStockSymbol.toUpperCase();
    });
    return Array.from(new Set(relevantTrades.map(d => d.ExpiryDate || d.expiry))).filter(e => e).sort();
  }, [allTrades, selectedStockSymbol]);


  if (tradesLoading || analysisLoading) { // Check if analysis parameters are also loading
    return <div className="text-center py-4"><p className="text-gray-500">Loading analysis data...</p></div>;
  }

  if (tradesError) {
    return <div className="text-center py-4"><p className="text-red-500">Error loading trades: {tradesError}</p></div>;
  }
  
  if (!selectedStockSymbol) {
    return <div className="text-center py-4"><p className="text-gray-500">Please select a stock symbol in the Ticker to perform analysis.</p></div>;
  }
  
  if (!targetStockPrice || calculatedPricePoints.length === 0) {
    return <div className="text-center py-4"><p className="text-gray-500">Please set a target stock price in Analysis Parameters.</p></div>;
  }


  return (
    <div className="mb-6">
      <h2 className="text-lg font-bold text-gray-800">
        P&L at Various Price for {selectedStockSymbol.toUpperCase()}
        <span className="ml-2 text-xs text-gray-500">
          @{targetStockPrice}, +{daysToVisualize}D, IV: {volatility}%, RFR: {riskFreeRate}%, ({displayData.length} of {calculatedPositionData.length} trades)  {calculatedPricePoints.length} Data Points
        </span>
      </h2>
      <div className="flex gap-4 mb-2 items-center">
        <div>
          {/* <label className="block text-xs font-medium mb-1">Filter by Expiry</label> */}
          <select 
            className="border border-gray-300 rounded px-2 py-1 text-xs focus:ring-blue-500 focus:border-blue-500" 
            value={displayExpiryFilter} 
            onChange={e => setDisplayExpiryFilter(e.target.value)}
          >
            <option value="">All Expiries</option>
            {allDisplayExpiries.map(exp => (
              <option key={exp} value={exp}>{exp}</option>
            ))}
          </select>
        </div>
      </div>
      {displayData.length === 0 && calculatedPositionData.length > 0 && (
         <p className="text-sm text-gray-500 py-2">No trades match the current expiry filter for {selectedStockSymbol.toUpperCase()}.</p>
      )}
      {calculatedPositionData.length === 0 && !tradesLoading && (
         <p className="text-sm text-gray-500 py-2">No trades found for {selectedStockSymbol.toUpperCase()} in the store, or parameters are missing for calculation.</p>
      )}

      {displayData.length > 0 && (
        <div className="overflow-x-auto">
          <table className="min-w-full border border-gray-200 text-xs">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="border p-1">Ticker</th>
                <th className="border p-1">Type</th>
                <th className="border p-1">Strike</th>
                <th className="border p-1">Qty</th>
                <th className="border p-1">Expiry</th>
                <th className="border p-1">Orig. Prem</th>
                {calculatedPricePoints.map(price => (
                  <th key={`th_prem_${price}`} className="border p-1 bg-blue-50">Prem@{price}</th>
                ))}
                {calculatedPricePoints.map(price => (
                  <th key={`th_pnl_${price}`} className="border p-1 bg-green-50">PnL@{price}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {displayData.map(d => (
                <tr key={d.id}>
                  <td className="border p-1" title={d.trade.ticker}>{d.trade.ticker}</td>
                  <td className="border p-1">{d.trade.type}</td>
                  <td className="border p-1">{d.trade.strike}</td>
                  <td className={`border p-1 ${d.trade.quantity < 0 ? 'text-red-600' : 'text-green-700'}`}>{d.trade.quantity}</td>
                  <td className="border p-1">{d.trade.ExpiryDate || d.trade.expiry}</td>
                  <td className="border p-1">{typeof d.trade.premium === 'number' ? d.trade.premium.toFixed(2) : d.trade.premium}</td>
                  {calculatedPricePoints.map(price => (
                    <td key={`${d.id}_prem_${price}`} className="border p-1">
                      {typeof d[`premium_${price}`] === 'number' ? d[`premium_${price}`].toFixed(2) : d[`premium_${price}`]}
                    </td>
                  ))}
                  {calculatedPricePoints.map(price => (
                    <td 
                      key={`${d.id}_pnl_${price}`} 
                      className={`border p-1 ${typeof d[`pnl_${price}`] === 'number' && d[`pnl_${price}`] < 0 ? 'text-red-600' : typeof d[`pnl_${price}`] === 'number' && d[`pnl_${price}`] > 0 ? 'text-green-700' : 'text-gray-700'}`}
                    >
                      {typeof d[`pnl_${price}`] === 'number' ? d[`pnl_${price}`].toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 }) : d[`pnl_${price}`]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default PnLAtVariousPriceTable;
