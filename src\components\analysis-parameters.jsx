import { useState, useEffect } from 'react';
import useAnalysisStore from '../store/useAnalysisStore';

/**
 * Analysis Parameters Component
 *
 * This component provides controls for analysis parameters using context for state management.
 * It's based on the SystemParameters component but uses context instead of props.
 */

// Helper function to get future date based on days from today (timezone-agnostic)
const getFutureDate = (daysFromToday) => {
  // Get today's date and reset time to midnight UTC
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Create a date string in YYYY-MM-DD format for today
  const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

  // Create a new date from the string (this ensures timezone consistency)
  const baseDate = new Date(todayStr);

  // Add the days
  const futureDate = new Date(baseDate);
  futureDate.setDate(baseDate.getDate() + daysFromToday);

  return futureDate;
};

// Helper function to format date as YYYY-MM-DD (timezone-agnostic)
const formatFullDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Helper function to format date as YY-MM-DD (timezone-agnostic)
const formatShortDate = (date) => {
  const year = date.getFullYear().toString().slice(2);
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const AnalysisParameters = () => {
  // Get analysis parameters from context
  const targetStockPrice = useAnalysisStore((s) => s.targetStockPrice);
  const setTargetStockPrice = useAnalysisStore((s) => s.setTargetStockPrice);
  const volatility = useAnalysisStore((s) => s.volatility);
  const setVolatility = useAnalysisStore((s) => s.setVolatility);
  const riskFreeRate = useAnalysisStore((s) => s.riskFreeRate);
  const setRiskFreeRate = useAnalysisStore((s) => s.setRiskFreeRate);
  const daysToVisualize = useAnalysisStore((s) => s.daysToVisualize);
  const setDaysToVisualize = useAnalysisStore((s) => s.setDaysToVisualize);
  const stockInfo = useAnalysisStore((s) => s.stockInfo);
  const symbol = useAnalysisStore((s) => s.symbol);
  const setSymbol = useAnalysisStore((s) => s.setSymbol);

  // State to track the input value
  const [inputValue, setInputValue] = useState(targetStockPrice);
  // State to track if date picker is visible
  const [showDatePicker, setShowDatePicker] = useState(false);
  // State to track the selected date
  const [selectedDate, setSelectedDate] = useState(formatFullDate(getFutureDate(daysToVisualize)));

  // Update target stock price when stock info changes
  useEffect(() => {
    if (stockInfo && stockInfo.response && stockInfo.response.regularMarketPrice) {
      const marketPrice = stockInfo.response.regularMarketPrice;
      // Round to nearest hundred
      const roundedPrice = Math.round(marketPrice / 100) * 100;
      setTargetStockPrice(roundedPrice);
      setInputValue(roundedPrice);
    }
  }, [stockInfo, setTargetStockPrice]);

  // Function to round to nearest hundred
  const roundToNearestHundred = (value) => {
    return Math.round(value / 100) * 100;
  };

  // Update input value when targetStockPrice changes
  useEffect(() => {
    setInputValue(targetStockPrice);
  }, [targetStockPrice]);

  // Update selected date when daysToVisualize changes
  useEffect(() => {
    setSelectedDate(formatFullDate(getFutureDate(daysToVisualize)));
  }, [daysToVisualize]);

  // Add click outside handler to close date picker
  useEffect(() => {
    if (!showDatePicker) return;

    const handleClickOutside = (event) => {
      if (event.target.type !== 'date' && !event.target.classList.contains('cursor-pointer')) {
        setShowDatePicker(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showDatePicker]);

  // Function to handle date change from date picker (timezone-agnostic)
  const handleDateChange = (e) => {
    // Get the selected date value (YYYY-MM-DD format)
    const dateValue = e.target.value;

    // Create today's date string in YYYY-MM-DD format
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

    // Create Date objects from strings (this ensures timezone consistency)
    const newDate = new Date(dateValue + 'T00:00:00Z');
    const todayDate = new Date(todayStr + 'T00:00:00Z');

    // Calculate days difference
    const diffTime = newDate.getTime() - todayDate.getTime();
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

    // Update days to visualize (ensure it's not negative)
    setDaysToVisualize(Math.max(0, diffDays));
    setSelectedDate(dateValue);
    setShowDatePicker(false);
  };

  return (
    <div className="mb-2">
      {/* <h2 className="text-sm font-bold mb-1 text-gray-800">Analysis Parameters</h2> */}

      {/* Single row - 5 columns */}
      <div className="grid grid-cols-5 gap-2">
        {/* Stock Symbol for Filtering */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-0.5">
            Stock Symbol
          </label>
          <div className="flex">
            <select
              className="w-full py-0.5 px-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              value={symbol}
              onChange={(e) => {
                const newSymbol = e.target.value;
                setSymbol(newSymbol);
              }}
            >
              <option value="HSI">HSI</option>
              <option value="HHI">HHI</option>
              <option value="HTI">HTI</option>
              <option value="MHI">MHI</option>
            </select>
          </div>
        </div>

        {/* Stock Price for Analysis - Editable with left/right controls */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-0.5">
            Target Price
          </label>
          <div className="flex">
            <button
              className="py-0.5 px-1 bg-gray-100 border border-gray-300 rounded-l hover:bg-gray-200 text-xs"
              onClick={() => {
                const newValue = Math.max(0, roundToNearestHundred(targetStockPrice - 100));
                setTargetStockPrice(newValue);
                setInputValue(newValue);
              }}
              title="Decrease by 100"
            >−</button>
            <input
              type="text"
              className="w-full py-0.5 px-1 text-xs border-t border-b border-gray-300 text-center focus:ring-blue-500 focus:border-blue-500"
              value={inputValue}
              onBlur={(e) => {
                const value = e.target.value;
                if (value === '' || isNaN(parseFloat(value))) {
                  // If invalid, keep current stock price
                  setInputValue(targetStockPrice);
                } else {
                  const parsedValue = parseFloat(value);
                  const roundedValue = roundToNearestHundred(parsedValue);
                  setInputValue(roundedValue);
                  setTargetStockPrice(roundedValue);
                }
              }}
              onChange={(e) => {
                const value = e.target.value;
                // Allow any numeric input including decimals
                if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                  setInputValue(value);
                }
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  const parsedValue = parseFloat(inputValue);
                  if (!isNaN(parsedValue)) {
                    const roundedValue = roundToNearestHundred(parsedValue);
                    setInputValue(roundedValue);
                    setTargetStockPrice(roundedValue);
                  }
                  e.target.blur();
                }
              }}
            />
            <button
              className="py-0.5 px-1 bg-gray-100 border border-gray-300 rounded-r hover:bg-gray-200 text-xs"
              onClick={() => {
                const newValue = roundToNearestHundred(targetStockPrice + 100);
                setTargetStockPrice(newValue);
                setInputValue(newValue);
              }}
              title="Increase by 100"
            >+</button>
          </div>
        </div>

        {/* Days to Expiry - showing exact calendar date */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-0.5">
            Target Date (+{daysToVisualize}D)</label>
          <div className="flex">
            <button
              className="py-0.5 px-1 bg-gray-100 border border-gray-300 rounded-l hover:bg-gray-200 text-xs"
              onClick={() => setDaysToVisualize(Math.max(0, daysToVisualize - 1))}
              title="Previous day"
            >−</button>
            <div className="relative w-full">
              <input
                type="text"
                className="w-full py-0.5 px-1 text-xs border-t border-b border-gray-300 text-center cursor-pointer focus:ring-blue-500 focus:border-blue-500"
                value={formatShortDate(getFutureDate(daysToVisualize))}
                readOnly
                title={`${daysToVisualize} days from today - Click to select date`}
                onClick={() => setShowDatePicker(!showDatePicker)}
              />
              {showDatePicker && (
                <div className="absolute top-full left-0 mt-1 z-10 bg-white shadow-md rounded border border-gray-300">
                  <input
                    type="date"
                    className="p-1 text-xs"
                    value={selectedDate}
                    onChange={handleDateChange}
                    min={formatFullDate(new Date())}
                  />
                </div>
              )}
            </div>
            <button
              className="py-0.5 px-1 bg-gray-100 border border-gray-300 rounded-r hover:bg-gray-200 text-xs"
              onClick={() => setDaysToVisualize(daysToVisualize + 1)}
              title="Next day"
            >+</button>
          </div>
        </div>

        {/* Volatility */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-0.5">Volatility</label>
          <div className="flex">
            <input
              type="text"
              className="w-full py-0.5 px-1 text-xs border border-gray-300 rounded-l focus:ring-blue-500 focus:border-blue-500"
              defaultValue={volatility}
              onBlur={(e) => {
                const value = e.target.value;
                if (value === '' || isNaN(parseFloat(value))) {
                  e.target.value = '0.00';
                  setVolatility(0);
                } else {
                  const parsedValue = parseFloat(value);
                  e.target.value = parsedValue.toFixed(2);
                  setVolatility(parsedValue);
                }
              }}
              onChange={(e) => {
                const value = e.target.value;
                // Allow any numeric input including decimals
                if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                  if (value !== '') {
                    setVolatility(parseFloat(value));
                  }
                }
              }}
            />
            <span className="inline-flex items-center px-1 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 rounded-r text-xs">%</span>
          </div>
        </div>

        {/* Risk-free Rate */}
        <div>
          <label className="block text-xs font-medium text-gray-700 mb-0.5">Risk-free Rate</label>
          <div className="flex">
            <input
              type="text"
              className="w-full py-0.5 px-1 text-xs border border-gray-300 rounded-l focus:ring-blue-500 focus:border-blue-500"
              defaultValue={riskFreeRate}
              onBlur={(e) => {
                const value = e.target.value;
                if (value === '' || isNaN(parseFloat(value))) {
                  e.target.value = '0.00';
                  setRiskFreeRate(0);
                } else {
                  const parsedValue = parseFloat(value);
                  e.target.value = parsedValue.toFixed(2);
                  setRiskFreeRate(parsedValue);
                }
              }}
              onChange={(e) => {
                const value = e.target.value;
                // Allow any numeric input including decimals
                if (value === '' || /^[0-9]*\.?[0-9]*$/.test(value)) {
                  if (value !== '') {
                    setRiskFreeRate(parseFloat(value));
                  }
                }
              }}
            />
            <span className="inline-flex items-center px-1 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 rounded-r text-xs">%</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalysisParameters;
