# Routing Implementation

## Overview
The application uses React Router v7.5.3 for navigation between different pages. Due to specific requirements and challenges with client-side routing, a custom implementation has been developed to ensure reliable navigation.

## Current Implementation

### Navigation Structure
The main navigation is implemented in `src/App.js` using direct anchor tags with forced page reloads:

```jsx
<nav className="bg-white shadow-md rounded-lg mb-6 p-4">
  <ul className="flex space-x-6">
    <li>
      <a 
        href="/"
        onClick={(e) => {
          e.preventDefault();
          window.location.href = '/';
        }}
        className="text-blue-600 hover:text-blue-800 font-medium"
      >
        Strategy Builder
      </a>
    </li>
    <li>
      <a 
        href="/trades-query"
        onClick={(e) => {
          e.preventDefault();
          window.location.href = '/trades-query';
        }}
        className="text-blue-600 hover:text-blue-800 font-medium"
      >
        Trades Query
      </a>
    </li>
    <li>
      <a 
        href="/trade-list"
        onClick={(e) => {
          e.preventDefault();
          window.location.href = '/trade-list';
        }}
        className="text-blue-600 hover:text-blue-800 font-medium"
      >
        Trade List
      </a>
    </li>
    <li>
      <a 
        href="/trade-parser"
        onClick={(e) => {
          e.preventDefault();
          window.location.href = '/trade-parser';
        }}
        className="text-blue-600 hover:text-blue-800 font-medium"
      >
        Trade Parser
      </a>
    </li>
  </ul>
</nav>
```

### Route Definitions
Routes are defined using React Router's `Routes` and `Route` components:

```jsx
<Routes>
  <Route path="/" element={<OptionsStrategyAnalyzer />} />
  <Route path="/trades-query" element={<OptionsTradesQueryPage />} />
  <Route path="/trade-list" element={<TradeListPage />} />
  <Route path="/trade-parser" element={<TradeParserPage />} />
  <Route path="/api-test" element={<APITestPage />} />
  <Route path="/direct-test" element={<DirectTestPage />} />
</Routes>
```

### Server-Side Support
The Express server is configured to support client-side routing in both development and production environments:

```javascript
// Serve static files from the React app in production
if (process.env.NODE_ENV === 'production') {
  const path = require('path');
  app.use(express.static(path.join(__dirname, 'build')));

  // Handle React routing, return all requests to React app
  app.get('*', function(req, res) {
    // Skip API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.sendFile(path.join(__dirname, 'build', 'index.html'));
  });
}
```

## Technical Details

### Why Force Page Reloads?
The application uses forced page reloads (`window.location.href`) instead of React Router's client-side navigation because:

1. It ensures components are properly re-rendered when navigating between pages
2. It resolves issues with component state not updating correctly with client-side navigation
3. It provides a more reliable user experience, eliminating the need for manual page refreshes

### Development Proxy Configuration
In development mode, API requests are proxied to the backend server using `http-proxy-middleware`:

```javascript
// src/setupProxy.js
const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:5000',
      changeOrigin: true,
    })
  );
};
```

### Available Pages
The application includes the following pages:

1. **Strategy Builder** (`/`): Main options strategy analysis page
2. **Trades Query** (`/trades-query`): Query and filter trades
3. **Trade List** (`/trade-list`): View all trades with filtering capabilities
4. **Trade Parser** (`/trade-parser`): Parse and upload trades
5. **API Test** (`/api-test`): Test API endpoints (development only)
6. **Direct Test** (`/direct-test`): Direct testing page (development only)

## Trade-offs and Considerations

### Advantages
- Reliable navigation without manual page refreshes
- Simple implementation that works across all browsers
- No complex state management required for navigation

### Disadvantages
- Full page reloads are slower than client-side navigation
- In-memory state is lost between page navigations
- Less smooth user experience compared to true SPA navigation

## Future Improvements
Potential future improvements to the routing implementation:

1. Investigate and resolve the underlying issues with React Router's client-side navigation
2. Implement a state management solution (Redux, Context API) to persist state between page reloads
3. Consider using a different routing library if React Router continues to cause issues
4. Add loading indicators during page transitions to improve user experience
