"use client";

import type * as React from 'react';
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Wand2, Loader2 } from "lucide-react";

interface TradeInputFormProps {
  rawText: string;
  onRawTextChange: (text: string) => void;
  onParse: () => void;
  isParsing: boolean;
}

export function TradeInputForm({ rawText, onRawTextChange, onParse, isParsing }: TradeInputFormProps) {
  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      onRawTextChange(text);
    } catch (err) {
      console.error('Failed to read clipboard contents: ', err);
      // Potentially show a toast message if permission denied or other error
    }
  };
  
  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="text-xl">Input Trade Data</CardTitle>
        <CardDescription>
          Paste your trade data from your trading app into the text area below.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Textarea
          placeholder="Paste your trade data here... Example:
HHI7200R5	Index 2025-06 7200 Put	2@400.00	...
HSIK5	Index Future 2025-05	2@23037.00	..."
          value={rawText}
          onChange={(e) => onRawTextChange(e.target.value)}
          rows={10}
          className="min-h-[200px] text-sm"
        />
        <div className="flex flex-col sm:flex-row gap-2">
          <Button onClick={handlePaste} variant="outline" className="w-full sm:w-auto">
            Paste from Clipboard
          </Button>
          <Button onClick={onParse} disabled={isParsing || !rawText.trim()} className="w-full sm:w-auto flex-grow">
            {isParsing ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Wand2 className="mr-2 h-4 w-4" />
            )}
            Parse Trades
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
