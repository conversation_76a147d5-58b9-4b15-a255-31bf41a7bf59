# Firebase Integration

## Overview
The application now integrates with Firebase Firestore to store and retrieve options trades data. This document outlines the implementation details, data structure, and API endpoints.

## Firebase Configuration
- Firebase Admin SDK is initialized in `utils/firebase.ts`
- The service account key is expected to be in `firebase.json` at the project root
- The Firestore database instance is exported as `db` for use throughout the application

## Data Structure
The trades collection in Firestore has the following structure:

```
trades/
  {document_id}/
    expiry: string (e.g., "2025-05-16")
    id: string (auto-generated)
    originalLine: string (original trade text)
    price: number (option premium)
    quantity: number
    strike: string (strike price)
    ticker: string (option ticker)
    type: string ("Call" or "Put")
    uploadedAt: timestamp
```

## API Endpoints
Two new API endpoints have been created to interact with Firestore:

1. `GET /api/firebase-expiry-dates`
   - Retrieves all unique expiry dates from the trades collection
   - Returns: `{ expiryDates: string[] }`

2. `GET /api/firebase-trades-by-expiry?expiryDate={date}`
   - Retrieves all trades for a specific expiry date
   - Query parameter: `expiryDate` (format: YYYY-MM-DD)
   - Returns: `{ trades: Trade[] }`

## Component Integration
The `OptionsTradesQuery` component has been updated to:
- Fetch expiry dates from `/api/firebase-expiry-dates`
- Fetch trades for a selected expiry date from `/api/firebase-trades-by-expiry`
- Handle Firestore-specific error messages
- Display trades grouped by strike price and type (Call/Put)

## Error Handling
- Proper error handling for Firestore connection issues
- Integration with the existing database offline indicator
- Graceful degradation when Firestore is unavailable

## Data Mapping
Firestore data is mapped to the expected frontend format:
- `id`: Document ID from Firestore
- `quantity`: Trade quantity
- `type`: "Call" or "Put"
- `strike`: Strike price (parsed as float)
- `ExpiryDate`: Expiry date string
- `premium`: Option price
- `debitCredit`: Calculated as quantity * price
- `created_at`: Timestamp from uploadedAt field
- `ticker`: Option ticker symbol
- `originalLine`: Original trade text

## Future Enhancements
- Add ability to upload trades directly to Firestore
- Implement real-time updates using Firestore listeners
- Add filtering by additional criteria (ticker, price range, etc.)
