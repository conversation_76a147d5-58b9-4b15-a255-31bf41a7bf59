// Standalone script to calculate premium for a put option
const { calculatePositionPremium } = require('./position-utils');

var position = {
  type: 'Put',
  strike: 22600,
  daysToExpiry: 50,
};
const stockPrice = 24800;
const riskFreeRate = 0;
const volatility = 20;

var premium = calculatePositionPremium(position, stockPrice, riskFreeRate, volatility);
console.log('Calculated premium:', premium);

var position = {
  type: 'Call',
  strike: 25200,
  daysToExpiry: 50,
};

var premium = calculatePositionPremium(position, stockPrice, riskFreeRate, volatility);
console.log('Calculated premium:', premium);
