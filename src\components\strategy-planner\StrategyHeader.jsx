import React, { useState } from 'react';
import StrategyEditModal from './StrategyEditModal';

/**
 * Strategy Header Component
 * 
 * Displays the strategy window header with strategy information,
 * window controls, and status indicators.
 */
const StrategyHeader = ({
  window,
  strategyMetadata,
  onClose,
  onMinimize,
  onTileOrMaximize,
  isLoading,
  error,
  onUpdateStrategy
}) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const formatDate = (date) => {
    if (!date) return '';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getStatusIcon = () => {
    if (error) {
      return (
        <svg className="h-4 w-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    }
    
    if (isLoading) {
      return (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
      );
    }
    
    return (
      <svg className="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    );
  };

  return (
    <div className="flex items-center justify-between">
      {/* Strategy Info */}
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        {/* Status Icon */}
        <div className="flex-shrink-0">
          {getStatusIcon()}
        </div>
        
        {/* Strategy Title */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="text-sm font-medium text-gray-900 truncate">
              Strategy: {strategyMetadata?.name || window.strategyName || 'Loading...'}
            </h3>
            {strategyMetadata?.stockSymbol && (
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                {strategyMetadata.stockSymbol}
              </span>
            )}
          </div>
          
          {/* Strategy Details */}
          <div className="text-xs text-gray-500 mt-1">
            <span>ID: {window.strategyId}</span>
            {strategyMetadata?.createdAt && (
              <>
                <span className="mx-2">•</span>
                <span>Created: {formatDate(strategyMetadata.createdAt)}</span>
              </>
            )}
            {strategyMetadata?.lastModified && (
              <>
                <span className="mx-2">•</span>
                <span>Modified: {formatDate(strategyMetadata.lastModified)}</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Window Controls */}
      <div className="window-controls flex items-center space-x-1 ml-4">
        {/* Edit Button */}
        <button
          className="p-1 rounded hover:bg-gray-200 text-gray-500 hover:text-gray-700"
          title="Edit Strategy"
          onClick={(e) => {
            e.stopPropagation();
            setShowEditModal(true);
          }}
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        </button>

        {/* Minimize Button */}
        <button
          className="p-1 rounded hover:bg-gray-200 text-gray-500 hover:text-gray-700"
          title="Minimize"
          onClick={(e) => {
            e.stopPropagation();
            onMinimize();
          }}
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        </button>

        {/* Tile/Maximize Button */}
        <button
          className="p-1 rounded hover:bg-gray-200 text-gray-500 hover:text-gray-700"
          title={window?.isMaximized ? "Restore" : "Tile/Maximize"}
          onClick={(e) => {
            e.stopPropagation();
            onTileOrMaximize();
          }}
        >
          {window?.isMaximized ? (
            // Restore icon (overlapping squares)
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h8a2 2 0 012 2v4m-6 9V9a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2z" />
            </svg>
          ) : (
            // Maximize icon (single square)
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4h16v16H4V4z" />
            </svg>
          )}
        </button>

        {/* Close Button */}
        <button
          className="p-1 rounded hover:bg-red-100 text-gray-500 hover:text-red-700"
          title="Close"
          onClick={(e) => {
            e.stopPropagation();
            onClose();
          }}
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Edit Strategy Modal */}
      <StrategyEditModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onUpdateStrategy={onUpdateStrategy}
        strategy={strategyMetadata}
      />
    </div>
  );
};

export default StrategyHeader;
