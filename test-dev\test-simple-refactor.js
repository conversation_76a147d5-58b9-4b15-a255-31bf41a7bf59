/**
 * Simple test to verify the refactored loadExchangeHolidays() function
 */

// Test the main function
console.log('🧪 Testing Refactored loadExchangeHolidays()');
console.log('==============================================');

try {
  // Import using dynamic import to handle ES module
  import('../src/utils/position-utils.js').then(({ loadExchangeHolidays, calculateDaysToExpiry }) => {
    console.log('\n1. Testing holiday loading:');
    const holidays = loadExchangeHolidays();
    
    console.log(`📅 Loaded ${holidays.length} holidays`);
    
    // Check for critical holiday
    const oct7_2025 = holidays.find(h => 
      h.getFullYear() === 2025 && h.getMonth() === 9 && h.getDate() === 7
    );
    
    if (oct7_2025) {
      console.log('✅ Critical holiday 2025-10-07 found');
    } else {
      console.log('❌ Critical holiday 2025-10-07 NOT found');
    }
    
    console.log('\n2. Testing days to expiry calculation:');
    const daysToOct7 = calculateDaysToExpiry('2025-10-07', new Date('2025-10-06'));
    console.log(`📊 Trading days from 2025-10-06 to 2025-10-07: ${daysToOct7}`);
    
    if (daysToOct7 === 0) {
      console.log('✅ October 7, 2025 correctly identified as holiday');
    } else {
      console.log(`❌ Expected 0 trading days, got ${daysToOct7}`);
    }
    
    console.log('\n✅ Refactored function works correctly!');
  }).catch(error => {
    console.error('❌ Error testing ES module:', error.message);
  });
  
} catch (error) {
  console.error('❌ Error:', error.message);
}