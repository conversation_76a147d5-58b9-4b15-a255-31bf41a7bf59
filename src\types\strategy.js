/**
 * Strategy Planner Data Models
 * 
 * This file contains all the data models and interfaces used by the Strategy Planner feature.
 * These models define the structure for strategies, strategy windows, and simulation parameters.
 */

/**
 * @typedef {Object} Strategy
 * @property {string} id - Unique strategy identifier (auto-generated)
 * @property {string} name - User-defined strategy name
 * @property {string} stockSymbol - Underlying asset symbol
 * @property {string} [description] - Optional strategy description
 * @property {Date} createdAt - Strategy creation timestamp
 * @property {Date} lastModified - Last modification timestamp
 * @property {string} [userId] - User ID for future multi-user support
 */

/**
 * @typedef {Object} StrategyWindow
 * @property {string} id - Unique window identifier
 * @property {string} strategyId - Associated strategy ID
 * @property {boolean} isMinimized - Window minimization state
 * @property {Object} position - Window position coordinates
 * @property {number} position.x - X coordinate
 * @property {number} position.y - Y coordinate
 * @property {Object} size - Window dimensions
 * @property {number} size.width - Window width
 * @property {number} size.height - Window height
 */

/**
 * @typedef {Object} SimulationParameters
 * @property {number} stockPrice - Current stock price for simulation
 * @property {number} volatility - Implied volatility percentage
 * @property {number} riskFreeRate - Risk-free interest rate percentage
 * @property {Date} targetDate - Target date for P&L calculation
 */

/**
 * @typedef {Object} StrategyMetadata
 * @property {Strategy} strategy - Strategy information
 * @property {number} tradesCount - Number of trades in strategy
 * @property {number} totalPremium - Total premium of all trades
 * @property {number} netDelta - Net delta exposure
 * @property {number} netGamma - Net gamma exposure
 * @property {number} netTheta - Net theta exposure
 * @property {number} netVega - Net vega exposure
 */

/**
 * @typedef {Object} PnLResults
 * @property {Array<Object>} priceAnalysis - P&L at various price points
 * @property {Array<Object>} timeDecayAnalysis - P&L over time
 * @property {Array<Object>} volatilityAnalysis - P&L at various volatility levels
 * @property {Date} calculatedAt - Timestamp of calculation
 * @property {SimulationParameters} parameters - Parameters used for calculation
 */

/**
 * Strategy creation form data
 * @typedef {Object} StrategyFormData
 * @property {string} name - Strategy name
 * @property {string} stockSymbol - Stock symbol
 * @property {string} description - Strategy description
 */

/**
 * Strategy validation errors
 * @typedef {Object} StrategyValidationErrors
 * @property {string} [name] - Name validation error
 * @property {string} [stockSymbol] - Stock symbol validation error
 * @property {string} [description] - Description validation error
 */

/**
 * Strategy table filter options
 * @typedef {Object} StrategyTableFilters
 * @property {string} search - Search query
 * @property {string} stockSymbol - Filter by stock symbol
 * @property {string} sortBy - Sort column
 * @property {string} sortOrder - Sort direction (asc/desc)
 * @property {number} page - Current page number
 * @property {number} pageSize - Items per page
 */

// Export types for use in other modules
export const StrategyTypes = {
  // Default values
  DEFAULT_STRATEGY: {
    id: '',
    name: '',
    stockSymbol: '',
    description: '',
    createdAt: new Date(),
    lastModified: new Date(),
    userId: null
  },
  
  DEFAULT_WINDOW: {
    id: '',
    strategyId: '',
    isMinimized: false,
    isMaximized: false,
    position: { x: 100, y: 100 },
    size: { width: 800, height: 600 },
    originalState: undefined
  },
  
  DEFAULT_SIMULATION_PARAMS: {
    stockPrice: 100,
    volatility: 25,
    riskFreeRate: 2.5,
    targetDate: new Date()
  },
  
  // Validation rules
  VALIDATION_RULES: {
    NAME_MAX_LENGTH: 100,
    DESCRIPTION_MAX_LENGTH: 500,
    STOCK_SYMBOL_MAX_LENGTH: 10,
    MAX_CONCURRENT_WINDOWS: 5
  },
  
  // Strategy ID generation
  generateStrategyId: () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `STR-${timestamp}-${random}`;
  },
  
  // Window ID generation
  generateWindowId: () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `WIN-${timestamp}-${random}`;
  }
};
