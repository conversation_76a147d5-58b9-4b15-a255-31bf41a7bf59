# Technical Tutorial: Building Interactive Options Strategy Charts with Chart.js and Recharts

This tutorial explains the implementation of the Strategy Chart and Time Decay Chart components in the Options Analyzer application. These charts visualize profit/loss scenarios for options strategies at different price points and over time, with interactive controls, zoom/pan functionality, and enhanced mouse-over effects.

## Overview

### Strategy Chart (PnL Chart)
1. A line chart showing profit/loss at different stock prices
2. Two data series: current value and value at expiry
3. Vertical reference lines for market price and analysis price
4. Interactive controls to adjust the analysis price
5. Mouse-over effects with crosshair and tooltips to display values along chart lines
6. Mouse wheel zoom and pan functionality for detailed analysis

### Time Decay Chart
1. A line chart showing how profit/loss changes over time at a specific stock price
2. Multiple data series: one for each position plus total P/L
3. Actual dates on the x-axis instead of numeric values
4. Mouse-over effects with tooltips to display values
5. Mouse wheel zoom and pan functionality for detailed analysis
6. Dynamic legend that only shows positions with data in the current view

## Technology Stack

- **React**: For component-based UI
- **Chart.js**: For rendering the Strategy Chart
- **chartjs-plugin-annotation**: For adding vertical reference lines
- **chartjs-plugin-zoom**: For zoom and pan functionality in the Strategy Chart
- **Custom Crosshair Plugin**: For displaying vertical line following the cursor
- **Recharts**: For the Time Decay Chart with enhanced tooltips
- **Tailwind CSS**: For styling

## Component Structure

The component accepts several props:
- `daysToVisualize`: Days until the analysis date
- `chartData`: Array of price/profit data points
- `selectedPrice`: Currently selected price for analysis
- `setSelectedPrice`: Function to update the selected price
- `stock`: Current stock price for analysis
- `marketPrice`: Current market price from ticker

## Implementation Details

### 1. Chart.js Setup

```jsx
// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  annotationPlugin
);
```

We register all necessary Chart.js components, including the annotation plugin for vertical reference lines.

### 2. State Management

```jsx
// State to track the input value
const [sliderValue, setSliderValue] = useState(selectedPrice);

// Reference to the chart instance
const chartRef = useRef(null);
```

The component maintains its own state for the input value and keeps a reference to the chart instance for direct manipulation.

### 3. Data Transformation

```jsx
// Prepare data for Chart.js with x,y coordinates for linear scale
const currentData = chartData.map(item => ({
  x: item.price,
  y: item.current
}));

const expiryData = chartData.map(item => ({
  x: item.price,
  y: item.expiry
}));
```

The input data is transformed into the format expected by Chart.js, with explicit x,y coordinates for the linear scale.

### 4. Chart Configuration

```jsx
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      type: 'linear',
      title: { display: true, text: 'Price', font: { size: 10 } },
      // min and max set dynamically in useEffect
    },
    y: {
      title: { display: true, text: 'PROFIT / LOSS', font: { size: 10 } },
      ticks: {
        font: { size: 10 },
        callback: function(value) { return '$' + value; }
      }
    }
  },
  plugins: {
    // Legend, tooltip, and annotation configurations
  }
};
```

The chart options define the appearance and behavior of the chart, including axes, scales, and plugins.

### 5. Annotations for Reference Lines

```jsx
annotation: {
  drawTime: 'afterDatasetsDraw',
  common: { drawTime: 'afterDraw' },
  annotations: {
    marketPriceLine: {
      type: 'line',
      scaleID: 'x',
      value: marketPrice,
      borderColor: '#2563eb',
      borderWidth: 2,
      drawTime: 'afterDraw',
      z: 100, // Ensure it's drawn on top
      label: {
        display: true,
        content: `Market: $${marketPrice ? marketPrice.toFixed(2) : '0.00'}`,
        position: 'top',
        backgroundColor: 'rgba(0,0,0,0)',
        color: '#2563eb',
        font: { size: 10 }
      }
    },
    stockPriceLine: {
      // Similar configuration for stock price line
    }
  }
}
```

The annotation plugin is used to add vertical reference lines for the market price and analysis price.

### 6. Chart Data

```jsx
const data = {
  datasets: [
    {
      label: `In ${daysToVisualize} Days`,
      data: currentData,
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.5)',
      tension: 0.1,
      pointRadius: 0,
      pointHoverRadius: 8
    },
    {
      label: 'At Expiry',
      data: expiryData,
      borderColor: '#f97316',
      backgroundColor: 'rgba(249, 115, 22, 0.5)',
      tension: 0.1,
      pointRadius: 0,
      pointHoverRadius: 8
    },
    // Empty datasets for legend entries
  ]
};
```

The chart data includes two main datasets (current and expiry values) and two empty datasets that are used only for the legend.

### 7. Interactive Controls

```jsx
<div className="flex items-center justify-center">
  <span className="text-xs text-gray-700 mr-2">Stock Price for Analysis:</span>
  <div className="flex" tabIndex={0} role="group" aria-label="Target price controls">
    <button
      onClick={() => {
        const newValue = Math.max(0, roundToNearestHundred(sliderValue - 100));
        setSliderValue(newValue);
        setSelectedPrice(newValue);
      }}
      className="px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-l-md border border-gray-300"
      aria-label="Decrease target price by 100"
    >
      <span className="font-bold">-</span>
    </button>

    <input
      type="text"
      value={`$${sliderValue.toFixed(2)}`}
      onChange={handlePriceInputChange}
      onBlur={handlePriceInputBlur}
      onKeyDown={handlePriceInputKeyPress}
      className="w-24 py-1 text-center border-t border-b border-gray-300 text-red-600 font-medium"
      aria-label="Current target price"
    />

    <button
      onClick={() => {
        const newValue = roundToNearestHundred(sliderValue + 100);
        setSliderValue(newValue);
        setSelectedPrice(newValue);
      }}
      className="px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-r-md border border-gray-300"
      aria-label="Increase target price by 100"
    >
      <span className="font-bold">+</span>
    </button>
  </div>
</div>
```

The interactive controls include buttons to adjust the price by ±100 and an editable input field.

### 8. Utility Functions

```jsx
// Function to round to nearest hundred
const roundToNearestHundred = (value) => {
  return Math.round(value / 100) * 100;
};

// Handle input change for the price input
const handlePriceInputChange = (e) => {
  let value = e.target.value;

  // Remove dollar sign if present
  value = value.replace(/^\$/, '');

  // Remove any non-numeric characters except decimal point
  value = value.replace(/[^\d.]/g, '');

  // Parse the value to a number
  const numValue = parseFloat(value);

  // Update the slider value if it's a valid number
  if (!isNaN(numValue)) {
    setSliderValue(numValue);
  }
};
```

Utility functions handle rounding to the nearest hundred and input validation.

### 9. Keyboard Navigation

```jsx
// Handle keyboard navigation for the entire component
const handleKeyDown = (e) => {
  // Left arrow decreases the price by 100
  if (e.key === 'ArrowLeft') {
    const newValue = Math.max(0, roundToNearestHundred(sliderValue - 100));
    setSliderValue(newValue);
    setSelectedPrice(newValue);
    e.preventDefault();
  }
  // Right arrow increases the price by 100
  else if (e.key === 'ArrowRight') {
    const newValue = roundToNearestHundred(sliderValue + 100);
    setSliderValue(newValue);
    setSelectedPrice(newValue);
    e.preventDefault();
  }
};

// Add keyboard event listener when component mounts
useEffect(() => {
  window.addEventListener('keydown', handleKeyDown);

  // Remove event listener on cleanup
  return () => {
    window.removeEventListener('keydown', handleKeyDown);
  };
}, [sliderValue]);
```

Keyboard navigation allows users to adjust the price using arrow keys.

## Key Technical Challenges and Solutions

### 1. Disappearing Reference Lines

**Problem**: Reference lines would disappear after user interactions.

**Solution**:
- Switched from Recharts to Chart.js for better control
- Added a key prop to force re-rendering when values change
- Used the annotation plugin for more reliable reference lines

```jsx
<Line
  key={`chart-${marketPrice}-${stock}`} /* Add key to force re-render */
  ref={chartRef}
  options={chartOptions}
  data={data}
/>
```

### 2. Dynamic Chart Range

**Problem**: Chart range needed to adjust based on the stock price.

**Solution**: Dynamically calculate the x-axis range in a useEffect hook.

```jsx
// Update the x-axis range to show a reasonable view around the stock price
if (chart.options && chart.options.scales && chart.options.scales.x) {
  const stockValue = stock || 100; // Default to 100 if stock is 0 or undefined
  const rangePercentage = 0.05; // 5% range
  const minValue = stockValue * (1 - rangePercentage);
  const maxValue = stockValue * (1 + rangePercentage);

  chart.options.scales.x.min = minValue;
  chart.options.scales.x.max = maxValue;
}
```

### 3. Precise Price Control

**Problem**: Slider was too sensitive for precise price selection.

**Solution**: Replaced slider with buttons that adjust by fixed increments and round to the nearest hundred.

```jsx
const newValue = roundToNearestHundred(sliderValue + 100);
setSliderValue(newValue);
setSelectedPrice(newValue);
```

### 4. Enhanced Mouse-Over Effects

**Problem**: Users needed to see exact values when hovering over chart lines.

**Solution**: Implemented a custom crosshair plugin and enhanced tooltip configuration.

```jsx
// Custom crosshair plugin implementation
export const Crosshair = {
  id: 'crosshair',

  // Default configuration
  defaults: {
    line: {
      color: 'rgba(100, 100, 100, 0.5)',
      width: 1,
      dashPattern: [5, 5]
    }
  },

  afterEvent: function(chart, args) {
    const { event, inChartArea } = args;

    // Only process mousemove events
    if (event.type !== 'mousemove') {
      return;
    }

    // Update crosshair position
    chart.crosshair.x = event.x;
    chart.crosshair.active = inChartArea;

    // Request a redraw
    chart.draw();
  },

  afterDraw: function(chart, args, options) {
    // Draw vertical line following the cursor
    if (chart.crosshair.active) {
      const ctx = chart.ctx;
      const x = chart.crosshair.x;
      const chartArea = chart.chartArea;

      ctx.save();
      ctx.beginPath();
      ctx.lineWidth = options.line.width;
      ctx.strokeStyle = options.line.color;
      ctx.setLineDash(options.line.dashPattern);
      ctx.moveTo(x, chartArea.top);
      ctx.lineTo(x, chartArea.bottom);
      ctx.stroke();
      ctx.restore();
    }
  }
};

// Enhanced tooltip configuration
tooltip: {
  mode: 'index',
  intersect: false,
  callbacks: {
    title: function(tooltipItems) {
      return `Price: $${parseFloat(tooltipItems[0].parsed.x).toFixed(2)}`;
    },
    label: function(context) {
      let label = context.dataset.label || '';
      if (label) {
        label += ': ';
      }
      if (context.parsed.y !== null) {
        label += '$' + context.parsed.y.toFixed(2);
      }
      return label;
    }
  }
}
```

## Zoom and Pan Functionality

### Strategy Chart (Chart.js)

The Strategy Chart uses the `chartjs-plugin-zoom` plugin to provide zoom and pan functionality:

```jsx
// Import the zoom plugin
import zoomPlugin from 'chartjs-plugin-zoom';

// Register the plugin with Chart.js
ChartJS.register(
  // Other plugins...
  zoomPlugin,
);

// Configure zoom options in the chart options
const chartOptions = {
  // Other options...
  plugins: {
    zoom: {
      pan: {
        enabled: true,
        mode: 'x',
      },
      zoom: {
        wheel: {
          enabled: true,
        },
        pinch: {
          enabled: true
        },
        mode: 'x',
        speed: 0.1,
        threshold: 2,
      },
      limits: {
        x: {
          // min and max set dynamically in useEffect
        }
      }
    },
    // Other plugins...
  }
};
```

The zoom plugin is configured to:
- Enable mouse wheel zooming on the x-axis
- Enable pinch zooming for touch devices
- Enable panning by dragging the chart
- Set appropriate zoom speed and threshold
- Set zoom limits based on the stock price

A reset zoom button is added to allow users to return to the default view:

```jsx
// Function to reset zoom
const resetZoom = () => {
  if (chartRef.current) {
    chartRef.current.resetZoom();
  }
};

// Reset zoom button in the JSX
<button
  onClick={resetZoom}
  className="px-2 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 text-xs rounded border border-gray-300"
  title="Reset zoom"
>
  Reset Zoom
</button>
```

### Time Decay Chart (Recharts)

Since Recharts doesn't have built-in zoom support, we implemented a custom solution:

1. **State Management**: Track zoom level, zoom center, and panning state
```jsx
const [zoomLevel, setZoomLevel] = useState(1);
const [zoomCenter, setZoomCenter] = useState(0.5);
const [isPanning, setIsPanning] = useState(false);
const [panStartX, setPanStartX] = useState(0);
```

2. **Data Filtering**: Show only a subset of data based on zoom level and center
```jsx
const getVisibleData = () => {
  // If no zoom, return all data
  if (zoomLevel <= 1) return fullData;

  // Calculate visible window size
  const windowSize = Math.floor(fullData.length / zoomLevel);

  // Calculate center index and start/end indices
  const centerIndex = Math.floor(zoomCenter * (fullData.length - 1));
  const startIndex = Math.max(0, centerIndex - Math.floor(windowSize / 2));
  const endIndex = Math.min(fullData.length - 1, startIndex + windowSize);

  // Return visible slice
  return fullData.slice(startIndex, endIndex + 1);
};
```

3. **Mouse Wheel Handler**: Zoom in/out centered on mouse position
```jsx
const handleWheel = (e) => {
  e.preventDefault();

  // Calculate new zoom level
  const zoomFactor = e.deltaY > 0 ? 0.8 : 1.25;
  const newZoomLevel = Math.max(1, Math.min(10, zoomLevel * zoomFactor));

  // Calculate new zoom center based on mouse position
  const rect = chartContainer.getBoundingClientRect();
  const mouseX = e.clientX - rect.left;
  const newCenter = mouseX / rect.width;

  // Update state
  setZoomLevel(newZoomLevel);
  setZoomCenter(newCenter);
};
```

4. **Panning**: Drag to pan when zoomed in
```jsx
// Mouse down handler
const handleMouseDown = (e) => {
  if (zoomLevel > 1) {
    setIsPanning(true);
    setPanStartX(e.clientX);
    chartContainer.style.cursor = 'grabbing';
  }
};

// Mouse move handler
const handleMouseMove = (e) => {
  if (isPanning) {
    const deltaX = e.clientX - panStartX;
    const panAmount = deltaX / containerWidth;
    const newCenter = Math.max(0, Math.min(1, zoomCenter - panAmount * 0.05));
    setZoomCenter(newCenter);
    setPanStartX(e.clientX);
  }
};
```

5. **Visual Feedback**: Change cursor and show reset button when zoomed
```jsx
// Dynamic class names based on state
className={`chart-container ${zoomLevel > 1 ? 'can-pan' : ''} ${isPanning ? 'panning' : ''}`}

// Reset zoom button
<button onClick={resetZoom} className="...">Reset Zoom</button>
```

6. **Dynamic Legend**: Only show legends for positions with data in the current view
```jsx
<Legend
  payload={getVisibleData().length > 0 ?
    positions
      .filter(position => {
        const dataPoint = getVisibleData()[0];
        return dataPoint && dataPoint[`position${position.id}`] !== undefined;
      })
      .map((position, index) => ({
        id: position.id,
        type: 'line',
        value: `${position.type} ${position.strike} (${position.quantity > 0 ? 'Long' : 'Short'})`,
        color: getPositionColor(index)
      }))
      .concat([{
        id: 'total',
        type: 'line',
        value: 'Total P/L',
        color: '#000000'
      }])
    : []
  }
/>
```

## Conclusion

The Strategy Chart and Time Decay Chart components provide interactive visualizations of options strategy performance. By using Chart.js with the annotation and zoom plugins for the Strategy Chart, and implementing custom zoom and pan functionality for the Time Decay Chart, we've created robust charts that allow users to explore data at different levels of detail.

The Strategy Chart uses the built-in capabilities of Chart.js and its plugins, while the Time Decay Chart demonstrates how to implement custom zoom and pan functionality in Recharts. Both approaches provide a consistent user experience with mouse wheel zooming, panning, and reset functionality.

The combination of buttons and an editable input field gives users precise control over the analysis price, while the enhanced mouse-over effects with tooltips and crosshair allow users to easily read exact values along the chart lines. The dynamic legend in the Time Decay Chart ensures that only relevant information is displayed, reducing clutter and improving usability.

This implementation demonstrates several React best practices:
- Using refs to access and manipulate the underlying chart instance
- Managing component state with useState and useEffect
- Providing keyboard navigation for accessibility
- Forcing re-renders when necessary with key props
- Handling input validation and formatting
- Implementing custom plugins to enhance user experience
- Using tooltips and visual cues to display data values on hover
