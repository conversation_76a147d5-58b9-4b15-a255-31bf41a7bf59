# Yahoo Finance API Integration (yahoo-finance2)

## Overview
The Options Strategy Analyzer application uses the yahoo-finance2 npm package to fetch stock information and historical price data from Yahoo Finance.

## API Package
- Package: [yahoo-finance2](https://www.npmjs.com/package/yahoo-finance2)
- Version: Latest
- GitHub: [gadicc/node-yahoo-finance2](https://github.com/gadicc/node-yahoo-finance2)

## Implementation

### Utility Functions
The application implements two main utility functions in `utils/stockUtils.js`:

1. `getSymbolInfo(symbol)`: Fetches current information for a stock symbol
   - Parameters:
     - `symbol` (string): The stock symbol to look up (e.g., '^HSI', '0700.HK')
   - Returns: Promise resolving to an object with stock information
   - Implementation:
     - Uses yahoo-finance2's `quote` function to fetch real-time stock data
     - Returns a formatted response with key stock information

2. `getHistoricalPrices(symbol, startDate, endDate, frequency)`: Fetches historical price data
   - Parameters:
     - `symbol` (string): The stock symbol
     - `startDate` (Date): Start date for historical data
     - `endDate` (Date): End date for historical data
     - `frequency` (string): Data frequency ('1d', '1wk', or '1mo')
   - Returns: Promise resolving to an object with historical price data
   - Implementation:
     - Uses yahoo-finance2's `historical` function to fetch historical data
     - Maps the response to a consistent format with OHLC data

### API Endpoints
The application exposes two API endpoints:

1. `GET /api/stock/:symbol`: Get current information for a stock symbol
   - URL Parameters:
     - `symbol`: The stock symbol (e.g., '^HSI', '0700.HK')
   - Response: JSON object with stock information

2. `GET /api/stock/:symbol/history`: Get historical price data for a stock symbol
   - URL Parameters:
     - `symbol`: The stock symbol
   - Query Parameters:
     - `startDate`: Start date in YYYY-MM-DD format
     - `endDate`: End date in YYYY-MM-DD format
     - `frequency`: Data frequency ('1d', '1wk', or '1mo'), defaults to '1d'
   - Response: JSON object with historical price data

## Ticker Component
The application includes a Ticker component (`src/components/ticker.jsx`) that:

1. Displays current stock information:
   - Symbol name and short name
   - Current price
   - Price change and percentage
   - Day range (high/low)
   - Volume and exchange information
   - Options IV (hardcoded as 30% initially)

2. Allows symbol selection:
   - Predefined symbols: HSI, HHI, HTI, MHI
   - Custom symbol input option
   - Automatically prefixes index symbols with '^' when needed

3. Integration with other components:
   - Updates the stock price in StrategyParameters when new data is received
   - Provides visual feedback during loading and error states
   - Current stock price is linked to and updates from Ticker.stockInfo.regularMarketPrice

## Symbol Handling
- Index symbols (HSI, HHI, HTI, MHI) are automatically prefixed with '^' when sent to the API
- Hong Kong stocks use the format '0700.HK', '9988.HK', etc.
- The component handles both predefined symbols and custom user input

## Data Refresh
- Stock data is fetched when the symbol changes
- Future enhancements may include automatic refresh at regular intervals

## Error Handling
- Provides visual feedback during loading states
- Displays error messages when API requests fail
- Includes fallback mechanisms when data cannot be retrieved

## Rate Limiting Considerations
- Yahoo Finance API has rate limits that may affect performance with frequent requests
- The application should implement caching mechanisms for frequently accessed data
- Consider implementing throttling for API requests to avoid hitting rate limits

## Testing
A test script (`test-yahoo-api.js`) is provided to test the API functionality with various symbols.

### Test Cases
1. Get Hang Seng Index (^HSI) information
2. Get Tencent (0700.HK) information
3. Get Alibaba (9988.HK) information
4. Get historical data for Hang Seng Index for the last 30 days

## Example Usage

### Fetching Stock Information
```javascript
// Client-side example
const fetchStockInfo = async (symbol) => {
  try {
    const response = await fetch(`/api/stock/${encodeURIComponent(symbol)}`);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching stock info:', error);
    return { error: true, message: error.message };
  }
};

// Usage
fetchStockInfo('^HSI').then(data => {
  console.log('Hang Seng Index info:', data);
});
```

### Fetching Historical Data
```javascript
// Client-side example
const fetchHistoricalData = async (symbol, startDate, endDate, frequency = '1d') => {
  try {
    const url = `/api/stock/${encodeURIComponent(symbol)}/history?startDate=${startDate}&endDate=${endDate}&frequency=${frequency}`;
    const response = await fetch(url);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching historical data:', error);
    return { error: true, message: error.message };
  }
};

// Usage
const today = new Date();
const thirtyDaysAgo = new Date(today);
thirtyDaysAgo.setDate(today.getDate() - 30);

const startDate = thirtyDaysAgo.toISOString().split('T')[0]; // Format: YYYY-MM-DD
const endDate = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD

fetchHistoricalData('^HSI', startDate, endDate).then(data => {
  console.log('Hang Seng Index historical data:', data);
});
```
