// Comprehensive test script to verify "calc prem" calculations
import { calculateOptionPrice } from './black-scholes-robust.js';
import { calculatePositionPremium } from './position-utils.js';

// Test matrix for different system parameters
const stockPrices = [22000, 22600, 23000];
const daysToExpiry = [0, 3, 30, 90];
const volatilities = [20, 30, 40];
const riskFreeRates = [1, 2, 3];

// Test positions
const testPositions = [
  {
    id: 'call-atm',
    type: 'Call',
    strike: 22600,
    description: 'Call At-The-Money'
  },
  {
    id: 'call-itm',
    type: 'Call',
    strike: 22000,
    description: 'Call In-The-Money'
  },
  {
    id: 'call-otm',
    type: 'Call',
    strike: 23000,
    description: 'Call Out-of-The-Money'
  },
  {
    id: 'put-atm',
    type: 'Put',
    strike: 22600,
    description: 'Put At-The-Money'
  },
  {
    id: 'put-itm',
    type: 'Put',
    strike: 23000,
    description: 'Put In-The-Money'
  },
  {
    id: 'put-otm',
    type: 'Put',
    strike: 22000,
    description: 'Put Out-of-The-Money'
  },
  {
    id: 'future',
    type: 'Future',
    strike: 'N/A',
    description: 'Future'
  }
];

// Function to run comprehensive tests
function runComprehensiveTests() {
  console.log("=== COMPREHENSIVE CALC PREM VERIFICATION ===");
  console.log("Testing all combinations of system parameters");
  
  // Test specific cases mentioned in the requirements
  console.log("\n=== SPECIFIC TEST CASE FROM REQUIREMENTS ===");
  const specificPosition = {
    id: 'specific-call',
    type: 'Call',
    strike: 22200,
    daysToExpiry: 3
  };
  
  const specificPremium = calculatePositionPremium(
    specificPosition,
    22600, // Stock price
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  
  console.log(`Call option (S=22600, K=22200, r=2%, v=30%, T=3 days): ${specificPremium.toFixed(2)}`);
  
  // Expected to be around 500 according to requirements
  console.log(`Expected: ~500, Actual: ${specificPremium.toFixed(2)}`);
  
  const specificPutPosition = {
    id: 'specific-put',
    type: 'Put',
    strike: 22200,
    daysToExpiry: 3
  };
  
  const specificPutPremium = calculatePositionPremium(
    specificPutPosition,
    22600, // Stock price
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  
  console.log(`Put option (S=22600, K=22200, r=2%, v=30%, T=3 days): ${specificPutPremium.toFixed(2)}`);
  
  // Expected to be around 100 according to requirements
  console.log(`Expected: ~100, Actual: ${specificPutPremium.toFixed(2)}`);
  
  // Test at expiry with stock price equal to strike
  console.log("\n=== AT EXPIRY WITH STOCK PRICE = STRIKE ===");
  const atExpiryAtmCall = {
    id: 'expiry-atm-call',
    type: 'Call',
    strike: 22600,
    daysToExpiry: 0
  };
  
  const atExpiryAtmCallPremium = calculatePositionPremium(
    atExpiryAtmCall,
    22600, // Stock price
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  
  console.log(`Call at expiry (S=22600, K=22600): ${atExpiryAtmCallPremium.toFixed(2)}`);
  console.log(`Expected: 0, Actual: ${atExpiryAtmCallPremium.toFixed(2)}`);
  
  // Run matrix tests
  console.log("\n=== MATRIX TESTS ===");
  
  // For brevity, we'll just test a few combinations
  for (const stockPrice of [22000, 23000]) {
    for (const days of [0, 30]) {
      for (const volatility of [30]) {
        for (const riskFreeRate of [2]) {
          console.log(`\nTesting with S=${stockPrice}, T=${days} days, v=${volatility}%, r=${riskFreeRate}%`);
          
          for (const position of testPositions) {
            const positionWithDays = {
              ...position,
              daysToExpiry: days
            };
            
            const premium = calculatePositionPremium(
              positionWithDays,
              stockPrice,
              riskFreeRate,
              volatility
            );
            
            console.log(`${position.description}: ${premium.toFixed(2)}`);
            
            // Verify with direct Black-Scholes calculation for options
            if (position.type !== 'Future') {
              const directPremium = calculateOptionPrice(
                position.type,
                stockPrice,
                position.strike,
                riskFreeRate / 100,
                volatility / 100,
                days / 365
              );
              
              console.log(`  Direct B-S: ${directPremium.toFixed(2)}, Difference: ${(premium - directPremium).toFixed(4)}`);
            }
          }
        }
      }
    }
  }
}

// Run the tests
runComprehensiveTests();

// Export a function to run the tests
export function verifyCalcPrem() {
  runComprehensiveTests();
}
