/**
 * Test proper holiday date comparison and fix timezone issues
 */

console.log('🧪 Testing Holiday Date Comparison Logic...\n');

// Test the current holiday loading
const exchangeHolidays = require('../src/server-utils/exchange-holidays.js');

console.log('=== 1. Current Holiday Loading Results ===');
const holidays = exchangeHolidays.loadExchangeHolidaysFromFile();
console.log(`📅 Loaded ${holidays.length} holidays from XML`);

// Find the Oct 7, 2025 holiday
const oct7Holiday = holidays.find(h => 
  h.getFullYear() === 2025 && h.getMonth() === 9 && h.getDate() === 7
);

if (oct7Holiday) {
  console.log('\n✅ Found October 7, 2025 holiday:');
  console.log(`   toString(): ${oct7Holiday.toString()}`);
  console.log(`   toISOString(): ${oct7Holiday.toISOString()}`);
  console.log(`   toLocaleDateString(): ${oct7Holiday.toLocaleDateString()}`);
  console.log(`   getFullYear(): ${oct7Holiday.getFullYear()}`);
  console.log(`   getMonth(): ${oct7Holiday.getMonth()} (October = 9)`);
  console.log(`   getDate(): ${oct7Holiday.getDate()}`);
}

console.log('\n=== 2. Proper Date Comparison Functions ===');

// Timezone-agnostic date comparison function
function isSameLocalDate(date1, date2) {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
}

// Timezone-agnostic date formatting
function formatLocalDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

console.log('Testing proper date comparison:');

// Test date we're looking for
const testDate = new Date(2025, 9, 7, 12, 0, 0, 0); // Oct 7, 2025 at noon local time
console.log(`Test date: ${formatLocalDate(testDate)} (${testDate.toString()})`);

// Check if Oct 7 holiday matches using proper comparison
const matches = isSameLocalDate(oct7Holiday, testDate);
console.log(`✅ Proper date comparison result: ${matches}`);

console.log('\n=== 3. Demonstrating the UTC vs Local Issue ===');

console.log('Using ISO string comparison (WRONG):');
holidays.slice(0, 5).forEach((holiday, index) => {
  const isoDate = holiday.toISOString().split('T')[0];
  const localDate = formatLocalDate(holiday);
  console.log(`  ${index + 1}. ISO: ${isoDate} vs Local: ${localDate}`);
});

console.log('\nUsing local date formatting (CORRECT):');
holidays.slice(0, 5).forEach((holiday, index) => {
  const localDate = formatLocalDate(holiday);
  const dayName = holiday.toLocaleDateString('en-US', { weekday: 'short' });
  console.log(`  ${index + 1}. ${localDate} (${dayName})`);
});

console.log('\n=== 4. Verifying Critical Holiday Detection ===');

// Test the specific date we care about
const targetDate = new Date(2025, 9, 7); // October 7, 2025
const foundHoliday = holidays.find(h => isSameLocalDate(h, targetDate));

if (foundHoliday) {
  console.log('✅ 2025-10-07 holiday found using proper date comparison');
  console.log(`   Holiday date: ${formatLocalDate(foundHoliday)}`);
  console.log(`   Target date: ${formatLocalDate(targetDate)}`);
} else {
  console.log('❌ 2025-10-07 holiday NOT found using proper date comparison');
}

console.log('\n=== 5. Testing Holiday Detection Function ===');

// Test the isHoliday function logic
function isHoliday(date, holidayList) {
  return holidayList.some(holiday => isSameLocalDate(holiday, date));
}

const testDates = [
  new Date(2025, 9, 6),  // Oct 6, 2025 (not a holiday)
  new Date(2025, 9, 7),  // Oct 7, 2025 (should be holiday)
  new Date(2025, 9, 8),  // Oct 8, 2025 (not a holiday)
];

console.log('Testing holiday detection:');
testDates.forEach(date => {
  const isHol = isHoliday(date, holidays);
  const dateStr = formatLocalDate(date);
  const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
  console.log(`  ${dateStr} (${dayName}): ${isHol ? '🏖️ HOLIDAY' : '💼 Working day'}`);
});

console.log('\n🎯 Holiday Date Comparison Test Complete!');