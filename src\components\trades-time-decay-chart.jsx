import React, { useEffect, useRef, useMemo } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, TimeScale } from 'chart.js';
import { Line } from 'react-chartjs-2';
import annotationPlugin from 'chartjs-plugin-annotation';
import zoomPlugin from 'chartjs-plugin-zoom';
import { Crosshair } from '../utils/crosshair-plugin';
import 'chartjs-adapter-date-fns'; // Time scale adapter
import 'hammerjs'; // Required for touch gesture support in chartjs-plugin-zoom
import useAllTradesStore from '../store/useAllTradesStore';
import useAnalysisStore from '../store/useAnalysisStore';
import usePnLAtVariousDateStore from '../store/usePnLAtVariousDateStore';
import FilteredTradesDateTable from './FilteredTradesDateTable';
import FilteredTradesThetaTable from './FilteredTradesThetaTable';

import './chart-styles.css';

// Register Chart.js components and plugins
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  annotationPlugin,
  zoomPlugin,
  Crosshair
);

/**
 * Trades Time Decay Chart Component
 *
 * This component displays a professional Chart.js-based chart showing how the total profit changes over time 
 * (from today to max expiry) for filtered trades at the target stock price from the analysis store.
 * Features include zoom/pan capabilities, market price annotations, enhanced tooltips, a data table,
 * and expiry date filtering.
 */
const TradesTimeDecayChart = ({ selectedExpiryDate }) => {
  const chartRef = useRef(null);
  
  // Store subscriptions
  const { allTrades, isLoading: tradesLoading } = useAllTradesStore();
  const { targetStockPrice, volatility, riskFreeRate, symbol: selectedStockSymbol, stockInfo } = useAnalysisStore();  const {
    positionData,
    isCalculating,
    calculatePnLAtVariousDates,
    getChartData,
    getFilteredData,
    getGlobalPnLBounds
  } = usePnLAtVariousDateStore();
  const marketPrice = stockInfo?.response?.regularMarketPrice;

  // Calculate global P&L bounds for consistent y-axis scaling across parameter changes
  const yAxisBounds = useMemo(() => {
    return getGlobalPnLBounds();
  }, [getGlobalPnLBounds]);
  const filteredTrades = useMemo(() => {
    if (!selectedStockSymbol || !allTrades || allTrades.length === 0) {
      return [];
    }
    
    // Filter trades using the 'stock' property (first 3 characters of ticker)
    // This matches the pattern used throughout the codebase
    const filtered = allTrades.filter(trade => {
      // Use the stock property if available, otherwise extract from ticker (fallback)
      const tradeStock = trade.stock || (trade.ticker ? trade.ticker.substring(0, 3) : null);
      return tradeStock === selectedStockSymbol;
    });
      return filtered;
  }, [allTrades, selectedStockSymbol]);

  // Filter trades by selected symbol and expiry date
  const expiredFilteredTrades = useMemo(() => {
    let trades = filteredTrades;

    if (selectedExpiryDate && selectedExpiryDate !== 'ALL') {
      trades = trades.filter(trade => trade.ExpiryDate === selectedExpiryDate);
    }

    return trades;
  }, [filteredTrades, selectedExpiryDate]);

  // Get filtered date data for the table
  const filteredDateData = useMemo(() => {
    if (!positionData || positionData.length === 0) {
      return { positionData: [], totals: {}, datePoints: [] };
    }

    // Create filters object for the store
    const filters = {};

    // Filter by expiry date if not 'ALL'
    if (selectedExpiryDate && selectedExpiryDate !== 'ALL') {
      filters.expiry = selectedExpiryDate;
    }

    // Get filtered data from the store
    return getFilteredData(filters);
  }, [positionData, selectedExpiryDate, getFilteredData]);

  // Calculate data when parameters change
  useEffect(() => {
    if (expiredFilteredTrades.length > 0 && targetStockPrice && volatility !== undefined && riskFreeRate !== undefined) {
      calculatePnLAtVariousDates({
        trades: expiredFilteredTrades, // Use expiry-filtered trades
        targetStockPrice,
        volatility,
        riskFreeRate
      });
    }
  }, [expiredFilteredTrades, targetStockPrice, volatility, riskFreeRate, calculatePnLAtVariousDates]);
  // Function to reset zoom
  const resetZoom = () => {
    if (chartRef.current) {
      console.log('Resetting zoom for chart:', chartRef.current);
      chartRef.current.resetZoom();
    } else {
      console.log('Chart ref is null, cannot reset zoom');
    }
  };

  // Add event listeners and debugging
  useEffect(() => {
    const chart = chartRef.current;
    if (!chart) return;

    console.log('Chart zoom plugin registered:', chart.config.plugins?.zoom);
    console.log('Chart instance:', chart);

    const handleDoubleClick = () => {
      console.log('Double-click detected, resetting zoom');
      resetZoom();
    };

    // Add double-click event listener to the canvas
    chart.canvas.addEventListener('dblclick', handleDoubleClick);

    // Add wheel event listener for debugging
    const handleWheel = (e) => {
      console.log('Wheel event detected:', e);
    };
    chart.canvas.addEventListener('wheel', handleWheel);

    // Cleanup event listeners
    return () => {
      if (chart.canvas) {
        chart.canvas.removeEventListener('dblclick', handleDoubleClick);
        chart.canvas.removeEventListener('wheel', handleWheel);
      }
    };
  }, []);
  // Get chart data and process it for Chart.js
  const rawChartData = getChartData();

  // Transform Recharts data format to Chart.js format and prepare individual trade data
  const processedChartData = useMemo(() => {
    if (!rawChartData || rawChartData.length === 0) {
      return [];
    }    return rawChartData.map(item => ({
      x: new Date(item.date), // Convert to Date object for time scale
      y: item.total // Total P&L for primary line
    }));
  }, [rawChartData]);
  // Process individual trade data for individual lines
  const individualTradeData = useMemo(() => {
    if (!positionData || positionData.length === 0 || !rawChartData || rawChartData.length === 0) {
      return [];
    }    const tradesWithPoints = positionData.map((position) => {
      const tradePoints = rawChartData.map(item => ({
        x: new Date(item.date), // Convert to Date object for time scale
        y: position[item.date]?.pnl || 0
      }));
      
      return {
        trade: position.trade,
        points: tradePoints
      };
    });

    // Sort trades by PnL at the leftmost date (+0D)
    // The leftmost date is the first date in rawChartData (earliest date)
    if (rawChartData.length > 0) {
      const leftmostDate = rawChartData[0].date;
      
      tradesWithPoints.sort((a, b) => {
        // Find PnL at leftmost date for each trade
        const aPnL = a.points.find(point => point.x === leftmostDate)?.y || 0;
        const bPnL = b.points.find(point => point.x === leftmostDate)?.y || 0;
        
        // Sort in descending order (highest PnL first)
        return bPnL - aPnL;
      });
    }

    return tradesWithPoints;
  }, [positionData, rawChartData]);
  // Prepare Chart.js data structure with individual trades and totals
  const chartData = useMemo(() => {
    if (!processedChartData || processedChartData.length === 0) {
      return { datasets: [] };
    }

    const datasets = [];

    // Add individual trade datasets (thinner lines)
    individualTradeData.forEach((tradeData, index) => {
      const trade = tradeData.trade;
      const tradeLabel = `${trade.ticker || 'Unknown'} ${trade.strike}${trade.type?.[0]?.toUpperCase() || ''} ${trade.ExpiryDate ? new Date(trade.ExpiryDate).toLocaleDateString() : ''}`;
      
      // Generate a color for this trade
      const hue = (index * 137.5) % 360; // Golden angle for good distribution
      const color = `hsl(${hue}, 70%, 50%)`;
      const lightColor = `hsla(${hue}, 70%, 50%, 0.1)`;

      datasets.push({
        label: tradeLabel,
        data: tradeData.points,
        borderColor: color,
        backgroundColor: lightColor,
        borderWidth: 1,
        tension: 0.1,
        pointRadius: 1,
        pointHoverRadius: 3,
        pointBackgroundColor: color,
        pointBorderColor: 'white',
        pointBorderWidth: 1,
      });
    });    // Add total P&L dataset (thick black line, prominent)
    datasets.push({
      label: `Total P&L for ${selectedStockSymbol || 'Selected Stock'}`,
      data: processedChartData.map(item => ({ x: item.x, y: item.y })),
      borderColor: 'rgb(0, 0, 0)',
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
      borderWidth: 3,
      tension: 0.1,
      pointRadius: 4,
      pointHoverRadius: 7,
      pointBackgroundColor: 'rgb(0, 0, 0)',
      pointBorderColor: 'rgb(255, 255, 255)',
      pointBorderWidth: 2,
      order: -1, // Ensure total line is rendered on top
    });

    return { datasets };
  }, [processedChartData, individualTradeData, selectedStockSymbol]);  // Format date for display
  const formatDate = (dateStr) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}-${day}`;
  };



  // Chart.js options with professional styling and features
  const chartOptions = useMemo(() => {
    const annotations = {};    // Add market price annotation if available
    if (typeof marketPrice === 'number' && processedChartData.length > 0) {
      // Find the current date or closest date in our data
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Normalize to start of day
      
      // Find the closest date in our data to today
      const todayData = processedChartData.find(item => {
        const itemDate = new Date(item.x);
        itemDate.setHours(0, 0, 0, 0);
        return itemDate.getTime() === today.getTime();
      }) || processedChartData[0];
      
      if (todayData) {
        annotations.todayLine = {
          type: 'line',
          xMin: todayData.x,
          xMax: todayData.x,
          borderColor: 'rgb(255, 99, 132)',
          borderWidth: 2,
          borderDash: [6, 6],
          label: {
            content: `Today`,
            enabled: true,
            position: 'start',
            backgroundColor: 'rgba(255, 99, 132, 0.7)',
            color: 'white',
            font: { size: 10 },
            padding: 2,
            yAdjust: -10,
          },
        };
      }
    }

    return {
      responsive: true,
      maintainAspectRatio: false,
      animation: false,
      interaction: {
        mode: 'index',
        intersect: false,
      },      scales: {
        x: {          type: 'time',
          time: {
            unit: 'day',
            displayFormats: {
              day: 'MM-dd'
            }
          },
          title: {
            display: true,
            text: 'Date',
            font: { size: 12, weight: 'bold' },
          },
          ticks: {
            font: { size: 10 },
            maxTicksLimit: 10,
          },
          grid: {
            color: 'rgba(200, 200, 200, 0.2)',
          },
        },        y: {
          title: {
            display: true,
            text: 'Profit / Loss',
            font: { size: 12, weight: 'bold' },
          },
          ticks: {
            font: { size: 10 },
            callback: function (value) {
              return '$' + value.toLocaleString();
            },
          },          grid: {
            color: 'rgba(200, 200, 200, 0.2)',
          },          // Use fixed bounds if provided for consistent scaling with safety checks
          ...(yAxisBounds && 
              yAxisBounds.min !== undefined && 
              yAxisBounds.max !== undefined && 
              typeof yAxisBounds.min === 'number' && 
              typeof yAxisBounds.max === 'number' &&
              yAxisBounds.min < yAxisBounds.max && {
            min: yAxisBounds.min,
            max: yAxisBounds.max
          })
        },
      },
      plugins: {
          legend: {
            position: 'top',
            labels: { 
              font: { size: 10 },
              usePointStyle: true,
              pointStyle: 'line',
              filter: function(legendItem, chartData) {
                // Show only Total P&L and Market Value in legend to avoid clutter
                const label = legendItem.text;
                return label.includes('Total P&L') || label.includes('Total Market Value');
              },
            },
          },
        // title: {
        //   display: true,
        //   text: `Time Decay Analysis for ${selectedStockSymbol || 'Trades'}`,
        //   font: { size: 16, weight: 'bold' },
        // },          
        tooltip: {
            mode: 'index',
            intersect: false,
            filter: function(tooltipItem) {
              // Show all individual trades and totals in tooltip
              return true;
            },            callbacks: {
              title: function(context) {
                const date = context[0]?.parsed?.x;
                if (date) {
                  return `Date: ${formatDate(new Date(date).toISOString().split('T')[0])}`;
                }
                return 'Date: Unknown';
              },
              label: function (context) {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                if (context.parsed.y !== null) {
                  label += new Intl.NumberFormat('en-US', { 
                    style: 'currency', 
                    currency: 'USD' 
                  }).format(context.parsed.y);
                }
                return label;
              },
              afterBody: function(tooltipItems) {
                // Add a summary showing total individual trades
                const individualTradesItems = tooltipItems.filter(item => 
                  !item.dataset.label.includes('Total P&L') && 
                  !item.dataset.label.includes('Total Market Value')
                );
                
                if (individualTradesItems.length > 0) {
                  const individualTotal = individualTradesItems.reduce((sum, item) => sum + item.parsed.y, 0);
                  return [`\nIndividual trades sum: ${new Intl.NumberFormat('en-US', { 
                    style: 'currency', 
                    currency: 'USD' 
                  }).format(individualTotal)}`];
                }
                return [];
              },
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: 'white',
            bodyColor: 'white',
            borderColor: 'rgba(255, 255, 255, 0.2)',
            borderWidth: 1,
          },        annotation: {
          annotations: annotations,
        },
        crosshair: {
          line: {
            color: 'rgba(100, 100, 100, 0.7)',
            width: 1,
            dashPattern: [5, 5]
          },
          sync: {
            enabled: true,
            group: 1,
            suppressTooltips: false
          },
          snap: {
            enabled: true
          }
        },zoom: {
          pan: {
            enabled: true,
            mode: 'xy',
          },
          zoom: {
            wheel: {
              enabled: true,
              speed: 0.1
            },
            pinch: {
              enabled: true
            },
            mode: 'xy',
            onZoomComplete: function({chart: _chart}) {
              // Optional: Add any zoom complete logic here
            }
          },
          limits: {
            x: {
              min: 'original',
              max: 'original',
              minRange: 1
            },
            y: {
              min: 'original',
              max: 'original',
              minRange: 100
            }
          }
        },
      },    };
  }, [marketPrice, processedChartData, yAxisBounds]);

  // Loading state
  if (tradesLoading || isCalculating) {
    return (
      <div className="chart-container bg-white p-4 rounded-lg shadow-md" style={{ height: '400px' }}>
        <h2 className="text-lg font-bold mb-3 text-gray-800">
          Time Decay Analysis for {selectedStockSymbol || 'All Symbols'}
        </h2>
        <div className="flex items-center justify-center" style={{ height: '300px' }}>
          <p className="text-gray-500">Loading trades and calculating time decay...</p>
        </div>
      </div>
    );
  }
  // Error handling for no trades
  if (expiredFilteredTrades.length === 0 && filteredTrades.length === 0) {
    return (
      <div className="chart-container bg-white p-4 rounded-lg shadow-md" style={{ height: '400px' }}>
        <h2 className="text-lg font-bold mb-3 text-gray-800">
          Time Decay Analysis for {selectedStockSymbol || 'All Symbols'}
        </h2>
        <div className="flex items-center justify-center" style={{ height: '300px' }}>
          <p className="text-gray-500">
            {selectedStockSymbol 
              ? `No trades available for ${selectedStockSymbol}. Please select a different symbol or load trades.` 
              : 'No symbol selected. Please select a symbol to see the time decay analysis.'
            }
          </p>
        </div>
      </div>
    );
  }

  // Show expiry filter info when trades are filtered
  if (expiredFilteredTrades.length === 0 && filteredTrades.length > 0) {
    return (        <div className="chart-container bg-white p-4 rounded-lg shadow-md" style={{ height: '400px' }}>
        <h2 className="text-lg font-bold mb-3 text-gray-800">
          Time Decay Analysis for {selectedStockSymbol || 'All Symbols'}
        </h2>
        <div className="flex items-center justify-center" style={{ height: '300px' }}>
          <p className="text-gray-500">
            No trades found for the selected expiry date. {filteredTrades.length} total trades available.
          </p>
        </div>
      </div>
    );
  }

  // Error handling for no chart data
  if (processedChartData.length === 0) {
    return (
      <div className="chart-container bg-white p-4 rounded-lg shadow-md" style={{ height: '400px' }}>
        <h2 className="text-lg font-bold mb-3 text-gray-800">
          Time Decay Analysis for {selectedStockSymbol || 'All Symbols'}
          <span className="ml-2 text-xs text-gray-500">
            @{targetStockPrice ? targetStockPrice.toLocaleString() : '0'}, IV: {volatility || 0}%, RFR: {riskFreeRate || 0}% ({filteredTrades.length} trades)
          </span>
        </h2>
        <div className="flex items-center justify-center" style={{ height: '300px' }}>
          <p className="text-gray-500">No chart data available. Please check your analysis parameters.</p>
        </div>
      </div>
    );
  }
  // Main chart render
  return (
    <div className="space-y-4">
      {/* Chart Section */}
      <div className="chart-container bg-white p-4 rounded-lg shadow-md relative" style={{ height: '450px' }}>
        <h2 className="text-lg font-bold mb-1 text-gray-800">
          Time Decay Analysis for {selectedStockSymbol || 'All Symbols'}
          <span className="ml-2 text-xs text-gray-500">
            Expiry:{selectedExpiryDate} @{targetStockPrice ? targetStockPrice.toLocaleString() : '0'}, IV: {volatility || 0}%, RFR: {riskFreeRate || 0}% ({expiredFilteredTrades.length} trades)
          </span>
        </h2>

        {/* Reset Zoom Button */}
        <div className="absolute top-2 right-2 z-10">
          <button
            onClick={resetZoom}
            className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded border border-blue-600 shadow-sm hover:shadow-md transition-colors"
            title="Reset zoom and pan to original view"
          >
            Reset Zoom
          </button>
        </div>

        {/* Chart Instructions */}
        <div className="absolute bottom-2 left-2 z-10">
          <div className="text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded">
            Mouse wheel: zoom • Drag: pan • Double-click: reset
          </div>
        </div>

        <div style={{ height: '320px' }}>
          <Line ref={chartRef} options={chartOptions} data={chartData} />
        </div>
      </div>      {/* Integrated Table Extension */}
      <FilteredTradesDateTable
        filteredDateData={filteredDateData}
        selectedExpiryDate={selectedExpiryDate}
        selectedStockSymbol={selectedStockSymbol}
      />

      {/* Theta Analysis Table */}
      <FilteredTradesThetaTable
        filteredDateData={filteredDateData}
        selectedExpiryDate={selectedExpiryDate}
        selectedStockSymbol={selectedStockSymbol}
      />
    </div>
  );
};

        export default TradesTimeDecayChart;
