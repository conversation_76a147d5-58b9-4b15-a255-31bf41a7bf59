/**
 * Debug the position-utils holiday detection specifically
 */

function debugPositionUtilsHolidays() {
  console.log('🔍 DEBUGGING POSITION-UTILS HOLIDAY DETECTION');
  console.log('=' .repeat(50));
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    // Read the position-utils file to understand current logic
    const utilsPath = path.join(process.cwd(), 'src', 'utils', 'position-utils.js');
    const utilsContent = fs.readFileSync(utilsPath, 'utf8');
    
    console.log('📁 Reading position-utils.js...');
    
    // Look for holiday-related functions
    const holidayFunctions = [
      'getFallbackHolidays',
      'isHongKongExchangeHoliday', 
      'calculateTradingDays',
      'loadExchangeHolidays',
      'calculateDaysToExpiry'
    ];
    
    console.log('\n🔍 Searching for holiday functions:');
    holidayFunctions.forEach(funcName => {
      if (utilsContent.includes(funcName)) {
        console.log(`✅ Found: ${funcName}`);
        
        // Find and extract the function
        const patterns = [
          `function ${funcName}`,
          `const ${funcName}`,
          `export function ${funcName}`,
          `${funcName} =`,
          `${funcName}:`
        ];
        
        let funcStart = -1;
        let matchedPattern = '';
        
        for (const pattern of patterns) {
          funcStart = utilsContent.indexOf(pattern);
          if (funcStart !== -1) {
            matchedPattern = pattern;
            break;
          }
        }
        
        if (funcStart !== -1) {
          // Extract a portion of the function
          const funcContent = utilsContent.substring(funcStart, funcStart + 400);
          console.log(`📝 ${funcName} (${matchedPattern}):`);
          console.log('   ' + funcContent.split('\n').slice(0, 8).join('\n   '));
          console.log('   ...\n');
        }
      } else {
        console.log(`❌ Missing: ${funcName}`);
      }
    });
    
    // Check if there's any reference to the XML file or API endpoint
    const xmlReferences = [
      'hkex_holidays.xml',
      '/api/exchange-holidays',
      'loadExchangeHolidays',
      'fetch(',
      'localStorage'
    ];
    
    console.log('🔍 Checking for XML/API references:');
    xmlReferences.forEach(ref => {
      const found = utilsContent.includes(ref);
      console.log(`   ${ref}: ${found ? '✅ FOUND' : '❌ NOT FOUND'}`);
      
      if (found && ref === 'loadExchangeHolidays') {
        // Count how many times it's used
        const matches = (utilsContent.match(new RegExp(ref, 'g')) || []).length;
        console.log(`     (used ${matches} times)`);
      }
    });
    
    // Look for hardcoded 2025 dates
    console.log('\n📅 Searching for hardcoded dates:');
    const dateMatches = utilsContent.match(/new Date\([^)]*2025[^)]*\)/g);
    if (dateMatches) {
      console.log('   Found hardcoded 2025 dates:');
      dateMatches.forEach((date, index) => {
        console.log(`   ${index + 1}: ${date}`);
        
        // Check if this is the problematic October date
        if (date.includes('9, 7') || date.includes('10, 7')) {
          console.log(`     🎯 This might be related to 2025-10-07!`);
        }
      });
    } else {
      console.log('   No hardcoded 2025 dates found');
    }
    
    // Check for the calculateDaysToExpiry function specifically
    if (utilsContent.includes('calculateDaysToExpiry')) {
      console.log('\n🎯 ANALYZING calculateDaysToExpiry function:');
      
      const funcStart = utilsContent.indexOf('export function calculateDaysToExpiry') ||
                       utilsContent.indexOf('function calculateDaysToExpiry') ||
                       utilsContent.indexOf('calculateDaysToExpiry =');
      
      if (funcStart !== -1) {
        // Find the end of the function
        const funcContent = utilsContent.substring(funcStart);
        const funcEnd = funcContent.indexOf('\nexport') || 
                       funcContent.indexOf('\nfunction') || 
                       funcContent.indexOf('\n\n/**') ||
                       1000;
        
        const fullFunction = funcContent.substring(0, funcEnd);
        
        console.log('   Key parts of the function:');
        
        // Check if it calls loadExchangeHolidays
        if (fullFunction.includes('loadExchangeHolidays')) {
          console.log('   ✅ Calls loadExchangeHolidays()');
        } else {
          console.log('   ❌ Does NOT call loadExchangeHolidays()');
        }
        
        // Check if it has holiday checking logic
        if (fullFunction.includes('isHongKongExchangeHoliday') || 
            fullFunction.includes('holiday') ||
            fullFunction.includes('exchange')) {
          console.log('   ✅ Contains holiday checking logic');
        } else {
          console.log('   ❌ No holiday checking logic found');
        }
        
        // Check for weekend logic
        if (fullFunction.includes('getDay()') || fullFunction.includes('weekend')) {
          console.log('   ✅ Contains weekend logic');
        } else {
          console.log('   ❌ No weekend logic found');
        }
        
        console.log('\n   Function preview:');
        console.log('   ' + fullFunction.split('\n').slice(0, 15).join('\n   '));
        console.log('   ...');
      }
    }
    
  } catch (error) {
    console.error('❌ Error reading position-utils.js:', error.message);
  }
}

// Test if we can access the holiday data by simulating browser environment
function testHolidayDataAccess() {
  console.log('\n🧪 TESTING HOLIDAY DATA ACCESS');
  console.log('=' .repeat(35));
  
  try {
    // Mock localStorage for testing
    global.localStorage = {
      data: {},
      getItem: function(key) {
        return this.data[key] || null;
      },
      setItem: function(key, value) {
        this.data[key] = value;
      }
    };
    
    // Read the XML file and simulate what should be in localStorage
    const fs = require('fs');
    const path = require('path');
    
    const xmlPath = path.join(process.cwd(), 'output', 'hkex_holidays.xml');
    const xmlContent = fs.readFileSync(xmlPath, 'utf8');
    
    // Simulate cached XML data
    localStorage.setItem('hkex_holidays_xml', xmlContent);
    localStorage.setItem('hkex_holidays_timestamp', Date.now().toString());
    
    console.log('✅ Simulated localStorage with XML data');
    console.log(`📊 XML size: ${xmlContent.length} characters`);
    
    // Test parsing the XML as the position-utils would
    console.log('\n🔍 Testing XML parsing (simulating browser DOMParser):');
    
    // Since we're in Node.js, we'll use jsdom to simulate DOMParser
    const { JSDOM } = require('jsdom');
    const dom = new JSDOM();
    global.window = dom.window;
    global.document = dom.window.document;
    global.DOMParser = dom.window.DOMParser;
    
    const parser = new DOMParser();
    const doc = parser.parseFromString(xmlContent, 'text/xml');
    
    // Check for parsing errors
    const parseError = doc.querySelector('parsererror');
    if (parseError) {
      console.log('❌ XML parsing error:', parseError.textContent);
    } else {
      console.log('✅ XML parsed successfully');
      
      // Extract holidays the same way position-utils should
      const holidays = [];
      const rows = doc.querySelectorAll('tr');
      
      console.log(`📋 Found ${rows.length} table rows`);
      
      let oct7Found = false;
      
      for (const row of rows) {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
          const dateText = cells[0].textContent.trim();
          const datePart = dateText.split(' ')[0];
          
          if (datePart && datePart.includes('/')) {
            try {
              const [day, month, year] = datePart.split('/');
              const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
              holidays.push(holidayDate);
              
              // Check if this is our target date
              if (datePart === '7/10/2025') {
                oct7Found = true;
                console.log(`🎯 Found 2025-10-07: ${holidayDate.toISOString().split('T')[0]}`);
              }
            } catch (e) {
              // Skip invalid dates
            }
          }
        }
      }
      
      console.log(`📅 Parsed ${holidays.length} holidays total`);
      console.log(`🎯 2025-10-07 found: ${oct7Found ? '✅ YES' : '❌ NO'}`);
      
      // Test the specific date that's causing issues
      const testDate = new Date('2025-10-07');
      const isHoliday = holidays.some(h => 
        h.getFullYear() === testDate.getFullYear() &&
        h.getMonth() === testDate.getMonth() &&
        h.getDate() === testDate.getDate()
      );
      
      console.log(`🔍 Is 2025-10-07 in parsed holidays: ${isHoliday ? '✅ YES' : '❌ NO'}`);
    }
    
  } catch (error) {
    console.error('❌ Error testing holiday data access:', error.message);
    
    // Try to install jsdom if missing
    if (error.message.includes("Cannot find module 'jsdom'")) {
      console.log('💡 jsdom not found - this is normal for Node.js testing');
      console.log('   The browser environment will use native DOMParser');
    }
  }
}

// Run the debug functions
function runDebug() {
  debugPositionUtilsHolidays();
  testHolidayDataAccess();
  
  console.log('\n🎯 DIAGNOSIS SUMMARY:');
  console.log('=' .repeat(25));
  console.log('1. position-utils.js should have holiday loading logic');
  console.log('2. XML data contains 2025-10-07 correctly');
  console.log('3. Need to verify the actual loading/parsing is working');
  console.log('4. Check if calculateDaysToExpiry is calling holiday functions');
}

if (require.main === module) {
  runDebug();
}

module.exports = { debugPositionUtilsHolidays, testHolidayDataAccess };