# System Patterns

## Architecture:
- React-based frontend application with PostgreSQL database
- Component-based architecture with modular design
- State management in parent components passed down as props
- RESTful API endpoints for database operations

## Key Technical Decisions:
- Use React for UI components
- Use Recharts for data visualization
- Use Tailwind CSS for styling
- Implement Black-Scholes model for options pricing with robust edge case handling
- Use PostgreSQL for database storage
- Handle dates without timezone considerations
- Implement loading states for better user experience

## Design Patterns:
- Component composition for UI organization
- Prop drilling for state management
- Unidirectional data flow (parent to child)
- Separation of concerns between UI components
- Loading state pattern to prevent premature user interaction
- Detailed error logging and user feedback

## Component Relationships:
- OptionsStrategyAnalyzer (parent component)
  - StrategyParameters (child component for setting stock price and other parameters)
  - PositionsTable (child component for managing options positions)
  - Sliders (child component for parameter controls)
  - StrategyChart (child component for P/L visualization)
  - TimeDecayChart (child component for time decay analysis)

## Critical Implementation Paths:
- Options position management (add, edit, remove)
- Black-Scholes calculation for theoretical pricing with robust edge case handling
- Chart data generation based on positions and parameters
- UI parameter controls affecting chart visualizations
- Database operations (save, load positions)
- Loading state management to prevent race conditions
- Date handling without timezone issues
- Zero stock price handling in all components
