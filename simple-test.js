// Simple test to verify the parsing logic
const fs = require('fs');

function log(message) {
  console.log(message);
  fs.appendFileSync('test-output.txt', message + '\n');
}

log('Testing @ position parsing...');

// Use the exact line from the terminal output
const testLine = "HSIN5\t 2025-07\t0@-800.00\t\t2@24780.00\t-2@24800.00\t\t0@-840.00\t24672\t42,000.00 HKD\t24445\t1.0000\t42,000.00 HKD\t50";

log('Input line: ' + testLine);

// Split by tabs and filter out empty parts
const parts = testLine.split('\t').map(part => part.trim()).filter(Boolean);
log('Parts after splitting by tabs: ' + JSON.stringify(parts));

// Find all parts with @ symbols
const atParts = parts.filter(part => part && part.includes('@'));
log('Parts containing @: ' + JSON.stringify(atParts));

// Get the last @ part
const lastAtPart = atParts.length > 0 ? atParts[atParts.length - 1] : null;
log('Last @ part: ' + lastAtPart);

if (lastAtPart) {
  const [qtyStr, priceStr] = lastAtPart.split('@');
  log('Quantity string: ' + qtyStr);
  log('Price string: ' + priceStr);

  const parsedQuantity = parseInt(qtyStr.replace(/[^0-9\-]/g, ''), 10);
  log('Parsed quantity (parseInt): ' + parsedQuantity);
  log('Is NaN? ' + isNaN(parsedQuantity));

  const quantity = !isNaN(parsedQuantity) ? parsedQuantity : 1;
  const price = parseFloat(priceStr.replace(/[^0-9\.\-]/g, '')) || 0;

  log('Final quantity: ' + quantity);
  log('Final price: ' + price);

  // Expected results
  log('');
  log('Expected quantity: 0');
  log('Expected price: -840');
  log('Quantity correct? ' + (quantity === 0));
  log('Price correct? ' + (Math.abs(price - (-840)) < 0.01));
}
