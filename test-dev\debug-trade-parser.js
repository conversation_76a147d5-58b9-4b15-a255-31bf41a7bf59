/**
 * Debug script to test the fixed extractTradeConfirmation function
 */

const fs = require('fs');
const path = require('path');
const { parseStatement } = require('../src/server-utils/statement-parser');

async function debugTradeConfirmation() {
  try {
    console.log('🔧 DEBUG: Testing Fixed Trade Confirmation Parser');
    console.log('=' .repeat(60));

    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const password = '6369';
    const pdfBuffer = fs.readFileSync(pdfPath);
    
    console.log('📖 Parsing PDF...');
    const parsedData = await parseStatement(pdfBuffer, password);
    
    console.log('📊 Results:');
    console.log(`Trade Confirmation count: ${parsedData.tradeConfirmation.length}`);
    
    if (parsedData.tradeConfirmation.length > 0) {
      console.log('✅ SUCCESS! Trades found:');
      parsedData.tradeConfirmation.forEach((trade, index) => {
        console.log(`\nTrade #${index + 1}:`);
        console.log(`  Date: ${trade.date}`);
        console.log(`  Order No: ${trade.orderNo}`);
        console.log(`  Market: ${trade.market}`);
        console.log(`  Instrument: ${trade.instrument}`);
        console.log(`  Expiry: ${trade.expiry}`);
        console.log(`  Quantity: ${trade.quantity}`);
        console.log(`  Currency: ${trade.currency}`);
        console.log(`  Strike Price: ${trade.strikePrice}`);
        console.log(`  Option Type: ${trade.optionType}`);
        console.log(`  Premium: ${trade.premium}`);
        console.log(`  Commission: ${trade.commission}`);
        console.log(`  Fees: ${trade.fees}`);
      });
    } else {
      console.log('❌ No trades found - debugging...');
      
      // Let's test the section extraction manually
      const PDFExtract = require('pdf.js-extract').PDFExtract;
      const pdfExtract = new PDFExtract();
      const tempFilePath = path.join(__dirname, '../temp_debug.pdf');
      fs.writeFileSync(tempFilePath, pdfBuffer);

      const extractOptions = { password: password.trim() };
      const data = await new Promise((resolve, reject) => {
        pdfExtract.extract(tempFilePath, extractOptions, (err, data) => {
          if (err) reject(err);
          else resolve(data);
        });
      });

      const fullText = data.pages.map(page =>
        page.content.map(item => item.str).join(' ')
      ).join('\n');

      fs.unlinkSync(tempFilePath);
      
      // Test section extraction
      console.log('\n🔍 Testing section extraction...');
      
      // Test case-insensitive search
      const tradeConfirmationIdx = fullText.toUpperCase().indexOf('TRADE CONFIRMATION');
      const localMarketIdx = fullText.toUpperCase().indexOf('LOCAL MARKET');
      const positionIdx = fullText.toUpperCase().indexOf('POSITION');
      
      console.log(`TRADE CONFIRMATION found at: ${tradeConfirmationIdx}`);
      console.log(`LOCAL MARKET found at: ${localMarketIdx}`);
      console.log(`POSITION found at: ${positionIdx}`);
      
      if (localMarketIdx !== -1 && positionIdx !== -1) {
        const section = fullText.substring(localMarketIdx, positionIdx);
        console.log(`\nExtracted section length: ${section.length} chars`);
        console.log('First 500 chars of section:');
        console.log(section.substring(0, 500));
        
        // Test the parsing function on some sample lines
        const lines = section.split('\n');
        console.log(`\nSection has ${lines.length} lines`);
        
        for (let i = 0; i < Math.min(lines.length, 10); i++) {
          const line = lines[i].trim();
          if (line.length > 0) {
            console.log(`\nLine ${i + 1}: "${line}"`);
            // Test if this line contains a date pattern
            if (/\d{2}\/\d{2}\/\d{4}/.test(line)) {
              console.log('  ✅ Contains date pattern');
            } else {
              console.log('  ❌ No date pattern');
            }
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

// Run the debug
debugTradeConfirmation();
