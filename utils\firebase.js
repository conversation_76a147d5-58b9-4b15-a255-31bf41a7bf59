const fs = require('fs');
const path = require('path');
const admin = require('firebase-admin');

// Path to your service account key file.
// firebase.json is expected to be in the project root.
const serviceAccountPath = path.join(process.cwd(), 'firebase.json');
let serviceAccount;

try {
  const fileContents = fs.readFileSync(serviceAccountPath, 'utf8');
  const parsedJson = JSON.parse(fileContents);
  // Perform a basic check to ensure it looks like a service account
  if (parsedJson && typeof parsedJson.project_id === 'string' && typeof parsedJson.private_key === 'string' && typeof parsedJson.client_email === 'string') {
    serviceAccount = parsedJson;
  } else {
    console.error(
      `Parsed content from ${serviceAccountPath} does not appear to be a valid Firebase service account key. It might be missing required fields like project_id, private_key, or client_email.`
    );
  }
} catch (e) {
  let errorMessage = `Failed to load or parse Firebase service account key from ${serviceAccountPath}. `;
  if (e instanceof Error) {
    errorMessage += `Error: ${e.message}`;
    if (e.message.includes('ENOENT')) {
      errorMessage += ` (File not found at ${serviceAccountPath})`;
    }
  } else {
    errorMessage += `Error: ${String(e)}`;
  }
  console.error(`${errorMessage} Firebase Admin SDK will not be initialized.`);
}

let firestoreDbInstance = null;

if (serviceAccount) {
  if (!admin.apps.length) {
    try {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
      firestoreDbInstance = admin.firestore();
      console.log("Firebase Admin SDK initialized successfully.");
    } catch (initError) {
      console.error("Firebase Admin SDK initialization error:", initError instanceof Error ? initError.message : String(initError));
    }
  } else {
    firestoreDbInstance = admin.firestore(); // App already initialized
  }
} else {
    // Error related to serviceAccount loading/parsing is logged above.
    // This else implies serviceAccount is undefined, so db will remain null.
}

const db = firestoreDbInstance;

/**
 * Uploads trades to Firebase Firestore
 * @param {Array} trades - Array of trade objects to upload
 * @returns {Promise<Object>} - Result of the upload operation
 */
async function uploadTradesToFirebase(trades) {
  if (!db) {
    let warningMessage = "Firebase Admin SDK (db) is not initialized. Upload to Firebase will be simulated. ";
    if (!serviceAccount) {
        warningMessage += `This is likely because the service account key could not be loaded/parsed from ${serviceAccountPath}. Please ensure 'firebase.json' is in the project root and is a valid service account key. `;
    } else if (!admin.apps.length || !admin.firestore()) {
        warningMessage += "This might be due to an SDK initialization failure or Firestore service being unavailable. Check previous logs for initialization errors. ";
    } else {
        warningMessage += "The reason for db not being initialized is unclear. ";
    }
    console.warn(`${warningMessage}Simulating successful upload for ${trades.length} trades as fallback.`);
    // Simulate network delay for fallback
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { success: true, count: trades.length, error: "Firebase not initialized, upload simulated." };
  }

  if (!trades || trades.length === 0) {
    return { success: true, count: 0, message: "No trades provided to upload." };
  }

  try {
    const tradesCollection = db.collection('trades');
    const batch = db.batch();

    trades.forEach((trade) => {
      const docRef = tradesCollection.doc(); // Auto-generate ID
      // Ensure all fields are correctly mapped and no undefined values are sent if not desired
      const tradeData = { ...trade };
      if (tradeData.originalLine === undefined) delete tradeData.originalLine; // Example: Firestore errors on `undefined`

      // Ensure we're using the new field names
      const updatedTradeData = {
        ...tradeData,
        // Convert old field names to new ones if they exist
        expiryDate: tradeData.expiryDate || tradeData.expiry,
        cost: tradeData.cost || tradeData.price,
        uploadedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      // Remove old field names if they exist
      if (updatedTradeData.expiry) delete updatedTradeData.expiry;
      if (updatedTradeData.price) delete updatedTradeData.price;

      batch.set(docRef, updatedTradeData);
    });

    await batch.commit();
    console.log(`${trades.length} trades uploaded successfully using Admin SDK.`);
    return { success: true, count: trades.length };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error uploading trades to Firebase with Admin SDK:', errorMessage, error);
    return { success: false, count: 0, error: errorMessage };
  }
}

module.exports = {
  db,
  uploadTradesToFirebase
};
