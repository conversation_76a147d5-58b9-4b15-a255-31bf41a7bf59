# %%
from lxml import html, etree
import requests as rq
from datetime import datetime
import pandas as pd
import pymysql
from sqlalchemy import create_engine
import calendar
import os
import pandas as pd
from pandas.tseries.offsets import MonthEnd, BMonthEnd
import numpy as np
import datetime as dt
from scipy.stats import norm
from scipy.optimize import bisect
# import matplotlib.dates as mpl_dates
# import matplotlib.pyplot as plt
from scipy.stats import norm
import math
import decimal # Added import for decimal

# Import option pricing functions from dedicated module
from OptionPricing import d1, d2, call_price, put_price, gamma, charm, calculate_implied_volatility as CalcIV
pathname = os.getenv('out_path')

def get_exchange_holidays():
    exchange_holidays = []
    # URL of the page containing the trading calendar
    url = "https://www.hkex.com.hk/Services/Trading/Derivatives/Overview/Trading-Calendar-and-Holiday-Schedule?sc_lang=en"
    
    # Send a GET request to the URL
    response = rq.get(url)
    
    # Parse the HTML content
    tree = html.fromstring(response.content)
    
    # Define the XPath to select the specific table rows
    xpath = '//*[@id="pagecontent_0_maincontent_0_common_content_section"]/div[2]/div[1]/table[26]/tbody/tr'
    
    # Select the table rows using the XPath
    rows = tree.xpath(xpath)
    # Convert the rows to XML content, removing <br> tags
    rows_xml = [etree.tostring(row).decode().replace('<br>', '') for row in rows]  
    # Concatenate rows into XML content
    xml_content = '<root>{}</root>'.format(''.join(rows_xml))
        # Save the XML content to a file
    save_xml_to_file(xml_content, pathname+'hkex_holidays.xml')    
    # Extract and print the content of each row
    for row in rows:
        # Extract text from each cell in the row
        cells = row.xpath('.//td')
        row_data = [cell.text_content().strip() for cell in cells]
        print(row_data)
        exchange_holidays.append(datetime.strptime(row_data[0].split(' ')[0],'%d/%m/%Y'))
    return exchange_holidays

def save_xml_to_file(xml_content, file_path):
    file = open(file_path, 'w', encoding='utf-8')
    file.write(xml_content)
    print("XML content saved to:", file_path)

def get_exchange_holidays_from_file(file_path):
    """
    Load exchange holidays from XML file.

    Args:
        file_path (str): Path to the XML file containing holiday data

    Returns:
        list: List of holiday dates
    """
    holidays = []  # Local list for this function

    file = open(file_path, 'r', encoding='utf-8')
    xml_content = file.read()
    file.close()

    # remove all <br> tags
    xml_content = xml_content.replace('<br>', '')
    xml_content = xml_content.replace('</span>', '')
    xml_content = xml_content.replace('<span>', '')
    # Parse the XML content
    root = etree.fromstring(xml_content)
    # Iterate through each row
    for row in root.xpath('.//tr'):
        # Extract text from each cell in the row
        cells = row.xpath('.//td')
        for cell in cells:
            print(cell.text.split(' ')[0])
            holidays.append(datetime.strptime(cell.text.split(' ')[0],'%d/%m/%Y'))
            break
    return holidays


# Option pricing functions moved to OptionPricing.py module



def create_connection():
    host = os.environ.get('dbhost')
    user = os.environ.get('user')
    password = os.environ.get('password')
    return pymysql.connect(  
        host = host,
        user = user,
        password = password,
        db = 'storacle',
        charset = 'utf8mb4',
        cursorclass = pymysql.cursors.DictCursor
        )

def create_cnx():
    host = os.environ.get('dbhost')
    user = os.environ.get('user')
    password = os.environ.get('password')
    cnx = create_engine(f'mysql+pymysql://{user}:{password}@{host}:3306/storacle', echo=False)
    return cnx

def getDay2Expiry(cmonth, d):
    """
    Calculate days to expiry for monthly options.

    Args:
        cmonth (str): Contract month (e.g., 'Jan25' or 'Jan-25')
        d (datetime.date): Current date

    Returns:
        int: Number of trading days to expiry
    """
    if len(cmonth) == 5:
        d0 = dt.datetime.strptime(cmonth, '%b%y')
    elif len(cmonth) == 6:
        d0 = dt.datetime.strptime(cmonth, '%b-%y')
    else:
        d0 = None
        print('Invalid getDay2Expiry parameter ', cmonth)
        return 0

    # Determine Expiry Date
    xdate = BMonthEnd().rollforward(d0)

    # Start counting from next day
    next_d = d + dt.timedelta(days=1)

    # Get cached exchange holidays
    holidays = get_cached_exchange_holidays()

    # All remaining trading days
    trading_days = pd.bdate_range(next_d, xdate,
                freq='C',
                weekmask=weekmask,
                holidays=holidays)

    # Monthly Contract Last trading date = 2nd last trading day
    # Count days until Contract Expiry Date. d not counted as assume closed price
    if len(trading_days) < 2:
        dx = 0
        # print(f'Last Trading Day PASSED, dx days to expiry from {d} = {dx}')
    else:
        dx = len(trading_days) - 1
        # print(f'Last Trading Day = {trading_days[-2]}, dx days to expiry from {d} = {dx}')
    return dx

def getWODay2Expiry(xdate, d):
    """
    Calculate days to expiry for weekly options.

    Args:
        xdate (str): Expiry date string (e.g., '15-Jan-25')
        d (datetime.date): Current date

    Returns:
        int: Number of trading days to expiry
    """
    # Start counting from next day
    next_d = d + dt.timedelta(days=1)
    d_end = dt.datetime.strptime(xdate, '%d-%b-%y')

    # Get cached exchange holidays
    holidays = get_cached_exchange_holidays()

    # All remaining trading days
    trading_days = pd.bdate_range(next_d, d_end,
                freq='C',
                weekmask=weekmask,
                holidays=holidays)

    # Count days until Contract Expiry Date. d not counted as assume dayend analysis
    dx = len(trading_days)
    # print(f'Last Trading Day = {xdate}, dx days to expiry from {d} = {dx}')

    return dx


# gamma and charm functions moved to OptionPricing.py module

def insert_inst(inst_name):
    connection = create_connection()
    with connection.cursor() as cursor:
            sql = "INSERT IGNORE INTO instruments(inst_name) VALUES (%s);"
            vals = (inst_name)
            try:
                cursor.execute(sql, vals)
                #print(inst_name + ' Inserted')
            except Exception as e:
                pass
                #print(inst_name + str(e), flush = True)
            # finally:
            #     connection.close()
    return


def listMonths():
    # Complie Month list
    months=[]
    for month_idx in range(1, 13):
        #print (calendar.month_name[month_idx])
        m= calendar.month_abbr[month_idx].upper()
        months.append(m)
    return months


def getPrice(isymb, idate):
    """
    Get stock price for a given symbol and date.

    This function is moved to Storacle module as it's general market data functionality,
    not specific to options processing.

    Args:
        isymb: Symbol (HSI, HHI, MHI, HTI)
        idate: Date to get price for

    Returns:
        float: Stock price for the given symbol and date
    """
    from sqlalchemy import text
    import pandas as pd
    import os
    from sqlalchemy import create_engine

    # Create database connection
    db_url = os.getenv('WILL9700_DB')
    if not db_url:
        raise ValueError("WILL9700_DB environment variable not set")

    cnx = create_engine(db_url, echo=False)

    if isymb == 'MHI':
        isymb = 'HSI'

    # Use parameterized query with text()
    query = text("""
        SELECT close FROM daily_stock_price
        WHERE hkats_code = :isymb
        AND txn_date = CAST(:idate AS DATE)
    """)

    # Execute query with parameters
    with cnx.connect() as conn:
        result = conn.execute(query, {"isymb": isymb, "idate": idate})
        df_data = result.fetchall()
        df_columns = result.keys()
        q_result = pd.DataFrame(df_data, columns=df_columns)

    if len(q_result) == 0:
        # Price not found, try to update price data
        try:
            # Call updatePrice function from same module
            updatePrice(isymb)

            # Try again after update
            with cnx.connect() as conn:
                result = conn.execute(query, {"isymb": isymb, "idate": idate})
                df_data = result.fetchall()
                df_columns = result.keys()
                q_result = pd.DataFrame(df_data, columns=df_columns)
        except Exception as e:
            print(f"Warning: updatePrice failed: {e}")

    if len(q_result) == 0:
        print('Price Still Not Found, Get nearest date')

        # Get nearest price if still missing
        nearest_query = text("""
            SELECT close FROM daily_stock_price
            WHERE hkats_code = :isymb AND txn_date = (
                SELECT max(txn_date) FROM daily_stock_price
                WHERE hkats_code = :isymb
                AND txn_date <= CAST(:idate AS DATE)
            )
        """)

        with cnx.connect() as conn:
            result = conn.execute(nearest_query, {"isymb": isymb, "idate": idate})
            df_data = result.fetchall()
            df_columns = result.keys()
            q_result = pd.DataFrame(df_data, columns=df_columns)

        return q_result.iloc[0,0] if len(q_result) > 0 else None
    else:
        return q_result.iloc[0,0]


def updatePrice(ihkats_code):
    """
    Update stock price data for a given HKATS code by fetching from Yahoo Finance.

    This function is moved to Storacle module as it's general market data functionality,
    responsible for preparing stock price data for other processing modules.

    Args:
        ihkats_code: HKATS code (HSI, HHI, HTI, MHI)

    Returns:
        None (updates database directly)
    """
    import yfinance
    import pandas as pd
    import datetime as dt
    from sqlalchemy import text, create_engine
    import os

    # Create database connection
    db_url = os.getenv('WILL9700_DB')
    if not db_url:
        raise ValueError("WILL9700_DB environment variable not set")

    cnx = create_engine(db_url, echo=False)

    # Map HKATS codes to Yahoo Finance symbols
    if ihkats_code == 'HSI':
        name = '^HSI'
    elif ihkats_code == 'HHI':
        name = '^HSCE'
    elif ihkats_code == 'HTI':
        name = 'HSTECH.HK'
    elif ihkats_code == 'MHI':
        # MHI uses HSI data
        name = '^HSI'
        ihkats_code = 'HSI'  # Store as HSI in database
    else:
        print('Unknown HKATS Code: ', ihkats_code)
        return

    # Get last date from database
    with cnx.connect() as conn:
        result = conn.execute(text(f"SELECT max(txn_date) FROM daily_stock_price where hkats_code='{ihkats_code}'"))
        df_data = result.fetchall()
        df_columns = result.keys()
        last_date = pd.DataFrame(df_data, columns=df_columns)

    # Determine start date for fetching
    if pd.isna(last_date.iloc[0, 0]):  # No existing data
        d1 = dt.date.today() - dt.timedelta(days=5)
    else:
        d1 = last_date.iloc[0, 0] + dt.timedelta(days=1)  # Start from day after last date

    d2 = dt.date.today() + dt.timedelta(days=1)  # End date (tomorrow)

    print(f'UpdatePrice: {ihkats_code}, {d1}, {d2}')

    # Fetch price history from Yahoo Finance
    ticker = yfinance.Ticker(name)
    df = ticker.history(interval='1d', start=d1, end=d2, actions=False)

    if len(df) > 0:
        # Clean and prepare data
        df.dropna(inplace=True)
        df.columns = [s.lower() for s in df.columns]
        df.volume = df.volume.apply(lambda x: float(x))
        df['txn_date'] = df.index
        df['ticker'] = name
        df['hkats_code'] = ihkats_code

        # Remove duplicates (fixes weekend extra rows issue)
        df = df.drop_duplicates(subset=['txn_date'])

        # Insert into database
        try:
            df.to_sql(name='daily_stock_price', con=cnx, if_exists='append', index=False)
            print(f'Successfully updated {len(df)} price records for {ihkats_code}')
        except Exception as e:
            print(f'{ihkats_code}: {str(e)}', flush=True)
            print(df)
    else:
        print(f'No Price Data for {ihkats_code} from {d1} to {d2}')
'''
def d1(istrike, ip, iiv, itx):
    return ( (np.log(ip/istrike)) + (itx*(0+(np.square(iiv)/2)))) / ( iiv*np.sqrt(itx) ) 
def gamma(id1, ip, iiv, itx):
    return (np.exp(-1*np.power(id1,2)/2)/np.sqrt(2*np.pi))/(ip*iiv*np.sqrt(itx))
'''

# CalcIV function moved to OptionPricing.py module

# %%
# Global configuration
weekmask = 'Mon Tue Wed Thu Fri'

# Module-level cache for exchange holidays (lazy loading)
_exchange_holidays_cache = None

def get_cached_exchange_holidays():
    """
    Get exchange holidays with caching to avoid repeated initialization.

    Returns:
        list: List of exchange holiday dates
    """
    global _exchange_holidays_cache

    if _exchange_holidays_cache is None:
        try:
            _exchange_holidays_cache = get_exchange_holidays()
            print("✓ Exchange holidays loaded from API")
        except Exception as e:
            print(f"Warning: Failed to load holidays from API: {e}")
            try:
                _exchange_holidays_cache = get_exchange_holidays_from_file(pathname + 'hkex_holidays.xml')
                print("✓ Exchange holidays loaded from file")
            except Exception as e2:
                print(f"Warning: Failed to load holidays from file: {e2}")
                _exchange_holidays_cache = []  # Fallback to empty list
                print("⚠️ Using empty holiday list as fallback")

    return _exchange_holidays_cache

def clear_exchange_holidays_cache():
    """
    Clear the exchange holidays cache to force reload.
    Useful for testing or when holidays need to be refreshed.
    """
    global _exchange_holidays_cache
    _exchange_holidays_cache = None
    print("🔄 Exchange holidays cache cleared")

if __name__ == '__main__':
    # Test code moved to OptionPricing.py module
    print("Storacle module - Market data and calendar functions")
    print("Option pricing functions available in OptionPricing.py module")


# %%
