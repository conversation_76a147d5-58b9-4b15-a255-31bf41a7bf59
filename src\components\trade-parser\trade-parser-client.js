import React, { useState } from 'react';
import TradeInputForm from './trade-input-form';
import TradePreviewTable from './trade-preview-table';
import useDatabaseStore from '../../store/useDatabaseStore';

/**
 * Trade Parser Client Component
 *
 * This component manages the state and logic for the trade parser feature.
 */
const TradeParserClient = () => {
  const [rawText, setRawText] = useState('');
  const [parsedTrades, setParsedTrades] = useState([]);
  const [isParsing, setIsParsing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [notification, setNotification] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  // Handle deleting all trades
  const handleDeleteAllTrades = async () => {
    if (isDeleting) return;
    setIsDeleting(true);
    try {
      const response = await fetch('/api/deleteAllTrades', { method: 'DELETE' });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.message || 'Failed to delete trades');
      }
      showNotification('Delete Successful', 'All trades have been deleted.', 'success');
      setParsedTrades([]);
      setRawText('');
    } catch (error) {
      showNotification('Delete Failed', error.message || 'Failed to delete trades.', 'error');
    } finally {
      setIsDeleting(false);
    }
  };

  // Get database status from context
  const { isOffline, setDatabaseOffline } = useDatabaseStore();

  // Show a notification message
  const showNotification = (title, message, type = 'success') => {
    setNotification({ title, message, type });

    // Auto-hide the notification after 5 seconds
    setTimeout(() => {
      setNotification(null);
    }, 5000);
  };

  // Handle parsing trades
  const handleParseTrades = async () => {
    if (!rawText.trim()) {
      showNotification('Input Required', 'Please paste some trade data before parsing.', 'error');
      return;
    }

    setIsParsing(true);
    setParsedTrades([]); // Clear previous results

    try {
      console.log('Sending raw text to server for parsing...');

      // Call the API to parse trades
      const response = await fetch('/api/parse-trades', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ rawText })
      });

      // Check if the response is JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error('Received non-JSON response:', await response.text());
        throw new Error('Server returned a non-JSON response. Please check the server logs.');
      }

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || result.details || 'Failed to parse trades');
      }

      if (result.trades && result.trades.length > 0) {
        console.log(`Successfully parsed ${result.trades.length} trades`);
        setParsedTrades(result.trades);
        showNotification(
          'Parsing Successful',
          `Found ${result.trades.length} trades. Please review them below.`
        );
      } else {
        console.log('No trades found in the parsed result');
        setParsedTrades([]);
        showNotification(
          'No Trades Found',
          'The parser ran successfully but did not find any trades in the provided text. Please check the format.',
          'warning'
        );
      }
    } catch (error) {
      console.error('Error parsing trades:', error);
      showNotification(
        'Parsing Failed',
        error.message || 'An unexpected error occurred while parsing trades.',
        'error'
      );
      setParsedTrades([]);
    } finally {
      setIsParsing(false);
    }
  };

  // Handle uploading trades
  const handleUploadTrades = async () => {
    if (parsedTrades.length === 0) {
      showNotification('No Trades to Upload', 'Please parse some trades first.', 'error');
      return;
    }

    if (isOffline) {
      showNotification('Database Offline', 'Cannot upload trades while database is offline.', 'error');
      return;
    }

    setIsUploading(true);

    try {
      // Call the API to upload trades
      const response = await fetch('/api/upload-trades', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ trades: parsedTrades })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to upload trades');
      }      showNotification(
        'Upload Successful',
        `Successfully uploaded ${result.count} trades to the database.`
      );

      // Clear the form after successful upload to prevent duplicated uploads
      setRawText('');
      setParsedTrades([]);
    } catch (error) {
      console.error('Error uploading trades:', error);

      // Check if this is a database connection error
      if (error.message && (
        error.message.includes('database') ||
        error.message.includes('connection') ||
        error.message.includes('offline')
      )) {
        // Update the global database status using Zustand
        setDatabaseOffline(error);
        showNotification(
          'Database Connection Error',
          'Failed to connect to the database. The application is now in offline mode.',
          'error'
        );
      } else {
        showNotification(
          'Upload Failed',
          error.message || 'An unexpected error occurred while uploading trades.',
          'error'
        );
      }
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div>
      {/* Notification */}
      {notification && (
        <div className={`mb-4 p-4 rounded-md ${
          notification.type === 'error' ? 'bg-red-100 text-red-800' :
          notification.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
          'bg-green-100 text-green-800'
        }`}>
          <div className="flex">
            <div className="flex-shrink-0">
              {notification.type === 'error' ? (
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              ) : notification.type === 'warning' ? (
                <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium">{notification.title}</h3>
              <div className="mt-2 text-sm">
                <p>{notification.message}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Database Offline Warning */}
      {isOffline && (
        <div className="mb-4 p-4 bg-yellow-100 text-yellow-800 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium">Database Offline</h3>
              <div className="mt-2 text-sm">
                <p>The database is currently offline. You can still parse trades, but you won't be able to upload them until the database connection is restored.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <TradeInputForm
        rawText={rawText}
        onRawTextChange={setRawText}
        onParse={handleParseTrades}
        isParsing={isParsing}
        onDeleteAllTrades={handleDeleteAllTrades}
        isDeleting={isDeleting}
      />

      <TradePreviewTable
        trades={parsedTrades}
        onUpload={handleUploadTrades}
        isUploading={isUploading}
      />
    </div>
  );
};

export default TradeParserClient;
