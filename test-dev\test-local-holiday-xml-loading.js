/**
 * Test script to verify local XML holiday loading functionality
 * This tests the loadExchangeHolidays() function specifically for XML file reading
 */

// Import the position-utils module
const { loadExchangeHolidays, calculateDaysToExpiry } = require('../src/utils/position-utils.js');

console.log('🧪 Testing Local Holiday XML Loading...\n');

console.log('=== 1. Testing loadExchangeHolidays() Function ===');

try {
  const startTime = Date.now();
  const holidays = loadExchangeHolidays();
  const loadTime = Date.now() - startTime;
  
  console.log(`📅 Loaded ${holidays.length} holidays in ${loadTime}ms`);
  
  if (holidays.length > 0) {
    console.log('✅ Holiday loading successful');
    
    // Display first few holidays for verification
    console.log('\n📋 First 5 holidays:');
    holidays.slice(0, 5).forEach((holiday, index) => {
      const dateStr = holiday.toISOString().split('T')[0];
      const dayName = holiday.toLocaleDateString('en-US', { weekday: 'long' });
      console.log(`  ${index + 1}. ${dateStr} (${dayName})`);
    });
    
    // Check for critical 2025-10-07 holiday
    const oct7_2025 = holidays.find(h => 
      h.getFullYear() === 2025 && h.getMonth() === 9 && h.getDate() === 7
    );
    
    if (oct7_2025) {
      console.log('\n✅ Critical holiday 2025-10-07 found in holiday list');
      console.log(`   Date: ${oct7_2025.toISOString().split('T')[0]}`);
      console.log(`   Day: ${oct7_2025.toLocaleDateString('en-US', { weekday: 'long' })}`);
    } else {
      console.log('\n❌ Critical holiday 2025-10-07 NOT found in holiday list');
    }
    
  } else {
    console.log('❌ No holidays loaded - this may indicate an issue');
  }
  
} catch (error) {
  console.error('❌ Error testing loadExchangeHolidays():', error.message);
  console.error('Stack trace:', error.stack);
}

console.log('\n=== 2. Testing Holiday File Path Configuration ===');

try {
  const config = require('../src/utils/config.js');
  const fs = require('fs');
  
  const holidayPath = config.getHolidayDataPath();
  console.log(`📁 Holiday XML file path: ${holidayPath}`);
  
  if (fs.existsSync(holidayPath)) {
    const stats = fs.statSync(holidayPath);
    console.log(`✅ XML file exists (${Math.round(stats.size / 1024)}KB)`);
    console.log(`📅 Last modified: ${stats.mtime.toISOString().split('T')[0]}`);
    
    // Read first few lines to verify content
    const xmlContent = fs.readFileSync(holidayPath, 'utf8');
    const firstLines = xmlContent.split('\n').slice(0, 5).join('\n');
    console.log('\n📄 XML file preview (first 5 lines):');
    console.log(firstLines);
    
    // Count date patterns in XML
    const datePattern = /(\d{1,2}\/\d{1,2}\/\d{4})/g;
    const dates = xmlContent.match(datePattern) || [];
    console.log(`\n🔍 Found ${dates.length} date patterns in XML file`);
    
    if (dates.length > 0) {
      console.log('📋 First 5 dates found:');
      dates.slice(0, 5).forEach((date, index) => {
        console.log(`  ${index + 1}. ${date}`);
      });
    }
    
  } else {
    console.log('❌ XML file does not exist at configured path');
  }
  
} catch (error) {
  console.error('❌ Error testing file configuration:', error.message);
}

console.log('\n=== 3. Testing Trading Days Calculation with Local Holidays ===');

try {
  // Test trading days around the critical 2025-10-07 date
  const testDate1 = '2025-10-06'; // Day before holiday
  const testDate2 = '2025-10-07'; // Holiday itself
  const testDate3 = '2025-10-08'; // Day after holiday
  
  console.log('📊 Testing trading days calculations:');
  
  // Calculate days to expiry for each test date
  const days1 = calculateDaysToExpiry(testDate1, new Date('2025-10-05'));
  const days2 = calculateDaysToExpiry(testDate2, new Date('2025-10-05'));
  const days3 = calculateDaysToExpiry(testDate3, new Date('2025-10-05'));
  
  console.log(`  Days to ${testDate1}: ${days1}`);
  console.log(`  Days to ${testDate2}: ${days2} (should be same as ${testDate1} if holiday is recognized)`);
  console.log(`  Days to ${testDate3}: ${days3}`);
  
  // Verify holiday detection
  if (days1 === days2) {
    console.log('✅ 2025-10-07 correctly treated as holiday (same trading days as previous day)');
  } else {
    console.log('❌ 2025-10-07 may not be properly recognized as holiday');
  }
  
} catch (error) {
  console.error('❌ Error testing trading days calculation:', error.message);
}

console.log('\n=== 4. Performance Test ===');

try {
  const iterations = 10;
  const times = [];
  
  console.log(`🚀 Running ${iterations} iterations of loadExchangeHolidays()...`);
  
  for (let i = 0; i < iterations; i++) {
    const start = Date.now();
    const holidays = loadExchangeHolidays();
    const time = Date.now() - start;
    times.push(time);
  }
  
  const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);
  
  console.log(`📈 Performance Results:`);
  console.log(`   Average: ${avgTime.toFixed(2)}ms`);
  console.log(`   Min: ${minTime}ms`);
  console.log(`   Max: ${maxTime}ms`);
  
  if (avgTime < 50) {
    console.log('✅ Performance: Excellent (< 50ms)');
  } else if (avgTime < 200) {
    console.log('⚠️ Performance: Good (50-200ms)');
  } else {
    console.log('❌ Performance: Needs improvement (> 200ms)');
  }
  
} catch (error) {
  console.error('❌ Error during performance test:', error.message);
}

console.log('\n🎯 Local Holiday XML Loading Test Complete!');