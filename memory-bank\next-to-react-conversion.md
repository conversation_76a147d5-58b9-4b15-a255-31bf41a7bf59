# Next.js to React Conversion Plan

## Files to Remove
- next.config.js/ts
- .next/ directory
- next-env.d.ts

## Files to Convert
- src/app/layout.tsx → src/components/Layout.jsx
- src/pages/* → Move to appropriate locations in src/components/
- Any files using Next.js specific imports like `next/image`, `next/link`, etc.

## Configuration Updates
- Update tsconfig.json to remove Next.js specific settings
- Update package.json to remove Next.js dependencies
- Add React Router if navigation is needed

## Build Process Updates
- Replace Next.js build commands with Create React App commands
- Update any deployment configurations

## Component Conversions
- Replace Next.js Image component with standard img tags or a React image component
- Replace Next.js Link component with React Router Link
- Convert any getServerSideProps or getStaticProps to useEffect hooks with data fetching

## Routing
- Implement React Router for client-side routing
- Create appropriate Route components for each page
- Set up a BrowserRouter in the main App component

## API Handling
- Move any API routes from pages/api/* to a separate backend or use serverless functions
- Update frontend code to fetch from these new endpoints