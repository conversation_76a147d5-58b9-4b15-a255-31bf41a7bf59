/**
 * Enhanced test script for extractTradeConfirmation() function
 * Includes detailed debugging to understand PDF structure
 */

const fs = require('fs');
const path = require('path');
const { parseStatement } = require('../src/server-utils/statement-parser');

// Import the extractSection function manually since it's not exported
const pdf = require('pdf-parse');
const PDFExtract = require('pdf.js-extract').PDFExtract;

/**
 * Helper function to extract a section between two headers (copied from statement-parser.js)
 */
function extractSection(text, startHeader, endHeader) {
  const startIndex = text.indexOf(startHeader);
  if (startIndex === -1) return null;

  let endIndex = text.length;
  if (endHeader) {
    const endHeaderIndex = text.indexOf(endHeader, startIndex);
    if (endHeaderIndex !== -1) {
      endIndex = endHeaderIndex;
    }
  }

  return text.substring(startIndex, endIndex);
}

async function testTradeConfirmationDetailed() {
  try {
    console.log('🧪 Enhanced Trade Confirmation Test');
    console.log('=' .repeat(60));

    // File path and password
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const password = '6369';

    console.log(`📁 PDF File: ${pdfPath}`);
    console.log(`🔐 Password: ${password}`);
    console.log('');

    // Check if file exists
    if (!fs.existsSync(pdfPath)) {
      throw new Error(`PDF file not found: ${pdfPath}`);
    }

    // Read and parse PDF
    const pdfBuffer = fs.readFileSync(pdfPath);
    console.log(`✅ PDF file loaded (${pdfBuffer.length} bytes)`);

    // Get the full text for debugging
    console.log('📖 Extracting full PDF text...');
    const pdfExtract = new PDFExtract();
    const tempFilePath = path.join(__dirname, '../temp_debug.pdf');
    fs.writeFileSync(tempFilePath, pdfBuffer);

    const extractOptions = { password: password.trim() };
    const data = await new Promise((resolve, reject) => {
      pdfExtract.extract(tempFilePath, extractOptions, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });

    // Convert to text
    const fullText = data.pages.map(page =>
      page.content.map(item => item.str).join(' ')
    ).join('\n');

    // Clean up temp file
    fs.unlinkSync(tempFilePath);

    console.log('✅ PDF text extracted');
    console.log('');

    // Show full text (first 2000 characters for debugging)
    console.log('📝 FULL PDF TEXT (first 2000 chars):');
    console.log('=' .repeat(60));
    console.log(fullText.substring(0, 2000));
    console.log('...[truncated]...');
    console.log('');

    // Look for trade-related keywords
    console.log('🔍 SEARCHING FOR TRADE-RELATED KEYWORDS:');
    console.log('=' .repeat(60));
    
    const keywords = [
      'Trade Confirmation',
      'trade confirmation', 
      'Trade',
      'TRADE',
      'Confirmation',
      'CONFIRMATION',
      'Buy',
      'Sell',
      'BUY',
      'SELL',
      'Position',
      'POSITION',
      'Contract',
      'CONTRACT',
      '成交確認',
      '交易',
      '買入',
      '賣出'
    ];

    keywords.forEach(keyword => {
      const index = fullText.indexOf(keyword);
      if (index !== -1) {
        console.log(`✅ Found "${keyword}" at position ${index}`);
        // Show context around the keyword
        const start = Math.max(0, index - 100);
        const end = Math.min(fullText.length, index + 200);
        const context = fullText.substring(start, end);
        console.log(`   Context: ...${context}...`);
        console.log('');
      } else {
        console.log(`❌ "${keyword}" not found`);
      }
    });

    // Try to extract the trade confirmation section
    console.log('🔍 SECTION EXTRACTION ANALYSIS:');
    console.log('=' .repeat(60));
    
    const tradeSection = extractSection(fullText, 'Trade Confirmation', 'Position Closed');
    if (tradeSection) {
      console.log('✅ Trade Confirmation section found');
      console.log(`Section length: ${tradeSection.length} characters`);
      console.log('Section content:');
      console.log(tradeSection);
    } else {
      console.log('❌ Trade Confirmation section not found');
      
      // Try alternative section names
      const altSections = [
        ['trade confirmation', 'position closed'],
        ['TRADE CONFIRMATION', 'POSITION CLOSED'],
        ['Trade', 'Position'],
        ['成交確認', '持倉'],
        ['交易確認', '平倉']
      ];
      
      console.log('🔍 Trying alternative section headers:');
      altSections.forEach(([start, end]) => {
        const section = extractSection(fullText, start, end);
        if (section) {
          console.log(`✅ Found section with "${start}" -> "${end}"`);
          console.log(`Section length: ${section.length} characters`);
          console.log('First 500 chars:', section.substring(0, 500));
        } else {
          console.log(`❌ No section found for "${start}" -> "${end}"`);
        }
      });
    }

    // Parse using the main function
    console.log('');
    console.log('🔍 MAIN PARSER RESULTS:');
    console.log('=' .repeat(60));
    const parsedData = await parseStatement(pdfBuffer, password);
    
    console.log('Trade Confirmation Data:');
    console.log(JSON.stringify(parsedData.tradeConfirmation, null, 2));
    
    console.log('');
    console.log('All parsed sections:');
    Object.keys(parsedData).forEach(key => {
      const section = parsedData[key];
      if (Array.isArray(section)) {
        console.log(`- ${key}: ${section.length} items`);
      } else if (section && typeof section === 'object') {
        console.log(`- ${key}: object with ${Object.keys(section).length} properties`);
      } else {
        console.log(`- ${key}: ${section ? 'present' : 'empty'}`);
      }
    });

    console.log('');
    console.log('✅ Enhanced test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testTradeConfirmationDetailed()
    .then(() => {
      console.log('🎉 Enhanced test completed!');
    })
    .catch((error) => {
      console.error('💥 Enhanced test failed:', error);
    });
}

module.exports = { testTradeConfirmationDetailed };
