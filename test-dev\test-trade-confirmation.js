/**
 * Test script for extractTradeConfirmation() function
 * Tests parsing of trade confirmation data from PDF statement
 */

const fs = require('fs');
const path = require('path');
const { parseStatement } = require('../src/server-utils/statement-parser');

async function testTradeConfirmation() {
  try {
    console.log('🧪 Testing extractTradeConfirmation() function');
    console.log('=' .repeat(60));

    // File path and password
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const password = '6369';

    console.log(`📁 PDF File: ${pdfPath}`);
    console.log(`🔐 Password: ${password}`);
    console.log('');

    // Check if file exists
    if (!fs.existsSync(pdfPath)) {
      throw new Error(`PDF file not found: ${pdfPath}`);
    }

    // Read PDF file
    console.log('📖 Reading PDF file...');
    const pdfBuffer = fs.readFileSync(pdfPath);
    console.log(`✅ PDF file loaded (${pdfBuffer.length} bytes)`);
    console.log('');

    // Parse the statement
    console.log('🔍 Parsing PDF statement...');
    const parsedData = await parseStatement(pdfBuffer, password);
    console.log('✅ PDF parsed successfully');
    console.log('');

    // Extract and display trade confirmation data
    console.log('📊 TRADE CONFIRMATION RESULTS');
    console.log('=' .repeat(60));
    
    const trades = parsedData.tradeConfirmation;
    
    if (!trades || trades.length === 0) {
      console.log('❌ No trade confirmation data found');
      console.log('');
      
      // Debug: Show what sections were found
      console.log('🔍 DEBUGGING - Available sections:');
      console.log('- Header:', parsedData.header ? '✅' : '❌');
      console.log('- Account Movement:', parsedData.accountMovement ? '✅' : '❌');
      console.log('- Trade Confirmation:', parsedData.tradeConfirmation ? '✅' : '❌');
      console.log('- Position Closed:', parsedData.positionClosed ? '✅' : '❌');
      console.log('- Open Position:', parsedData.openPosition ? '✅' : '❌');
      console.log('- Financial Summary:', parsedData.financialSummary ? '✅' : '❌');
      console.log('- Margin Summary:', parsedData.marginSummary ? '✅' : '❌');
      
    } else {
      console.log(`✅ Found ${trades.length} trade(s)`);
      console.log('');
      
      // Display each trade
      trades.forEach((trade, index) => {
        console.log(`📈 Trade #${index + 1}:`);
        console.log(`   Date: ${trade.date || 'N/A'}`);
        console.log(`   Instrument: ${trade.instrument || 'N/A'}`);
        console.log(`   Buy/Sell: ${trade.buySell || 'N/A'}`);
        console.log(`   Quantity: ${trade.quantity || 'N/A'}`);
        console.log(`   Price: ${trade.price || 'N/A'}`);
        console.log(`   Amount: ${trade.amount || 'N/A'}`);
        console.log(`   Commission: ${trade.commission || 'N/A'}`);
        console.log(`   Currency: ${trade.currency || 'N/A'}`);
        console.log('');
      });
    }

    // Show JSON output for debugging
    console.log('🔧 RAW JSON OUTPUT');
    console.log('=' .repeat(60));
    console.log('Trade Confirmation Data:');
    console.log(JSON.stringify(trades, null, 2));
    console.log('');

    // Show header info for context
    console.log('📋 STATEMENT HEADER INFO');
    console.log('=' .repeat(60));
    console.log('Header Data:');
    console.log(JSON.stringify(parsedData.header, null, 2));
    console.log('');

    console.log('✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testTradeConfirmation()
    .then(() => {
      console.log('🎉 All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testTradeConfirmation };
