# PostgreSQL Database Integration

## Overview
The Options Strategy Analyzer application uses PostgreSQL for persistent storage of options positions. This allows users to save their positions and load them later.

## Database Configuration
- Connection URL is stored in `.env` file under the `DATABASE_URL` key
- The application uses the `pg` Node.js client to connect to PostgreSQL
- Environment variables are loaded using the `dotenv` package

## Database Schema
The main table is `INDEX_OPTIONS_TRADES` with the following structure:

```sql
CREATE TABLE IF NOT EXISTS INDEX_OPTIONS_TRADES (
  id SERIAL PRIMARY KEY,
  quantity INTEGER CHECK (quantity >= -999 AND quantity <= 999),
  call_put CHAR(5) CHECK (call_put IN ('Call', 'Put')),
  strike NUMERIC(10, 2) CHECK (strike >= 0 AND strike <= 99999),
  expirydate DATE NOT NULL,
  premium NUMERIC(10, 2),
  debit_credit NUMERIC(10, 2) NOT NULL,
  created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Column Mapping
The database column names differ from the frontend property names:
- `type` → `call_put` (CHAR(5))
- `ExpiryDate` → `expirydate` (DATE)
- `debitCredit` → `debit_credit` (NUMERIC(10,2))
- `created_at` → `created_date` (TIMESTAMP)

## Data Type Handling
- `strike`, `premium`, and `debit_credit` are stored as NUMERIC(10,2) instead of INTEGER to support decimal values
- Dates are stored without timezone considerations to avoid date shifts
- When retrieving dates from the database, they are formatted as YYYY-MM-DD strings for the frontend

## API Endpoints
- `GET /api/positions` - Retrieves all saved positions
- `POST /api/savePosition` - Saves a new position to the database

## Implementation Details
- The server automatically creates the table if it doesn't exist
- Detailed error logging is implemented for database operations
- Loading state prevents user interaction during initial data loading
- Input validation occurs on both client and server sides

## Reference Implementation
See `save-trade.js` for implementation details on how to save positions to the database.
