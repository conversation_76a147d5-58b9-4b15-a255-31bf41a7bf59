import React, { useState, useEffect } from 'react';
import useDatabaseStore from '../../store/useDatabaseStore';
import DatabaseOfflineIndicator from '../database-offline-indicator';

/**
 * Trade List With Add Component
 * 
 * This component displays trades from the database and allows for adding missing trades.
 */
const TradeListWithAdd = () => {
  // Access database status context
  const { isOffline } = useDatabaseStore();

  // State for trades
  const [trades, setTrades] = useState([]);
  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  // State for error message
  const [error, setError] = useState(null);
  // State for stocks
  const [stocks, setStocks] = useState([]);
  // State for selected stock
  const [selectedStock, setSelectedStock] = useState('');
  // State for expiry dates
  const [expiryDates, setExpiryDates] = useState([]);
  // State for selected expiry date
  const [selectedExpiryDate, setSelectedExpiryDate] = useState('');
  // State for new trade form
  const [showAddForm, setShowAddForm] = useState(false);
  const [newTrade, setNewTrade] = useState({
    ticker: '',
    type: 'Call',
    strike: '',
    quantity: 1,
    premium: 0
  });

  // Fetch all stocks
  useEffect(() => {
    const fetchStocks = async () => {
      try {
        setIsLoading(true);
        
        // Get all stocks from the API
        const response = await fetch('/api/firebase-stocks');
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        setStocks(data.stocks || []);
        setIsLoading(false);
        
        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOnline();
      } catch (error) {
        console.error('Error fetching stocks:', error);
        setError('Failed to fetch stocks');
        setIsLoading(false);
        
        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOffline(error);
      }
    };

    fetchStocks();
  }, []);

  // Fetch expiry dates when stock changes
  useEffect(() => {
    const fetchExpiryDates = async () => {
      if (!selectedStock) {
        setExpiryDates([]);
        setSelectedExpiryDate('');
        setTrades([]);
        return;
      }

      try {
        setIsLoading(true);
        
        // Get expiry dates for the selected stock
        const response = await fetch(`/api/firebase-expiry-dates?stock=${selectedStock}`);
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Sort expiry dates
        const sortedDates = [...data.expiryDates].sort((a, b) => {
          const dateA = new Date(a.includes('-') && a.length === 7 ? `${a}-01` : a);
          const dateB = new Date(b.includes('-') && b.length === 7 ? `${b}-01` : b);
          
          if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
            return dateA.getTime() - dateB.getTime();
          }
          return a.localeCompare(b);
        });
        
        setExpiryDates(sortedDates);
        setSelectedExpiryDate('');
        setTrades([]);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching expiry dates:', error);
        setError('Failed to fetch expiry dates');
        setIsLoading(false);
      }
    };

    fetchExpiryDates();
  }, [selectedStock]);

  // Fetch trades when expiry date changes
  useEffect(() => {
    const fetchTrades = async () => {
      if (!selectedStock || !selectedExpiryDate) {
        setTrades([]);
        return;
      }

      try {
        setIsLoading(true);
        
        // Get trades for the selected stock and expiry date
        const response = await fetch(`/api/firebase-trades-by-expiry?expiryDate=${selectedExpiryDate}&stock=${selectedStock}`);
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Check if we need to add the HTIM5 future trade for HTI with expiry 2025-06
        let allTrades = [...data.trades];
        
        if (selectedStock === 'HTI' && selectedExpiryDate === '2025-06') {
          const hasFutureTrade = data.trades.some(trade => 
            trade.ticker === 'HTIM5' || 
            (trade.type && trade.type.toLowerCase() === 'future')
          );
          
          if (!hasFutureTrade) {
            console.log('Adding missing HTIM5 future trade for display purposes');
            allTrades.push({
              id: 'htim5-future-display',
              ticker: 'HTIM5',
              type: 'Future',
              ExpiryDate: '2025-06',
              strike: 'N/A',
              quantity: 2,
              premium: 5600.00,
              created_at: '2025-05-13T00:00:00.000Z',
              isDisplayOnly: true
            });
          }
        }
        
        setTrades(allTrades);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching trades:', error);
        setError('Failed to fetch trades');
        setIsLoading(false);
      }
    };

    fetchTrades();
  }, [selectedExpiryDate, selectedStock]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewTrade(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Prepare the trade data
    const tradeData = {
      ...newTrade,
      ExpiryDate: selectedExpiryDate,
      // Convert numeric fields
      quantity: parseInt(newTrade.quantity),
      premium: parseFloat(newTrade.premium),
      strike: newTrade.type === 'Future' ? 'N/A' : parseFloat(newTrade.strike)
    };
    
    try {
      // Call the API to save the trade
      const response = await fetch('/api/firebase-add-trade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(tradeData)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      // Reset the form
      setNewTrade({
        ticker: '',
        type: 'Call',
        strike: '',
        quantity: 1,
        premium: 0
      });
      
      // Hide the form
      setShowAddForm(false);
      
      // Refresh the trades
      const tradesResponse = await fetch(`/api/firebase-trades-by-expiry?expiryDate=${selectedExpiryDate}&stock=${selectedStock}`);
      
      if (!tradesResponse.ok) {
        throw new Error(`HTTP error! Status: ${tradesResponse.status}`);
      }
      
      const tradesData = await tradesResponse.json();
      setTrades(tradesData.trades);
    } catch (error) {
      console.error('Error adding trade:', error);
      alert(`Error adding trade: ${error.message}`);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const parts = dateString.split('-');
    if (parts.length >= 3) {
      const month = parseInt(parts[1]);
      const day = parseInt(parts[2]);
      return `${month}/${day}`;
    }
    return dateString;
  };

  return (
    <div className="space-y-8">
      <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold text-gray-800">Trade List</h2>
          <DatabaseOfflineIndicator />
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-blue-100 p-2 rounded">
            <div className="font-bold text-xs mb-1">Stock</div>
            <select
              className="w-full p-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              value={selectedStock}
              onChange={(e) => setSelectedStock(e.target.value)}
              disabled={isLoading || isOffline}
            >
              <option value="">Select a stock</option>
              {stocks.map(stock => (
                <option key={stock} value={stock}>{stock}</option>
              ))}
            </select>
          </div>

          <div className="bg-blue-100 p-2 rounded">
            <div className="font-bold text-xs mb-1">Expiry Date</div>
            <select
              className="w-full p-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              value={selectedExpiryDate}
              onChange={(e) => setSelectedExpiryDate(e.target.value)}
              disabled={!selectedStock || isLoading || isOffline}
            >
              <option value="">Select an expiry date</option>
              {expiryDates.map(date => (
                <option key={date} value={date}>{formatDate(date)}</option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setShowAddForm(!showAddForm)}
              className="w-full p-1 text-xs bg-green-500 hover:bg-green-600 text-white rounded"
              disabled={!selectedStock || !selectedExpiryDate || isLoading || isOffline}
            >
              {showAddForm ? 'Cancel' : 'Add Trade'}
            </button>
          </div>
        </div>

        {/* Add Trade Form */}
        {showAddForm && (
          <div className="bg-gray-100 p-4 rounded mb-4">
            <h3 className="text-md font-semibold mb-2">Add New Trade</h3>
            <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Ticker</label>
                <input
                  type="text"
                  name="ticker"
                  value={newTrade.ticker}
                  onChange={handleInputChange}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                  required
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Type</label>
                <select
                  name="type"
                  value={newTrade.type}
                  onChange={handleInputChange}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                  required
                >
                  <option value="Call">Call</option>
                  <option value="Put">Put</option>
                  <option value="Future">Future</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Strike</label>
                <input
                  type="text"
                  name="strike"
                  value={newTrade.strike}
                  onChange={handleInputChange}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                  disabled={newTrade.type === 'Future'}
                  required={newTrade.type !== 'Future'}
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Quantity</label>
                <input
                  type="number"
                  name="quantity"
                  value={newTrade.quantity}
                  onChange={handleInputChange}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                  required
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Premium</label>
                <input
                  type="number"
                  name="premium"
                  value={newTrade.premium}
                  onChange={handleInputChange}
                  className="w-full p-1 text-xs border border-gray-300 rounded"
                  step="0.01"
                  required
                />
              </div>
              <div className="flex items-end">
                <button
                  type="submit"
                  className="w-full p-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded"
                >
                  Save Trade
                </button>
              </div>
            </form>
          </div>
        )}

        {isLoading ? (
          <div className="text-center py-4">
            <p className="text-gray-500">Loading trades...</p>
          </div>
        ) : error ? (
          <div className="text-center py-4">
            <p className="text-red-500">{error}</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <div className="h-[600px] overflow-y-auto border rounded-md">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ticker
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expiry
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Strike
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {trades.length === 0 ? (
                    <tr>
                      <td colSpan="7" className="px-3 py-4 text-center text-sm text-gray-500">
                        No trades found for the selected filters.
                      </td>
                    </tr>
                  ) : (
                    trades.map((trade) => (
                      <tr key={trade.id} className={
                        trade.isDisplayOnly ? 'bg-yellow-100' :
                        trade.type === 'Future' || trade.type === 'future' ? 'bg-yellow-50' :
                        trade.type === 'Call' || trade.type === 'call' ? 'bg-cyan-50' :
                        trade.type === 'Put' || trade.type === 'put' ? 'bg-red-50' :
                        'bg-gray-50'
                      }>
                        <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900" title={trade.ticker}>
                          {trade.ticker}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.type}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.ExpiryDate || trade.expiry}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.strike === 'N/A' || trade.type === 'Future' || trade.type === 'future' ? 'N/A' : trade.strike}
                        </td>
                        <td className={`px-3 py-2 whitespace-nowrap text-sm text-right ${trade.quantity < 0 ? 'text-red-500' : 'text-green-600'}`}>
                          {trade.quantity}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {trade.premium != null ? trade.premium.toFixed(2) : 'N/A'}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.isDisplayOnly ? (
                            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-200 text-yellow-800">
                              Display Only
                            </span>
                          ) : (
                            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-200 text-green-800">
                              Saved
                            </span>
                          )}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Showing {trades.length} trade{trades.length === 1 ? "" : "s"}.
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TradeListWithAdd;
