// Utility functions for handling positions with Firestore
import { calculateDaysToExpiry } from './position-utils';

// Variable to store the setDatabaseOffline function
let setDatabaseOfflineFunc = null;
let isOfflineState = false;

// Function to set the database offline function
export const setDatabaseOfflineFunction = (func) => {
  setDatabaseOfflineFunc = func;
};

// Function to check if database is offline
export const isDatabaseOffline = () => {
  return isOfflineState;
};

// Function to set database offline status
export const setDatabaseOffline = (error) => {
  // If already offline, don't trigger the notification again
  if (isOfflineState) {
    console.log('Database already in offline mode, not triggering notification again');
    return;
  }

  console.log('Setting database to offline mode from firebase-position-utils');
  // Set offline state first
  isOfflineState = true;

  // Then call the context function to show notification
  if (setDatabaseOfflineFunc) {
    setDatabaseOfflineFunc(error);
    console.log('Database offline function called with error:', error);
  } else {
    console.error('setDatabaseOfflineFunc is not set!');
  }
};

/**
 * Fetch all positions from Firestore
 * @returns {Promise<Array>} - Array of positions
 * @throws {Error} - If there's a database connection error
 */
export const fetchFirestorePositions = async () => {
  try {
    console.log('Fetching positions from Firestore...');
    console.log('API endpoint: /api/firebase-all-trades');

    // Use the firebase-all-trades API endpoint
    const response = await fetch('/api/firebase-all-trades');
    console.log('Fetch response received:', response);
    console.log('Response status:', response.status, response.statusText);

    if (!response.ok) {
      const errorData = await response.json();
      console.log('Error data from server:', errorData);

      // Check if the error is related to database connection
      if (errorData.message && (
          errorData.message.includes('database') ||
          errorData.message.includes('connection') ||
          errorData.message.includes('firestore') ||
          errorData.message.includes('firebase') ||
          errorData.message.includes('ECONNREFUSED') ||
          errorData.message.includes('timeout')
        )) {
        // Set database offline status
        const dbError = new Error(`Database connection error: ${errorData.message}`);
        console.log('Database connection error detected in response');
        setDatabaseOffline(dbError.message);
        throw dbError;
      }
      throw new Error(errorData.message || 'Failed to fetch positions');
    }

    // Log the raw response for debugging
    const data = await response.json();
    console.log('Raw Firestore data received:', data);

    // Map the Firestore data to the format expected by the positions table
    const mappedTrades = data.trades.map(trade => {
      // Extract stock from ticker (first 3 characters)
      const stockFromTicker = trade.ticker ? trade.ticker.substring(0, 3) : '';

      // Use stock field from trade if available, otherwise extract from ticker
      const stockFromTrade = trade.stock || stockFromTicker;

      console.log(`Processing trade ${trade.id}: ticker=${trade.ticker}, extracted stock=${stockFromTicker}, trade.stock=${trade.stock}, using stock=${stockFromTrade}`);

      const mappedTrade = {
        id: trade.id,
        quantity: parseInt(trade.quantity) || 0,
        type: trade.type || 'Call', // 'Call', 'Put', or 'Future'
        strike: trade.type?.toLowerCase() === 'future' ? 'N/A' : (parseFloat(trade.strike) || 0),
        ExpiryDate: trade.expiry || trade.ExpiryDate || '',
        premium: parseFloat(trade.price) || parseFloat(trade.premium) || 0,
        // Calculate debitCredit as quantity * price/premium
        // This will be recalculated in the component for consistency
        debitCredit: 0, // Will be calculated in the component
        created_at: trade.uploadedAt || trade.created_at || new Date().toISOString(),
        ticker: trade.ticker || '',
        originalLine: trade.originalLine || '',
        saved: true, // Mark as saved since it's from the database
        stock: stockFromTrade // Add stock field (first 3 characters of ticker or from trade.stock)
      };      // Calculate days to expiry for the position using utility function
      if (mappedTrade.ExpiryDate) {
        mappedTrade.daysToExpiry = calculateDaysToExpiry(mappedTrade.ExpiryDate);
      } else {
        mappedTrade.daysToExpiry = 0;
      }

      return mappedTrade;
    });

    console.log('Mapped trades from Firestore:', mappedTrades);
    return mappedTrades;
  } catch (error) {
    console.error('Error fetching positions from Firestore:', error);
    throw error; // Re-throw to be handled by the component
  }
};

/**
 * Save a position to Firestore
 * @param {Object} position - The position to save
 * @returns {Promise<Object>} - The result of the database operation
 */
export const saveFirestorePosition = async (position) => {
  try {
    // Check which fields are missing and log them
    console.log('Attempting to save position to Firestore:', position);

    const missingFields = [];
    if (!position.quantity && position.quantity !== 0) missingFields.push('quantity');
    if (!position.type) missingFields.push('type');
    if (position.type !== 'Future' && (!position.strike && position.strike !== 0)) missingFields.push('strike');
    if (!position.ExpiryDate) missingFields.push('ExpiryDate');
    if (!position.premium && position.premium !== 0) missingFields.push('premium');
    if (position.debitCredit === undefined) missingFields.push('debitCredit');

    if (missingFields.length > 0) {
      console.error('Missing fields in position:', missingFields);
      return {
        success: false,
        message: `Missing required fields: ${missingFields.join(', ')}`
      };
    }

    // Validate data according to schema constraints
    if (position.quantity < -999 || position.quantity > 999) {
      return {
        success: false,
        message: 'Quantity must be between -999 and 999'
      };
    }

    if (position.type !== 'Call' && position.type !== 'Put' && position.type !== 'Future') {
      return {
        success: false,
        message: 'Type must be either "Call", "Put", or "Future"'
      };
    }

    if (position.type !== 'Future' && (position.strike < 0 || position.strike > 99999)) {
      return {
        success: false,
        message: 'Strike must be between 0 and 99999'
      };
    }

    // Create a ticker based on the position type and strike
    // Use the symbol from the position - must be provided
    if (!position.symbol) {
      return {
        success: false,
        message: 'Symbol is required for creating a ticker'
      };
    }

    const stockCode = position.symbol;
    const ticker = position.type === 'Future'
      ? `${stockCode}M5` // Example future ticker
      : `${stockCode}${position.type === 'Call' ? 'C' : 'P'}${position.strike}`;

    // Extract stock from ticker (first 3 characters) if not already present
    const stock = position.stock || position.ticker?.substring(0, 3) || position.symbol?.substring(0, 3);

    // Use the firebase-add-trade API endpoint
    const response = await fetch('/api/firebase-add-trade', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ticker: position.ticker || ticker,
        type: position.type,
        expiry: position.ExpiryDate,
        strike: position.type === 'Future' ? 'N/A' : position.strike,
        quantity: position.quantity,
        price: position.premium,
        originalLine: position.originalLine || '',
        symbol: position.symbol, // Include the symbol in the request
        stock: stock // Include the stock field (first 3 characters of ticker)
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Server returned error:', errorData);

      // Check if the error is related to database connection
      if (errorData.message && (
          errorData.message.includes('database') ||
          errorData.message.includes('connection') ||
          errorData.message.includes('firestore') ||
          errorData.message.includes('firebase') ||
          errorData.message.includes('ECONNREFUSED') ||
          errorData.message.includes('timeout')
        )) {
        throw new Error(`Database connection error: Unable to connect to the database`);
      }

      throw new Error(errorData.message || 'Failed to save position');
    }

    const data = await response.json();

    return {
      success: true,
      message: 'Position saved successfully',
      id: data.tradeId
    };
  } catch (error) {
    console.error('Error saving position to Firestore:', error);

    // Check if it's a network error
    if (error instanceof TypeError || error.name === 'TypeError' ||
        error.message.includes('Failed to fetch') || error.message.includes('Network Error')) {
      return {
        success: false,
        message: 'Network error: Unable to connect to the server',
        error: error.message || 'Network error',
        isOffline: true
      };
    }

    // Check if it's a database connection error
    const errorMessage = error.message || 'Unknown error';
    const isDatabaseError = errorMessage.includes('database') ||
                           errorMessage.includes('connection') ||
                           errorMessage.includes('firestore') ||
                           errorMessage.includes('firebase') ||
                           errorMessage.includes('ECONNREFUSED') ||
                           errorMessage.includes('timeout');

    return {
      success: false,
      message: isDatabaseError ? `Database connection error: Unable to connect to the database` : 'Error saving position',
      error: errorMessage,
      isOffline: isDatabaseError || true
    };
  }
};
