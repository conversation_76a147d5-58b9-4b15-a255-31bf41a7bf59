import { useMemo } from 'react';
import TradesPnLChart from './TradesPnLChart';
import FilteredTradesTable from './FilteredTradesTable';
import usePnLAtVariousPriceStore from '../store/usePnLAtVariousPriceStore';
import useAnalysisStore from '../store/useAnalysisStore';

/**
 * FilteredTradesPnLChart Component
 *
 * This component displays a PnL chart for trades filtered by expiry date.
 * It consumes the reactive usePnLAtVariousPriceStore and uses getFilteredData()
 * to filter trades by the selectedExpiryDate passed from the parent.
 */

const FilteredTradesPnLChart = ({ selectedExpiryDate }) => {
  // Get analysis parameters for display
  const selectedStockSymbol = useAnalysisStore(state => state.symbol);
  const targetStockPrice = useAnalysisStore(state => state.targetStockPrice);
  const volatility = useAnalysisStore(state => state.volatility);
  const riskFreeRate = useAnalysisStore(state => state.riskFreeRate);
  const daysToVisualize = useAnalysisStore(state => state.daysToVisualize);
  const stockInfo = useAnalysisStore(state => state.stockInfo);

  // Get PnL store data (store is now reactive, no manual parameter setting needed)
  const { pnlData, getFilteredData, getGlobalPnLBounds } = usePnLAtVariousPriceStore();

  // Get filtered PnL data based on the selectedExpiryDate
  const filteredPnLData = useMemo(() => {
    if (!pnlData || !pnlData.pricePoints || pnlData.pricePoints.length === 0) {
      return { positionData: [], totals: {}, pricePoints: [] };
    }

    // Create filters object for the store
    const filters = {};

    // Filter by expiry date if not 'ALL'
    if (selectedExpiryDate && selectedExpiryDate !== 'ALL') {
      filters.expiry = selectedExpiryDate;
    }

    // Get filtered data from the store
    return getFilteredData(filters);
  }, [pnlData, selectedExpiryDate, getFilteredData]);

  // Calculate global P&L bounds for consistent y-axis scaling
  const yAxisBounds = useMemo(() => {
    return getGlobalPnLBounds();
  }, [getGlobalPnLBounds]);

  // Extract market price from stockInfo
  const marketPrice = stockInfo?.response?.regularMarketPrice;

  // Create a display title based on the filters
  const getChartTitle = () => {
    if (!selectedExpiryDate || selectedExpiryDate === 'ALL') {
      return `${selectedStockSymbol || 'All Stocks'} - All Expiries`;
    }
    return `${selectedStockSymbol || 'All Stocks'} - ${selectedExpiryDate}`;
  };

  // Count of filtered trades for display
  const filteredTradesCount = filteredPnLData.positionData?.length || 0;

  // Don't render if no filtered trades
  if (filteredTradesCount === 0) {
    return (
      <div className="bg-white p-4 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-2">Filtered Trades P&L Chart</h3>
        <p className="text-gray-500 text-center py-8">
          No trades match the selected expiry date ({selectedExpiryDate}). Please select a different expiry date to see the P&L chart.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <div className="mb-4">
        <h2 className="text-lg font-bold mb-1 text-gray-800">
          Filtered Trades P&L Analysis
          <span className="ml-2 text-xs text-gray-500">
            Expiry: {selectedExpiryDate || 'ALL'} |
            @${targetStockPrice ? targetStockPrice.toLocaleString() : '0'} |
            IV: {volatility || 0}% |
            RFR: {riskFreeRate || 0}% |
            Days: {daysToVisualize || 0} |
            ({filteredTradesCount} trades)
          </span>
        </h2>
      </div>

      <TradesPnLChart
        pnlData={filteredPnLData.positionData || []}
        pricePoints={filteredPnLData.pricePoints || []}
        isLoading={false}
        error={null}
        selectedStockSymbol={getChartTitle()}
        marketPrice={marketPrice}
        yAxisBounds={yAxisBounds}
        targetPrice={targetStockPrice}
      />

      {/* Integrated Table Extension */}
      <FilteredTradesTable
        filteredPnLData={filteredPnLData}
        selectedExpiryDate={selectedExpiryDate}
        selectedStockSymbol={selectedStockSymbol}
        targetPrice={targetStockPrice}
      />
    </div>
  );
};

export default FilteredTradesPnLChart;
