// Import the functions to test
const { calculatePositionPremium, calculateDaysToExpiry } = require('./position-utils');

// Test the calculatePositionPremium function
describe('calculatePositionPremium', () => {
  // Test case for a call option at expiry that is in the money
  test('should calculate correct premium for call option at expiry (in the money)', () => {
    const position = {
      type: 'Call',
      strike: 22000,
      daysToExpiry: 0, // At expiry
    };
    const stockPrice = 22500;
    const riskFreeRate = 2;
    const volatility = 30;

    const premium = calculatePositionPremium(position, stockPrice, riskFreeRate, volatility);
    
    // At expiry, call premium should be max(0, stockPrice - strike)
    expect(premium).toBe(500); // 22500 - 22000 = 500
  });

  // Test case for a call option at expiry that is out of the money
  test('should calculate correct premium for call option at expiry (out of the money)', () => {
    const position = {
      type: 'Call',
      strike: 23000,
      daysToExpiry: 0, // At expiry
    };
    const stockPrice = 22500;
    const riskFreeRate = 2;
    const volatility = 30;

    const premium = calculatePositionPremium(position, stockPrice, riskFreeRate, volatility);
    
    // At expiry, out-of-money call premium should be 0
    expect(premium).toBe(0);
  });

  // Test case for a put option at expiry that is in the money
  test('should calculate correct premium for put option at expiry (in the money)', () => {
    const position = {
      type: 'Put',
      strike: 23000,
      daysToExpiry: 0, // At expiry
    };
    const stockPrice = 22500;
    const riskFreeRate = 2;
    const volatility = 30;

    const premium = calculatePositionPremium(position, stockPrice, riskFreeRate, volatility);
    
    // At expiry, put premium should be max(0, strike - stockPrice)
    expect(premium).toBe(500); // 23000 - 22500 = 500
  });

  // Test case for a put option at expiry that is out of the money
  test('should calculate correct premium for put option at expiry (out of the money)', () => {
    const position = {
      type: 'Put',
      strike: 22000,
      daysToExpiry: 0, // At expiry
    };
    const stockPrice = 22500;
    const riskFreeRate = 2;
    const volatility = 30;

    const premium = calculatePositionPremium(position, stockPrice, riskFreeRate, volatility);
    
    // At expiry, out-of-money put premium should be 0
    expect(premium).toBe(0);
  });

  // Test case for a call option before expiry
  test('should calculate correct premium for call option before expiry', () => {
    const position = {
      type: 'Call',
      strike: 22200,
      daysToExpiry: 3, // 3 days to expiry
    };
    const stockPrice = 22600;
    const riskFreeRate = 2;
    const volatility = 30;

    const premium = calculatePositionPremium(position, stockPrice, riskFreeRate, volatility);
    
    // Should use Black-Scholes model for non-zero days to expiry
    // This is an approximate value based on the Black-Scholes model
    expect(premium).toBeGreaterThan(400); // Intrinsic value is 400, time value adds more
  });

  // Test case for a put option before expiry
  test('should calculate correct premium for put option before expiry', () => {
    const position = {
      type: 'Put',
      strike: 22200,
      daysToExpiry: 3, // 3 days to expiry
    };
    const stockPrice = 22600;
    const riskFreeRate = 2;
    const volatility = 30;

    const premium = calculatePositionPremium(position, stockPrice, riskFreeRate, volatility);
    
    // Should use Black-Scholes model for non-zero days to expiry
    // This is an approximate value based on the Black-Scholes model
    expect(premium).toBeGreaterThan(0); // Out of the money but still has time value
  });
  test('should calculate correct premium for put option before expiry', () => {
    const position = {
      type: 'Call',
      strike: 22600,
      daysToExpiry: 50, 
    };
    const stockPrice = 24800;
    const riskFreeRate = 0;
    const volatility = 20;

    const premium = calculatePositionPremium(position, stockPrice, riskFreeRate, volatility);
    
    // Should use Black-Scholes model for non-zero days to expiry
    // This is an approximate value based on the Black-Scholes model
    expect(premium).toBeGreaterThan(0); // Out of the money but still has time value
  });

});
