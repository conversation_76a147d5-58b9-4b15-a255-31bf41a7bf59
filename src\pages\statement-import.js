import React from 'react';
import StatementImportClient from '../components/statement-import/statement-import-client';

/**
 * Statement Import Page
 * 
 * This page allows users to upload PDF trading statements, parse them,
 * preview the extracted data, and save them to the database.
 */
const StatementImportPage = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Statement Import</h1>
      <p className="text-gray-600 mb-6">
        Upload your trading statement PDF file to automatically extract and import your trading data.
        Supports password-protected files and extracts account movements, trade confirmations, 
        positions, and financial summaries.
      </p>
      
      <StatementImportClient />
    </div>
  );
};

export default StatementImportPage;
