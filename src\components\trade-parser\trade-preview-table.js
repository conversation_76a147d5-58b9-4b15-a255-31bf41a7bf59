import React from 'react';

/**
 * Trade Preview Table Component
 *
 * This component displays a preview of parsed trades and allows uploading them.
 */
const TradePreviewTable = ({ trades, onUpload, isUploading }) => {
  return (
    <div className="bg-white shadow-md rounded-lg p-4">
      <div className="mb-4 flex justify-between items-start">
        <div>
          <h2 className="text-xl font-bold mb-2">Preview Parsed Trades</h2>
          <p className="text-sm text-gray-600 mb-4">
            Review the parsed trades below. If everything looks correct, you can upload them.
          </p>
        </div>
        <button
          onClick={onUpload}
          disabled={isUploading || trades.length === 0}
          className={`px-4 py-2 rounded flex items-center ${
            isUploading || trades.length === 0
              ? 'bg-blue-300 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isUploading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Uploading...
            </>
          ) : (
            <>
              <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              Upload Trades
            </>
          )}
        </button>
      </div>

      <div className="border rounded overflow-hidden mb-4">
        <div className="max-h-[400px] overflow-auto">
          <table className="min-w-full divide-y divide-gray-200">
            {trades.length === 0 ? (
              <caption className="py-8 text-center text-gray-500">
                <div className="flex flex-col items-center justify-center">
                  <svg className="h-10 w-10 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No trades parsed yet, or no data to display. Paste data and click "Parse Trades".
                </div>
              </caption>
            ) : (
              <>
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ticker
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Stock
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expiry
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Strike
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {trades.map((trade) => (
                    <tr key={trade.id} className={
                      trade.type === 'Future' ? 'bg-yellow-50' :
                      trade.type === 'Call' ? 'bg-cyan-50' :
                      trade.type === 'Put' ? 'bg-red-50' :
                      'bg-gray-50'
                    }>
                      <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900" title={trade.ticker}>
                        {trade.ticker || 'N/A'}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                        {trade.stock || 'N/A'}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                        {trade.expiryDate || trade.expiry || 'N/A'}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                        {trade.strike || 'N/A'}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                        {trade.type || 'Unknown'}
                      </td>
                      <td className={`px-3 py-2 whitespace-nowrap text-sm text-right ${
                        trade.quantity < 0 ? 'text-red-500' :
                        trade.quantity > 0 ? 'text-green-600' :
                        'text-gray-500'
                      }`}>
                        {trade.quantity || 0}
                      </td>
                      <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                        {typeof trade.cost === 'number' ? trade.cost.toFixed(2) :
                         typeof trade.price === 'number' ? trade.price.toFixed(2) : '0.00'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </>
            )}
          </table>
        </div>
      </div>
    </div>
  );
};

export default TradePreviewTable;
