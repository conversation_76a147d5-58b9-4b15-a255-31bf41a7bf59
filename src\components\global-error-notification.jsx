import React from 'react';
import useDatabaseStore from '../store/useDatabaseStore';
import ErrorNotification from './error-notification';

/**
 * Global Error Notification Component
 * 
 * This component displays error notifications from the global database context
 * 
 * @returns {JSX.Element} The rendered component
 */
const GlobalErrorNotification = () => {
  const { errorNotification, closeErrorNotification } = useDatabaseStore();

  return (
    <ErrorNotification
      show={errorNotification.show}
      title={errorNotification.title}
      message={errorNotification.message}
      details={errorNotification.details}
      type={errorNotification.type}
      onClose={closeErrorNotification}
      autoCloseTime={0} // Don't auto-close error notifications
    />
  );
};

export default GlobalErrorNotification;
