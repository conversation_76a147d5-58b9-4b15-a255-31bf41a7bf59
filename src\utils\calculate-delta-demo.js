// Standalone script to calculate premium and delta for two option positions
const { calculatePositionPremium } = require('./position-utils');

function normalCDF(x) {
  // Standard normal cumulative distribution function
  const a1 = 0.254829592;
  const a2 = -0.284496736;
  const a3 = 1.421413741;
  const a4 = -1.453152027;
  const a5 = 1.061405429;
  const p = 0.3275911;
  const sign = x < 0 ? -1 : 1;
  x = Math.abs(x);
  const t = 1.0 / (1.0 + p * x);
  const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
  return 0.5 * (1.0 + sign * y);
}

function normalCDF_erf(x) {
  // More accurate normal CDF using the error function
  return 0.5 * (1 + erf(x / Math.sqrt(2)));
}

function erf(x) {
  // Approximation of the error function
  // <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> formula 7.1.26
  const sign = x < 0 ? -1 : 1;
  x = Math.abs(x);
  const a1 = 0.254829592;
  const a2 = -0.284496736;
  const a3 = 1.421413741;
  const a4 = -1.453152027;
  const a5 = 1.061405429;
  const p = 0.3275911;
  const t = 1.0 / (1.0 + p * x);
  const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
  return sign * y;
}

function calculateD1(S, K, r, v, T) {
  return (Math.log(S / K) + (r + 0.5 * v * v) * T) / (v * Math.sqrt(T));
}

function calculateDelta(type, S, K, r, v, T) {
  const d1 = calculateD1(S, K, r, v, T);
  if (type === 'Call') {
    return normalCDF(d1);
  } else {
    return normalCDF(d1) - 1;
  }
}

const stockPrice = 24800;
const riskFreeRate = 0;
const volatility = 20;
const daysToExpiry = 50;
const T = daysToExpiry / 252;
const v = volatility / 100;
const r = riskFreeRate / 100;

const positions = [
  { type: 'Call', strike: 22600, daysToExpiry },
  { type: 'Call', strike: 25200, daysToExpiry }
];

// Method 4: Rational approximation (Hart et al.)
function normalCDF_hart(x) {
  const sign = x < 0 ? -1 : 1;
  x = Math.abs(x);
  if (x > 37) {
    return sign > 0 ? 1 : 0;
  }
  const exp_val = Math.exp(-0.5 * x * x);
  let cdf;
  if (x < 7.07106781186547) {
    const num = ((((((0.0352624965998911 * x + 0.700383064443688) * x +
                    6.37396220353165) * x + 33.912866078383) * x +
                   112.079291497871) * x + 221.213596169931) * x +
                  220.206867912376);
    const den = (((((((0.0883883476483184 * x + 1.75566716318264) * x +
                     16.064177579207) * x + 86.7807322029461) * x +
                    296.564248779674) * x + 637.333633378831) * x +
                   793.826512519948) * x + 440.413735824752);
    cdf = 1 - exp_val * num / den;
  } else {
    const num = (((((0.00565362490251678 * x + 0.256345893712771) * x +
                   4.42670760699551) * x + 34.2421788644669) * x +
                  122.706522983818) * x + 183.471030643998);
    const den = ((((((0.0151339245098523 * x + 0.683471827167415) * x +
                    12.4194596690635) * x + 107.780399473888) * x +
                   495.225979108646) * x + 1214.46988320507) * x +
                  1324.14529748068);
    cdf = 1 - exp_val * num / den;
  }
  return sign > 0 ? cdf : 1 - cdf;
}

// Method 5: Series expansion (most accurate for small x)
function normalCDF_series(x) {
  if (x < -6) return 0;
  if (x > 6) return 1;
  const sign = x >= 0 ? 1 : -1;
  x = Math.abs(x);
  if (x < 0.5) {
    const x_squared = x * x;
    let sum = x;
    let term = x;
    for (let i = 1; i <= 20; i++) {
      term = term * x_squared / (2 * i + 1);
      sum += term;
      if (Math.abs(term) < 1e-15) break;
    }
    return sign * sum * Math.exp(-x_squared / 2) / Math.sqrt(2 * Math.PI);
  } else {
    const z = Math.sqrt(-2 * Math.log(1e-15 / x));
    return sign * (1 - normalCDF(z));
  }
}

// Calculate and display the premium and delta for each position using the selected method
positions.forEach(position => {
  const { type, strike, daysToExpiry } = position;
  const S = stockPrice;
  const K = strike;
  const T = daysToExpiry / 252;
  const v = volatility / 100;
  const r = riskFreeRate / 100;

  // Calculate d1 and d2 using the Black-Scholes formula
  const d1 = calculateD1(S, K, r, v, T);
  const d2 = d1 - v * Math.sqrt(T);

  // Calculate N(d1) and N(d2) using all normal CDF methods
  const Nd1_approx = normalCDF(d1);
  const Nd2_approx = normalCDF(d2);
  const Nd1_erf = normalCDF_erf(d1);
  const Nd2_erf = normalCDF_erf(d2);
  const Nd1_hart = normalCDF_hart(d1);
  const Nd2_hart = normalCDF_hart(d2);
  const Nd1_series = normalCDF_series(d1);
  const Nd2_series = normalCDF_series(d2);

  // Calculate the premium and delta based on the option type
  let premium_approx, premium_erf, premium_hart, premium_series;
  if (type === 'Call') {
    premium_approx = S * Nd1_approx - K * Math.exp(-r * T) * Nd2_approx;
    premium_erf = S * Nd1_erf - K * Math.exp(-r * T) * Nd2_erf;
    premium_hart = S * Nd1_hart - K * Math.exp(-r * T) * Nd2_hart;
    premium_series = S * Nd1_series - K * Math.exp(-r * T) * Nd2_series;
  } else {
    premium_approx = K * Math.exp(-r * T) * Nd2_approx - S * Nd1_approx;
    premium_erf = K * Math.exp(-r * T) * Nd2_erf - S * Nd1_erf;
    premium_hart = K * Math.exp(-r * T) * Nd2_hart - S * Nd1_hart;
    premium_series = K * Math.exp(-r * T) * Nd2_series - S * Nd1_series;
  }
  const delta = calculateDelta(type, S, K, r, v, T);

  // Display the results
  console.log(`Position: ${type} ${strike}`);
  console.log(`  d1: ${d1.toFixed(4)}`);
  console.log(`  Delta: ${delta.toFixed(4)}`);
  console.log(`  Premium (approx):  ${premium_approx.toFixed(2)}`);
  console.log(`  Premium (erf):     ${premium_erf.toFixed(2)}`);
  console.log(`  Premium (hart):    ${premium_hart.toFixed(2)}`);
  console.log(`  Premium (series):  ${premium_series.toFixed(2)}`);
  console.log(`  N(d1) (approx):    ${Nd1_approx.toFixed(4)}`);
  console.log(`  N(d1) (erf):       ${Nd1_erf.toFixed(4)}`);
  console.log(`  N(d1) (hart):      ${Nd1_hart.toFixed(4)}`);
  console.log(`  N(d1) (series):    ${Nd1_series.toFixed(4)}`);
});
