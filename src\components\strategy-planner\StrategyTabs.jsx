import React from 'react';
import TradesTab from './TradesTab';
import AnalysisTab from './AnalysisTab';

/**
 * Strategy Tabs Component
 * 
 * Provides tab navigation within strategy windows for switching between
 * Trades management and P&L Analysis views.
 */
const StrategyTabs = ({ strategyStore, activeTab, onTabChange }) => {
  const {
    trades,
    getTradesSummary,
    isCalculating
  } = strategyStore();

  const tradesSummary = getTradesSummary();

  const tabs = [
    {
      id: 'trades',
      name: 'Trades',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      badge: tradesSummary.totalTrades > 0 ? tradesSummary.totalTrades : null
    },
    {
      id: 'analysis',
      name: 'P&L Analysis',
      icon: (
        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      badge: isCalculating ? '...' : null,
      disabled: tradesSummary.totalTrades === 0
    }
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-4" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => !tab.disabled && onTabChange(tab.id)}
              disabled={tab.disabled}
              className={`
                flex items-center space-x-2 py-3 px-1 border-b-2 font-medium text-sm
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : tab.disabled
                    ? 'border-transparent text-gray-400 cursor-not-allowed'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }
              `}
            >
              {tab.icon}
              <span>{tab.name}</span>
              {tab.badge && (
                <span className={`
                  inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                  ${activeTab === tab.id
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                  }
                `}>
                  {tab.badge}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'trades' && (
          <TradesTab strategyStore={strategyStore} />
        )}
        
        {activeTab === 'analysis' && (
          <AnalysisTab strategyStore={strategyStore} />
        )}
      </div>

      {/* Status Bar */}
      <div className="border-t border-gray-200 px-4 py-2 bg-gray-50">
        <div className="flex items-center justify-between text-xs text-gray-600">
          <div className="flex items-center space-x-4">
            <span>Trades: {tradesSummary.totalTrades}</span>
            <span>Total Premium: ${tradesSummary.totalPremium.toFixed(2)}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {isCalculating && (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                <span>Calculating...</span>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategyTabs;
