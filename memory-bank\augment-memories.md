# Options Strategy Analyzer
- Use quantity sign (positive/negative) to indicate position direction instead of a separate Buy/Sell field.
- Calculate debit/credit as -1 * quantity * premium.
- Display financial values with 2 decimal places and use cyan background for long positions and light red for short positions.
- Replace up-down sliders in financial input fields with plain numeric inputs that accept up to two decimal places.
- Premium represents actual premium when position is opened.
- Calc Premium should display theoretical Black-Scholes value without affecting other fields.
- Debit/credit is always calculated as -1 * quantity * premium regardless of volatility changes.
- Add a chart component to plot profit against remaining days to expiry at a specific stock price.
- Organize UI with a dedicated 'Sliders' section between 'PositionTable' and 'StrategyChart' containing all sliders (daysToVisualize, volatility, riskFreeRate, stock price) that update related charts when changed.
- Added Market Value column (quantity * calc Premium) and PnL column (sum of Market Value and debitCredit) to PositionTable.

# Options Pricing
- Implemented Black-Scholes model for options pricing and split components for better maintainability with low coupling and high cohesion.
- Use proper Black-Scholes model for option pricing calculations without custom adjustments.
- For Black-Scholes model validation: at stock price 22600, strike 22200, IV=30%, 3 days to expiry, call premium should be ~500 and put premium ~100.

# Database Integration
- Position records are saved to a PostgreSQL database with the connection URL stored in .env under DATABASE_URL key.
- The database table name is INDEX_OPTIONS_TRADES with column changes: type renamed to call_put, ExpiryDate to expirydate, debitCredit to debit_credit, created_at to created_date.
- Data types for strike, premium and debit_credit changed from INTEGER to NUMERIC(10,2) to support decimal values.
- Expiry dates are stored and handled without timezone considerations to avoid date shifts.
- Implemented automatic table creation if it doesn't exist with proper constraints.

# User Experience Improvements
- Added loading state to prevent user interaction during initial data loading.
- Implemented loading spinner and disabled controls during loading.
- Added detailed error messages for validation failures.
- Improved feedback when saving positions.
- Prevent user interaction with components during initial data loading by showing loading notifications and disabling actions like adding new positions until existing records are loaded.

# Date Handling
- Fixed issues with ExpiryDate field not displaying correctly after picking a date.
- Set default date to null for new positions, requiring user to select a date.
- Implemented timezone-agnostic date handling to prevent date shifts between UI and database.
- Improved date parsing and formatting for consistent display.
- Updated calculateDaysToExpiry function to handle dates properly without timezone issues.

# Yahoo Finance API Integration
- Implemented Yahoo Finance data integration using the yahoo-finance2 library instead of yahoo-stock-api.
- Added API endpoints on the local Node server to retrieve stock symbol information.
- Added support for Hang Seng index and Hong Kong stocks with test support for symbols like '0700.HK' and '9988.HK'.
- Implemented a Ticker component above StrategyParameters with fields for symbol (dropdown/text input for HSI/HHI/HTI/MHI).
- Ticker component displays shortName, current price, options IV (hardcoded as '30' initially), and supplementary price info.
- PositionTable's 'Current Stock Price' field is linked to and updated from Ticker.stockInfo.regularMarketPrice.
- Index symbols (HSI, HHI, HTI, MHI) are automatically prefixed with '^' when sent to the API.
