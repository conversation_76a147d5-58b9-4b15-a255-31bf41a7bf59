# Time Decay Chart Calculation Fixes

## Issues Fixed

### 1. Black-Scholes Parameter Passing Issue (FIXED ✅)
The "P&L At Various Dates Table" (time decay chart) was showing incorrect P&L and Market Value calculations due to improper Black-Scholes model parameter passing.

### 2. Days to Expiry Calculation Issue (FIXED ✅)
The time decay store was incorrectly calculating days to expiry for monthly options, causing monthly options like "2025-06" to expire at the beginning of June instead of at the end of June.

## Root Causes

### Issue 1: Black-Scholes Parameter Passing
The `usePnLAtVariousDateStore.js` was incorrectly calling the Black-Scholes function:

### BEFORE (Incorrect Implementation):
```javascript
// WRONG: Object-based parameter passing to calculateOptionPrice
const optionPrice = calculateOptionPrice({
  S: targetStockPrice,
  K: trade.strike,
  T: daysToExpiry / 365,
  r: riskFreeRate / 100,
  sigma: volatility / 100,
  type: trade.type?.toLowerCase() === 'call' ? 'call' : 'put'
});

// WRONG: Misusing calculatePositionPremium with a simple number
const marketValue = calculatePositionPremium(trade, optionPrice);
```

**Problems:**
1. `calculateOptionPrice` expects individual parameters `(type, S, K, r, v, T)` not an object
2. `calculatePositionPremium` expects a position object and parameters, not a pre-calculated price
3. Bypassed robust position utilities and parameter validation
4. Inconsistent with working PnL table implementation

### Issue 2: Days to Expiry Calculation  
The time decay store was manually calculating days to expiry using basic date arithmetic:

```javascript
// WRONG: Manual calculation that doesn't handle monthly options correctly
const expiryDate = new Date(trade.ExpiryDate);
const timeDiff = expiryDate.getTime() - date.getTime();
daysToExpiry = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));
```

**Problems:**
1. `new Date("2025-06")` creates a date at the beginning of June (June 1st)
2. Monthly options should expire at the end of the month (June 30th)
3. Ignored existing utility function that correctly handles this
4. Inconsistent with other components in the application

### AFTER (Correct Implementation):
```javascript
// CORRECT: Import and use the proper utility function
import { calculatePositionPremium, calculateDaysToExpiry } from '../utils/position-utils';

// CORRECT: Use utility function for days to expiry calculation
// This correctly handles monthly options (YYYY-MM) vs weekly options (YYYY-MM-DD)
const daysToExpiry = calculateDaysToExpiry(trade.ExpiryDate, date);

// CORRECT: Create position object with adjusted days to expiry
const positionWithDaysToExpiry = {
  ...trade,
  daysToExpiry: daysToExpiry // Use days remaining at target date
};

// CORRECT: Use calculatePositionPremium with proper parameters
const premium = calculatePositionPremium(
  positionWithDaysToExpiry,
  targetStockPrice,
  riskFreeRate,
  volatility
);

// CORRECT: Calculate market value and PnL properly
const marketValue = Math.round(trade.quantity * premium);
const pnl = marketValue - (trade.debitCredit || trade.premium || 0);
```

## How the calculateDaysToExpiry Utility Function Works

The `calculateDaysToExpiry` function in `src/utils/position-utils.js` correctly handles different expiry date formats:

### Monthly Options (YYYY-MM format like "2025-06"):
```javascript
if (expiryDate.length === 7) {
  // Monthly expiry format (YYYY-MM)
  // Get the last day of the month
  const [year, month] = expiryDate.split('-').map(num => parseInt(num));
  expiry = new Date(year, month - 1 + 1, 0); // Last day of the month
}
```

### Weekly Options (YYYY-MM-DD format like "2025-06-06"):
```javascript
else {
  // Daily expiry format (YYYY-MM-DD)
  const [year, month, day] = expiryDate.split('-').map(num => parseInt(num));
  expiry = new Date(year, month - 1, day); // Specific date
}
```

### Legacy Format Support:
- Converts YY-MM to YYYY-MM (e.g., "25-06" → "2025-06")
- Converts YY-MM-DD to YYYY-MM-DD (e.g., "25-06-06" → "2025-06-06")

## Benefits of the Fix

1. **Accurate Monthly Option Expiry**: Monthly options like "2025-06" now correctly expire on June 30th instead of June 1st
2. **Consistent with Other Components**: All components now use the same reliable utility function
3. **Proper Black-Scholes Integration**: The T parameter (time to expiry) is now calculated correctly for the Black-Scholes model
4. **Future-Proof**: Adding new expiry date formats only requires updating the utility function

## Technical Details

### Function Signatures Used:
```javascript
// CORRECT Black-Scholes function signature
calculateOptionPrice(type, S, K, r, v, T)

// CORRECT Position premium function signature  
calculatePositionPremium(position, stockPrice, riskFreeRate, volatility)
```

### Expiry Handling:
- The `calculatePositionPremium` function automatically detects when `daysToExpiry <= 0`
- Returns intrinsic value for expired options: `max(0, S-K)` for calls, `max(0, K-S)` for puts
- No need for separate expiry logic in the time decay store

## Files Modified:
- `src/store/usePnLAtVariousDateStore.js` - Fixed calculation logic and removed unused import
- `memory-bank/time-decay-calculation-fix.md` - Updated documentation to include both fixes

## Verification:
- Application compiles successfully with no errors
- Both frontend (port 3003) and backend (port 5003) servers running
- Calculation logic now matches the working PnL table component exactly

## Impact:
The time decay chart will now show accurate P&L and Market Value calculations that are consistent with the PnL table, properly reflecting Black-Scholes model results for options at various future dates.
