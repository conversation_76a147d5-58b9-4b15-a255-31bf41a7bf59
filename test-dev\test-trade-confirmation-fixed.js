/**
 * Fixed test script for extractTradeConfirmation() function
 * This version fixes the case sensitivity issue and shows actual trade data
 */

const fs = require('fs');
const path = require('path');
const { parseStatement } = require('../src/server-utils/statement-parser');

// Import required modules for direct text extraction
const PDFExtract = require('pdf.js-extract').PDFExtract;

/**
 * Helper function to extract a section between two headers (case insensitive)
 */
function extractSectionCaseInsensitive(text, startHeader, endHeader) {
  const startIndex = text.toUpperCase().indexOf(startHeader.toUpperCase());
  if (startIndex === -1) return null;

  let endIndex = text.length;
  if (endHeader) {
    const endHeaderIndex = text.toUpperCase().indexOf(endHeader.toUpperCase(), startIndex);
    if (endHeaderIndex !== -1) {
      endIndex = endHeaderIndex;
    }
  }

  return text.substring(startIndex, endIndex);
}

/**
 * Parse a trade line (improved version)
 */
function parseTradeLineImproved(line) {
  // Skip header lines and empty lines
  if (!line || line.includes('TRADE DATE') || line.includes('ORDER NO') || line.includes('---') || line.length < 10) {
    return null;
  }

  // Clean the line and split by whitespace (2 or more spaces or tabs)
  const parts = line.trim().split(/\s{2,}|\t+/).filter(part => part.trim());
  
  console.log('  Parsing line:', line);
  console.log('  Split into parts:', parts);
  
  if (parts.length >= 6) {
    const trade = {
      date: parts[0] || '',
      orderNo: parts[1] || '',
      refNo: parts[2] || '',
      market: parts[3] || '',
      description: parts[4] || '',
      buySell: parts[5] || '',
      quantity: parts[6] || '',
      price: parts[7] || '',
      amount: parts[8] || '',
      commission: parts[9] || '',
      currency: parts[10] || 'HKD'
    };
    
    console.log('  Parsed trade:', trade);
    return trade;
  }

  return null;
}

async function testTradeConfirmationFixed() {
  try {
    console.log('🧪 Fixed Trade Confirmation Test');
    console.log('=' .repeat(60));

    // File path and password
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const password = '6369';

    console.log(`📁 PDF File: ${pdfPath}`);
    console.log(`🔐 Password: ${password}`);
    console.log('');

    // Read and parse PDF
    const pdfBuffer = fs.readFileSync(pdfPath);
    console.log(`✅ PDF file loaded (${pdfBuffer.length} bytes)`);

    // Extract full text
    console.log('📖 Extracting full PDF text...');
    const pdfExtract = new PDFExtract();
    const tempFilePath = path.join(__dirname, '../temp_debug.pdf');
    fs.writeFileSync(tempFilePath, pdfBuffer);

    const extractOptions = { password: password.trim() };
    const data = await new Promise((resolve, reject) => {
      pdfExtract.extract(tempFilePath, extractOptions, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });

    const fullText = data.pages.map(page =>
      page.content.map(item => item.str).join(' ')
    ).join('\n');

    fs.unlinkSync(tempFilePath);

    // Extract trade confirmation section with case insensitive search
    console.log('🔍 EXTRACTING TRADE CONFIRMATION SECTION:');
    console.log('=' .repeat(60));
    
    const tradeSection = extractSectionCaseInsensitive(fullText, 'TRADE CONFIRMATION', 'POSITION');
    
    if (tradeSection) {
      console.log('✅ Trade Confirmation section found');
      console.log(`Section length: ${tradeSection.length} characters`);
      console.log('');
      
      // Show first part of the section
      console.log('📝 TRADE SECTION CONTENT (first 1000 chars):');
      console.log('=' .repeat(60));
      console.log(tradeSection.substring(0, 1000));
      console.log('...[truncated]...');
      console.log('');
      
      // Parse trade lines
      console.log('🔍 PARSING TRADE LINES:');
      console.log('=' .repeat(60));
      
      const lines = tradeSection.split('\n');
      const trades = [];
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line.length > 0) {
          console.log(`Line ${i + 1}: "${line}"`);
          const trade = parseTradeLineImproved(line);
          if (trade) {
            trades.push(trade);
            console.log('  ✅ Trade parsed successfully');
          } else {
            console.log('  ❌ Not a trade line');
          }
          console.log('');
        }
      }
      
      console.log('📊 FINAL RESULTS:');
      console.log('=' .repeat(60));
      console.log(`Found ${trades.length} trade(s)`);
      
      if (trades.length > 0) {
        trades.forEach((trade, index) => {
          console.log(`\n📈 Trade #${index + 1}:`);
          Object.keys(trade).forEach(key => {
            console.log(`   ${key}: ${trade[key]}`);
          });
        });
      }
      
      console.log('');
      console.log('🔧 RAW JSON OUTPUT:');
      console.log(JSON.stringify(trades, null, 2));
      
    } else {
      console.log('❌ Trade Confirmation section not found');
    }

    console.log('');
    console.log('✅ Fixed test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testTradeConfirmationFixed()
    .then(() => {
      console.log('🎉 Fixed test completed!');
    })
    .catch((error) => {
      console.error('💥 Fixed test failed:', error);
    });
}

module.exports = { testTradeConfirmationFixed };
