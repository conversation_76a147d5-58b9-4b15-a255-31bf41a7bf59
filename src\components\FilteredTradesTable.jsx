import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';

// Enhanced frozen column style with stronger visual distinction
const frozenColumnStyle = {
  position: 'sticky',
  backgroundColor: '#f8fafc',
  borderRight: '3px solid #3b82f6',
  boxShadow: '3px 0 8px -2px rgba(59, 130, 246, 0.2)'
};

/**
 * FilteredTradesTable Component
 * 
 * Displays filtered trades data in a collapsible table format with:
 * - Frozen left columns for trade basic info
 * - Dynamic columns for each price point showing Premium and PnL
 * - Horizontal scrolling with frozen trade info columns
 * - Collapsible functionality via clickable header
 */
const FilteredTradesTable = ({
  filteredPnLData,
  selectedExpiryDate,
  selectedStockSymbol,
  targetPrice // <-- Add this prop
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const scrollContainerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });

  // Extract data from filtered PnL data
  const { positionData = [], pricePoints = [] } = filteredPnLData || {};
  const tradesCount = positionData.length;

  // Sorting functionality
  const sortedPositionData = useMemo(() => {
    if (!sortConfig.key || !positionData) return positionData;

    return [...positionData].sort((a, b) => {
      let aValue, bValue;

      if (sortConfig.key.startsWith('pnl_') || sortConfig.key.startsWith('premium_')) {
        aValue = a[sortConfig.key] || 0;
        bValue = b[sortConfig.key] || 0;
      } else {
        // Handle trade properties
        aValue = a.trade[sortConfig.key] || '';
        bValue = b.trade[sortConfig.key] || '';
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
      }

      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();

      if (sortConfig.direction === 'asc') {
        return aStr < bStr ? -1 : aStr > bStr ? 1 : 0;
      } else {
        return aStr > bStr ? -1 : aStr < bStr ? 1 : 0;
      }
    });
  }, [positionData, sortConfig]);

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const getSortIcon = (key) => {
    if (sortConfig.key !== key) {
      return <span className="text-gray-400">↕</span>;
    }
    return sortConfig.direction === 'asc' ?
      <span className="text-blue-600">↑</span> :
      <span className="text-blue-600">↓</span>;
  };

  // Calculate totals for PnL columns
  const pnlTotals = useMemo(() => {
    const totals = {};

    pricePoints.forEach(price => {
      totals[price] = 0;

      positionData.forEach(position => {
        const pnl = position[`pnl_${price}`] || 0;
        if (typeof pnl === 'number') {
          totals[price] += pnl;
        }
      });
    });

    return totals;
  }, [positionData, pricePoints]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Mouse drag scrolling handlers
  const handleMouseDown = useCallback((e) => {
    if (!scrollContainerRef.current) return;
    setIsDragging(true);
    setDragStart({
      x: e.pageX,
      scrollLeft: scrollContainerRef.current.scrollLeft
    });
    scrollContainerRef.current.style.cursor = 'grabbing';
    scrollContainerRef.current.style.userSelect = 'none';
  }, []);

  const handleMouseMove = useCallback((e) => {
    if (!isDragging || !scrollContainerRef.current) return;
    e.preventDefault();
    const x = e.pageX;
    const walk = (x - dragStart.x) * 2; // Multiply by 2 for faster scrolling
    scrollContainerRef.current.scrollLeft = dragStart.scrollLeft - walk;
  }, [isDragging, dragStart.x, dragStart.scrollLeft]);

  const handleMouseUp = useCallback(() => {
    if (!scrollContainerRef.current) return;
    setIsDragging(false);
    scrollContainerRef.current.style.cursor = 'grab';
    scrollContainerRef.current.style.userSelect = 'auto';
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (!scrollContainerRef.current) return;
    setIsDragging(false);
    scrollContainerRef.current.style.cursor = 'grab';
    scrollContainerRef.current.style.userSelect = 'auto';
  }, []);

  // Add event listeners for mouse events
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseup', handleMouseUp);
    container.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseup', handleMouseUp);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [handleMouseMove, handleMouseUp, handleMouseLeave]);

  // Scroll to target price column on first display
  useEffect(() => {
    if (isExpanded && scrollContainerRef.current && targetPrice !== undefined && pricePoints.length > 0) {
      // Find the index of the target price
      const targetIndex = pricePoints.indexOf(targetPrice);
      if (targetIndex !== -1) {
        // Estimate the scroll position based on column width (min-w-28)
        const columnWidth = 112; // min-w-28 in px
        const scrollLeft = (targetIndex * 2) * columnWidth; // 2 columns per price point (Premium + PnL)
        scrollContainerRef.current.scrollLeft = scrollLeft;
      }
    }
  }, [isExpanded, targetPrice, pricePoints]);

  // Don't render if no data
  if (!filteredPnLData || tradesCount === 0 || pricePoints.length === 0) {
    return null;
  }

  return (
    <div className="mt-4 border-t border-gray-200">
      {/* Show Table Button */}
      <div className="pt-4">
        <button
          onClick={toggleExpanded}
          className="flex items-center justify-center w-full py-3 px-4 bg-blue-500 hover:bg-blue-600 border border-blue-600 rounded-lg transition-colors text-sm font-medium text-white shadow-sm hover:shadow-md"
        >
          <svg
            className="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7h18M3 12h18M3 17h18" />
          </svg>
          {isExpanded ? 'Hide Data Table' : 'Show Data Table'}
          <span className="ml-2 text-xs text-blue-100">
            ({tradesCount} trades, {pricePoints.length} price points)
          </span>
          <svg
            className={`w-4 h-4 ml-2 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      {/* Table Content */}
      {isExpanded && (
        <div className="mt-4">
          <div className="bg-gray-50 p-3 rounded-t-lg border-x border-t border-gray-200">
            <div className="flex items-center justify-between">
              <p className="text-xs text-gray-600">
                <strong>Instructions:</strong> Scroll horizontally or <strong>click and drag</strong> to view all price points.
                Trade info columns (with blue border) are frozen on the left. Click column headers to sort.
              </p>
              <div className="flex items-center text-xs text-gray-500">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                </svg>
                Drag to scroll
              </div>
            </div>
          </div>

          <div
            ref={scrollContainerRef}
            className={`overflow-x-auto border-x border-b border-gray-200 cursor-grab ${isDragging ? 'select-none' : ''}`}
            onMouseDown={handleMouseDown}
            style={{
              cursor: isDragging ? 'grabbing' : 'grab',
              userSelect: isDragging ? 'none' : 'auto'
            }}
          >
            <div style={{ minWidth: 'max-content' }}>
              <table className="text-xs border-collapse w-full">
                <thead className="bg-gray-50">
                  <tr>
                    {/* Fixed columns for trade basic info */}
                    <th
                      className="sticky left-0 z-10 bg-slate-50 border border-gray-300 p-2 text-left font-medium min-w-20 max-w-20 border-r-blue-500 border-r-2 cursor-pointer hover:bg-blue-100"
                      onClick={() => handleSort('ticker')}
                    >
                      Ticker {getSortIcon('ticker')}
                    </th>
                    <th
                      className="sticky left-20 z-10 bg-slate-50 border border-gray-300 p-2 text-left font-medium min-w-12 max-w-12 border-r-blue-500 border-r-2 cursor-pointer hover:bg-blue-100"
                      onClick={() => handleSort('type')}
                    >
                      Type {getSortIcon('type')}
                    </th>
                    <th
                      className="sticky left-32 z-10 bg-slate-50 border border-gray-300 p-2 text-left font-medium min-w-16 max-w-16 border-r-blue-500 border-r-2 cursor-pointer hover:bg-blue-100"
                      onClick={() => handleSort('strike')}
                    >
                      Strike {getSortIcon('strike')}
                    </th>
                    <th
                      className="sticky left-48 z-10 bg-slate-50 border border-gray-300 p-2 text-left font-medium min-w-12 max-w-12 border-r-blue-500 border-r-2 cursor-pointer hover:bg-blue-100"
                      onClick={() => handleSort('quantity')}
                    >
                      Qty {getSortIcon('quantity')}
                    </th>
                    <th
                      className="sticky left-60 z-10 bg-slate-50 border border-gray-300 p-2 text-left font-medium min-w-20 max-w-20 border-r-blue-500 border-r-2 cursor-pointer hover:bg-blue-100"
                      onClick={() => handleSort('ExpiryDate')}
                    >
                      Expiry {getSortIcon('ExpiryDate')}
                    </th>
                    <th
                      className="sticky left-80 z-10 bg-slate-50 border border-gray-300 p-2 text-left font-medium min-w-20 max-w-20 cursor-pointer hover:bg-blue-100"
                      style={frozenColumnStyle}
                      onClick={() => handleSort('premium')}
                    >
                      Orig. Premium {getSortIcon('premium')}
                    </th>

                    {/* Dynamic columns for each price point */}
                    {pricePoints.map(price => (
                      <React.Fragment key={price}>
                        <th
                          className="border border-gray-300 p-2 text-center font-medium bg-blue-50 min-w-28 cursor-pointer hover:bg-blue-100"
                          onClick={() => handleSort(`premium_${price}`)}
                        >
                          Premium<br/>@ ${price} {getSortIcon(`premium_${price}`)}
                        </th>
                        <th
                          className="border border-gray-300 p-2 text-center font-medium bg-green-50 min-w-24 cursor-pointer hover:bg-green-100"
                          onClick={() => handleSort(`pnl_${price}`)}
                        >
                          PnL<br/>@ ${price} {getSortIcon(`pnl_${price}`)}
                        </th>
                      </React.Fragment>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {sortedPositionData.map((tradeData, index) => {
                    const trade = tradeData.trade;
                    return (
                      <tr key={tradeData.id || index} className="hover:bg-gray-50">
                        {/* Fixed columns */}
                        <td className="sticky left-0 z-10 bg-white border border-gray-300 p-2 font-mono text-xs min-w-20 max-w-20 truncate border-r-blue-500 border-r-2">
                          {trade.ticker}
                        </td>
                        <td className="sticky left-20 z-10 bg-white border border-gray-300 p-2 min-w-12 max-w-12 truncate border-r-blue-500 border-r-2">
                          {trade.type}
                        </td>
                        <td className="sticky left-32 z-10 bg-white border border-gray-300 p-2 min-w-16 max-w-16 truncate border-r-blue-500 border-r-2">
                          {trade.strike}
                        </td>
                        <td className={`sticky left-48 z-10 bg-white border border-gray-300 p-2 min-w-12 max-w-12 border-r-blue-500 border-r-2 ${
                          trade.quantity < 0 ? 'text-red-600' : 'text-green-700'
                        }`}>
                          {trade.quantity}
                        </td>
                        <td className="sticky left-60 z-10 bg-white border border-gray-300 p-2 min-w-20 max-w-20 truncate border-r-blue-500 border-r-2">
                          {trade.ExpiryDate || trade.expiry}
                        </td>
                        <td
                          className="sticky left-80 z-10 bg-white border border-gray-300 p-2 min-w-20 max-w-20 truncate"
                          style={frozenColumnStyle}
                        >
                          {typeof trade.premium === 'number' ?
                            `$${trade.premium.toFixed(2)}` :
                            trade.premium || 'N/A'
                          }
                        </td>

                        {/* Dynamic columns for premium and PnL */}
                        {pricePoints.map(price => {
                          const premium = tradeData[`premium_${price}`];
                          const pnl = tradeData[`pnl_${price}`];
                          const isTarget = targetPrice !== undefined && price === targetPrice;
                          return (
                            <React.Fragment key={`${tradeData.id || index}_${price}`}>
                              {/* Premium */}
                              <td className={`border border-gray-300 p-2 text-right min-w-28 ${isTarget ? 'bg-cyan-200 font-bold' : ''}`}>
                                {typeof premium === 'number' ?
                                  `$${premium.toFixed(2)}` :
                                  'N/A'
                                }
                              </td>
                              {/* PnL */}
                              <td className={`border border-gray-300 p-2 text-right min-w-24 ${
                                typeof pnl === 'number' ?
                                  (pnl < 0 ? 'text-red-600' : pnl > 0 ? 'text-green-700' : 'text-gray-700') :
                                  'text-gray-700'
                              }`}>
                                {typeof pnl === 'number' ?
                                  `$${pnl.toLocaleString()}` :
                                  'N/A'
                                }
                              </td>
                            </React.Fragment>
                          );
                        })}
                      </tr>
                    );
                  })}

                  {/* Totals row */}
                  <tr className="bg-gray-100 font-bold border-t-2 border-gray-400">
                    <td className="sticky left-0 z-10 bg-gray-200 border border-gray-300 p-2 text-left min-w-20 max-w-20 border-r-blue-500 border-r-2">
                      TOTAL
                    </td>
                    <td className="sticky left-20 z-10 bg-gray-200 border border-gray-300 p-2 min-w-12 max-w-12 border-r-blue-500 border-r-2"></td>
                    <td className="sticky left-32 z-10 bg-gray-200 border border-gray-300 p-2 min-w-16 max-w-16 border-r-blue-500 border-r-2"></td>
                    <td className="sticky left-48 z-10 bg-gray-200 border border-gray-300 p-2 min-w-12 max-w-12 border-r-blue-500 border-r-2"></td>
                    <td className="sticky left-60 z-10 bg-gray-200 border border-gray-300 p-2 min-w-20 max-w-20 border-r-blue-500 border-r-2"></td>
                    <td className="sticky left-80 z-10 bg-gray-200 border border-gray-300 p-2 min-w-20 max-w-20"></td>

                    {/* Totals for each price point */}
                    {pricePoints.map(price => {
                      const totalPnL = pnlTotals[price] || 0;
                      return (
                        <React.Fragment key={`total_${price}`}>
                          {/* Premium total (empty) */}
                          <td className="border border-gray-300 p-2 text-right min-w-28 bg-gray-200"></td>
                          {/* PnL total */}
                          <td className={`border border-gray-300 p-2 text-right min-w-24 bg-gray-200 ${
                            totalPnL < 0 ? 'text-red-600' : totalPnL > 0 ? 'text-green-700' : 'text-gray-700'
                          }`}>
                            ${totalPnL.toLocaleString()}
                          </td>
                        </React.Fragment>
                      );
                    })}
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Table info */}
          <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-b-lg border-x border-b border-gray-200">
            <div className="flex flex-wrap gap-4">
              <span>• Premium and PnL calculated using Black-Scholes pricing</span>
              <span>• {pricePoints.length} price points from ${Math.min(...pricePoints)} to ${Math.max(...pricePoints)}</span>
              <span>• Green = Profit, Red = Loss</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilteredTradesTable;
