const express = require('express');
// const { Pool } = require('pg');
const cors = require('cors');
const bodyParser = require('body-parser');
const multer = require('multer');
require('dotenv').config();

// Import stock utilities
const { getSymbolInfo, getHistoricalPrices } = require('./src/server-utils/stockUtils');

// Import Firebase utilities
const { db } = require('./src/server-utils/firebase.js');
const admin = require('firebase-admin');

// Import trade parser utility
const { parseTrades, getUnderlyingSymbol } = require('./src/server-utils/trade-parser.js');

// Import statement parser utility
const { parseStatement } = require('./src/server-utils/statement-parser.js');

// Import statement validation utility
const { validateStatementData, sanitizeStatementData } = require('./src/utils/statement-validation.js');

// Import exchange holidays utility
const { fetch_exchange_holidays, loadExchangeHolidaysFromFile, initializeHolidayData, getExchangeHolidays } = require('./src/server-utils/exchange-holidays.js');

const app = express();
const port = process.env.NODE_PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed'), false);
    }
  }
});

// Create a PostgreSQL connection pool
// const pool = new Pool({
//   connectionString: process.env.WILL9700_DB,
// });

// API endpoint to fetch all positions
// app.get('/api/positions', async (req, res) => {
//   try {
//     const client = await pool.connect();
//     const query = `
//       SELECT id, quantity, call_put, strike, expirydate, premium, debit_credit, created_date
//       FROM INDEX_OPTIONS_TRADES
//       ORDER BY created_date DESC;
//     `;
//     const result = await client.query(query);

//     // Process the results to ensure proper data types
//     const positions = result.rows.map(position => {
//       // Debug the date format from PostgreSQL
//       console.log('Retrieved expirydate from DB:', position.expirydate);
//       console.log('ExpiryDate type:', typeof position.expirydate);
//       console.log('ExpiryDate instanceof Date:', position.expirydate instanceof Date);
//       if (position.expirydate) {
//         console.log('ExpiryDate toString():', position.expirydate.toString());
//         console.log('ExpiryDate toISOString():', position.expirydate instanceof Date ? position.expirydate.toISOString() : 'Not a Date object');
//       }

//       // Format the date as YYYY-MM-DD for the date picker
//       let formattedDate = null;
//       if (position.expirydate) {
//         // Check if it's a Date object or a string
//         if (position.expirydate instanceof Date) {
//           // Format the Date object to YYYY-MM-DD
//           const year = position.expirydate.getFullYear();
//           const month = String(position.expirydate.getMonth() + 1).padStart(2, '0');
//           const day = String(position.expirydate.getDate()).padStart(2, '0');
//           formattedDate = `${year}-${month}-${day}`;
//         } else if (typeof position.expirydate === 'string') {
//           // If it's already a string, check if it's in ISO format
//           if (position.expirydate.includes('T')) {
//             // It's an ISO string, extract just the date part
//             formattedDate = position.expirydate.split('T')[0];
//           } else {
//             // It's already a date string, use it directly
//             formattedDate = position.expirydate;
//           }
//         }
//         console.log('Formatted date for frontend:', formattedDate);
//       }

//       return {
//         id: parseInt(position.id),
//         quantity: parseInt(position.quantity),
//         // Map the new column names to the old ones for backward compatibility with the frontend
//         type: position.call_put.trim(), // Trim to remove any padding from CHAR(5)
//         strike: parseFloat(position.strike),
//         ExpiryDate: formattedDate, // Use the formatted date string
//         premium: parseFloat(position.premium),
//         debitCredit: parseFloat(position.debit_credit),
//         created_at: position.created_date,
//         // Add saved flag to indicate this position is already in the database
//         saved: true
//       };
//     });

//     client.release();

//     res.status(200).json({ positions });
//   } catch (error) {
//     console.error('Error fetching positions:', error);
//     res.status(500).json({ message: 'Error fetching positions', error: error.message });
//   }
// });

// Add this to server.js (anywhere before the static file serving)
app.delete('/api/deleteAllTrades', async (req, res) => {
  try {
    if (!db) {
      return res.status(500).json({ error: true, message: 'Firestore database is not initialized' });
    }
    const tradesRef = db.collection('trades');
    const snapshot = await tradesRef.get();
    const batch = db.batch();
    snapshot.forEach((doc) => {
      batch.delete(doc.ref);
    });
    await batch.commit();
    return res.status(200).json({ message: 'All trades deleted' });
  } catch (error) {
    console.error('Error deleting trades:', error);
    return res.status(500).json({ message: 'Failed to delete trades', error: error.message });
  }
});

// API endpoint to save a position
// app.post('/api/savePosition', async (req, res) => {
//   const { quantity, type, strike, ExpiryDate, premium, debitCredit } = req.body;

//   // Check which fields are missing and log them
//   const missingFields = [];
//   if (!quantity && quantity !== 0) missingFields.push('quantity');
//   if (!type) missingFields.push('type');
//   if (!strike && strike !== 0) missingFields.push('strike');
//   if (!ExpiryDate) missingFields.push('ExpiryDate');
//   if (!premium && premium !== 0) missingFields.push('premium');
//   if (debitCredit === undefined) missingFields.push('debitCredit');

//   console.log('Received fields:', { quantity, type, strike, ExpiryDate, premium, debitCredit });

//   if (missingFields.length > 0) {
//     console.log('Missing fields:', missingFields);
//     return res.status(400).json({
//       message: `Missing required fields: ${missingFields.join(', ')}`,
//       missingFields
//     });
//   }

//   // Validate data according to schema constraints
//   if (quantity < -999 || quantity > 999) {
//     return res.status(400).json({ message: 'Quantity must be between -999 and 999' });
//   }

//   if (type !== 'Call' && type !== 'Put') {
//     return res.status(400).json({ message: 'Type must be either "Call" or "Put"' });
//   }

//   if (strike < 0 || strike > 99999) {
//     return res.status(400).json({ message: 'Strike must be between 0 and 99999' });
//   }

//   try {
//     console.log('Saving position with values:', {
//       quantity,
//       type,
//       strike,
//       ExpiryDate,
//       premium,
//       debitCredit
//     });

//     // First, make sure the table exists
//     const client = await pool.connect();

//     // Create the table if it doesn't exist
//     const createTableQuery = `
//       CREATE TABLE IF NOT EXISTS INDEX_OPTIONS_TRADES (
//         id SERIAL PRIMARY KEY,
//         quantity INTEGER CHECK (quantity >= -999 AND quantity <= 999),
//         call_put CHAR(5) CHECK (call_put IN ('Call', 'Put')),
//         strike NUMERIC(10, 2) CHECK (strike >= 0 AND strike <= 99999),
//         expirydate DATE NOT NULL,
//         premium NUMERIC(10, 2),
//         debit_credit NUMERIC(10, 2) NOT NULL,
//         created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
//       );
//     `;

//     await client.query(createTableQuery);

//     // Now insert the data
//     const query = `
//       INSERT INTO INDEX_OPTIONS_TRADES (quantity, call_put, strike, expirydate, premium, debit_credit)
//       VALUES ($1, $2, $3, $4, $5, $6)
//       RETURNING id;
//     `;

//     // Convert values to match the database schema
//     // For ExpiryDate, just use the date string directly (YYYY-MM-DD format)
//     // PostgreSQL DATE type doesn't store time information, so no timezone issues
//     const values = [
//       parseInt(quantity),
//       type,
//       parseFloat(strike),
//       ExpiryDate, // Use the date string directly
//       parseFloat(premium),
//       parseFloat(debitCredit)
//     ];

//     console.log('Executing query with values:', values);
//     console.log('SQL Query:', query);

//     const result = await client.query(query, values);
//     client.release();

//     console.log('Position saved successfully with ID:', result.rows[0].id);
//     res.status(201).json({ message: 'Position saved successfully', id: result.rows[0].id });
//   } catch (error) {
//     console.error('Error saving position:', error);
//     console.error('Error details:', {
//       name: error.name,
//       message: error.message,
//       stack: error.stack,
//       code: error.code,
//       detail: error.detail,
//       hint: error.hint,
//       position: error.position
//     });
//     res.status(500).json({
//       message: 'Error saving position',
//       error: error.message,
//       details: {
//         code: error.code,
//         detail: error.detail,
//         hint: error.hint
//       }
//     });
//   }
// });

// API endpoint to check database connection and schema
// app.get('/api/check-db', async (_, res) => {
//   try {
//     const client = await pool.connect();

//     // Check if the table exists
//     const tableCheckQuery = `
//       SELECT EXISTS (
//         SELECT FROM information_schema.tables
//         WHERE table_name = 'index_options_trades'
//       );
//     `;
//     const tableExists = await client.query(tableCheckQuery);

//     if (!tableExists.rows[0].exists) {
//       client.release();
//       return res.status(200).json({
//         status: 'error',
//         message: 'Table index_options_trades does not exist'
//       });
//     }

//     // Check table schema
//     const schemaQuery = `
//       SELECT column_name, data_type, character_maximum_length, is_nullable
//       FROM information_schema.columns
//       WHERE table_name = 'index_options_trades'
//       ORDER BY ordinal_position;
//     `;
//     const schema = await client.query(schemaQuery);

//     client.release();

//     res.status(200).json({
//       status: 'success',
//       message: 'Database connection successful',
//       tableExists: tableExists.rows[0].exists,
//       schema: schema.rows
//     });
//   } catch (error) {
//     console.error('Error checking database:', error);
//     res.status(500).json({
//       status: 'error',
//       message: 'Error checking database',
//       error: error.message,
//       details: {
//         code: error.code,
//         detail: error.detail,
//         hint: error.hint
//       }
//     });
//   }
// });

// API endpoint to create the table if it doesn't exist
// app.get('/api/create-table', async (_, res) => {
//   try {
//     const client = await pool.connect();

//     // Create the table if it doesn't exist
//     const createTableQuery = `
//       CREATE TABLE IF NOT EXISTS INDEX_OPTIONS_TRADES (
//         id SERIAL PRIMARY KEY,
//         quantity INTEGER CHECK (quantity >= -999 AND quantity <= 999),
//         call_put CHAR(5) CHECK (call_put IN ('Call', 'Put')),
//         strike NUMERIC(10, 2) CHECK (strike >= 0 AND strike <= 99999),
//         expirydate DATE NOT NULL,
//         premium NUMERIC(10, 2),
//         debit_credit NUMERIC(10, 2) NOT NULL,
//         created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
//       );
//     `;
//     await client.query(createTableQuery);
//     client.release();

//     res.status(200).json({
//       status: 'success',
//       message: 'Table created or already exists'
//     });
//   } catch (error) {
//     console.error('Error creating table:', error);
//     res.status(500).json({
//       status: 'error',
//       message: 'Error creating table',
//       error: error.message,
//       details: {
//         code: error.code,
//         detail: error.detail,
//         hint: error.hint
//       }
//     });
//   }
// });

// API endpoint to fetch distinct expiry dates
// app.get('/api/expiry-dates', async (req, res) => {
//   try {
//     const client = await pool.connect();
//     const query = `
//       SELECT DISTINCT expirydate
//       FROM INDEX_OPTIONS_TRADES
//       ORDER BY expirydate ASC;
//     `;
//     const result = await client.query(query);
//     client.release();

//     // Format dates as YYYY-MM-DD
//     const expiryDates = result.rows.map(row => {
//       const date = new Date(row.expirydate);
//       const year = date.getFullYear();
//       const month = String(date.getMonth() + 1).padStart(2, '0');
//       const day = String(date.getDate()).padStart(2, '0');
//       return `${year}-${month}-${day}`;
//     });

//     res.status(200).json({ expiryDates });
//   } catch (error) {
//     console.error('Error fetching expiry dates:', error);
//     res.status(500).json({ message: 'Error fetching expiry dates', error: error.message });
//   }
// });

// API endpoint to fetch trades by expiry date
// app.get('/api/trades-by-expiry/:expiryDate', async (req, res) => {
//   const { expiryDate } = req.params;
//   try {
//     const client = await pool.connect();
//     const query = `
//       SELECT id, quantity, call_put, strike, expirydate, premium, debit_credit, created_date
//       FROM INDEX_OPTIONS_TRADES
//       WHERE expirydate = $1
//       ORDER BY created_date DESC;
//     `;
//     const result = await client.query(query, [expiryDate]);
//     client.release();

//     const trades = result.rows.map(trade => {
//       // Format the date as YYYY-MM-DD
//       let formattedDate = null;
//       if (trade.expirydate) {
//         if (trade.expirydate instanceof Date) {
//           const year = trade.expirydate.getFullYear();
//           const month = String(trade.expirydate.getMonth() + 1).padStart(2, '0');
//           const day = String(trade.expirydate.getDate()).padStart(2, '0');
//           formattedDate = `${year}-${month}-${day}`;
//         } else if (typeof trade.expirydate === 'string') {
//           if (trade.expirydate.includes('T')) {
//             formattedDate = trade.expirydate.split('T')[0];
//           } else {
//             formattedDate = trade.expirydate;
//           }
//         }
//       }
//       return {
//         id: parseInt(trade.id),
//         quantity: parseInt(trade.quantity),
//         type: trade.call_put.trim(),
//         strike: parseFloat(trade.strike),
//         ExpiryDate: formattedDate,
//         premium: parseFloat(trade.premium),
//         debitCredit: parseFloat(trade.debit_credit),
//         created_at: trade.created_date,
//         saved: true
//       };
//     });

//     res.status(200).json({ trades });
//   } catch (error) {
//     console.error(`Error fetching trades for expiry date ${expiryDate}:`, error);
//     res.status(500).json({ message: `Error fetching trades for expiry date ${expiryDate}`, error: error.message });
//   }
// });

// Firebase Endpoints
// Endpoint to fetch trades from Firestore
app.get('/api/firestore/trades', async (req, res) => {
  try {
    // Check if Firestore is initialized
    if (!db) {
      console.error('Firestore database is not initialized');
      return res.status(500).json({
        error: true,
        message: 'Firestore database is not initialized',
      });
    }

    // Get all trades from the 'trades' collection
    const tradesSnapshot = await db.collection('trades').get();

    if (tradesSnapshot.empty) {
      return res.status(200).json({ trades: [] });
    }

    // Process the results to match the expected format in the frontend
    const trades = tradesSnapshot.docs.map(doc => {
      const trade = doc.data();

      // Map Firestore fields to the expected frontend format
      // Special handling for futures trades
      const tradeType = (trade.type || '').toLowerCase();
      const isFuture = tradeType === 'future';

      // Handle strike field - keep 'N/A' as is for futures
      let strikeValue = trade.strike;
      if (strikeValue !== 'N/A' && !isFuture) {
        strikeValue = parseFloat(strikeValue) || 0;
      }

      // Extract stock from ticker using getUnderlyingSymbol if not already present
      const stockFromTicker = trade.ticker ? getUnderlyingSymbol(trade.ticker) : '';
      const stockField = trade.stock || stockFromTicker;

      return {
        id: doc.id,
        quantity: trade.quantity || 0,
        type: trade.type || '',  // 'Call', 'Put', or 'Future'
        strike: strikeValue,
        ExpiryDate: trade.expiryDate || trade.expiry || '',
        premium: trade.cost || trade.price || 0,
        debitCredit: (trade.quantity || 0) * (trade.cost || trade.price || 0),
        created_at: trade.uploadedAt ? trade.uploadedAt.toDate().toISOString() : new Date().toISOString(),
        ticker: trade.ticker || '',
        originalLine: trade.originalLine || '',
        stock: stockField // Add stock field using getUnderlyingSymbol
      };
    });

    res.status(200).json({ trades });
  } catch (error) {
    console.error('Error fetching trades from Firestore:', error);
    return res.status(500).json({
      error: true,
      message: 'Error fetching trades from Firestore',
      details: error.message
    });
  }
});

// API endpoint to get stock symbol information
app.get('/api/stock/:symbol', async (req, res) => {
  try {
    const { symbol } = req.params;

    if (!symbol) {
      return res.status(400).json({
        error: true,
        message: 'Symbol parameter is required'
      });
    }

    console.log(`Fetching stock info for symbol: ${symbol}`);
    const stockInfo = await getSymbolInfo(symbol);

    res.status(200).json(stockInfo);
  } catch (error) {
    console.error('Error fetching stock info:', error);
    res.status(500).json({
      error: true,
      message: 'Error fetching stock information',
      details: error.message
    });
  }
});

// API endpoint to get historical prices
app.get('/api/stock/:symbol/history', async (req, res) => {
  try {
    const { symbol } = req.params;
    const { startDate, endDate, frequency } = req.query;

    if (!symbol) {
      return res.status(400).json({
        error: true,
        message: 'Symbol parameter is required'
      });
    }

    if (!startDate || !endDate) {
      return res.status(400).json({
        error: true,
        message: 'startDate and endDate query parameters are required'
      });
    }

    console.log(`Fetching historical data for symbol: ${symbol}`);
    const historicalData = await getHistoricalPrices(
      symbol,
      new Date(startDate),
      new Date(endDate),
      frequency || '1d'
    );

    res.status(200).json(historicalData);
  } catch (error) {
    console.error('Error fetching historical data:', error);
    res.status(500).json({
      error: true,
      message: 'Error fetching historical data',
      details: error.message
    });
  }
});

// API endpoint to get exchange holidays (XML format)
app.get('/api/exchange-holidays', async (req, res) => {
  try {
    console.log('Fetching exchange holidays...');
    
    const { getHolidayDataPath } = require('./src/utils/config.js');
    const path = require('path');
    const fs = require('fs');
    
    // Try to read the XML file using environment-configured path
    const xmlFilePath = getHolidayDataPath();
    
    if (fs.existsSync(xmlFilePath)) {
      // Read and return the XML file content
      const xmlContent = fs.readFileSync(xmlFilePath, 'utf8');
      res.set('Content-Type', 'text/xml');
      res.send(xmlContent);
      return;
    }

    // If file doesn't exist, try to fetch fresh data
    console.log('No XML file found, fetching fresh data...');
    const xmlContent = await fetch_exchange_holidays();

    if (xmlContent) {
      res.set('Content-Type', 'text/xml');
      res.send(xmlContent);
    } else {
      // Return empty holidays list
      res.set('Content-Type', 'text/xml');
      res.send('<root></root>');
    }
  } catch (error) {
    console.error('Error in exchange holidays endpoint:', error);
    res.status(500).json({
      error: true,
      message: 'Failed to fetch exchange holidays',
      details: error.message
    });
  }
});

// API endpoint to get exchange holidays as JSON array
app.get('/api/exchange-holidays/json', async (req, res) => {
  try {
    console.log('Fetching exchange holidays as JSON...');
    
    // Get holidays using the unified API
    const holidays = getExchangeHolidays();
    
    // Convert dates to ISO string format for JSON transport
    const holidayStrings = holidays.map(date => ({
      date: date.toISOString().split('T')[0], // YYYY-MM-DD format
      timestamp: date.getTime()
    }));
    
    res.json({
      success: true,
      count: holidayStrings.length,
      holidays: holidayStrings
    });
  } catch (error) {
    console.error('Error in exchange holidays JSON endpoint:', error);
    res.status(500).json({
      error: true,
      message: 'Failed to fetch exchange holidays',
      details: error.message
    });
  }
});

// API endpoint to get unique expiry dates
app.get('/api/expiry-dates', async (req, res) => {
  try {
    const client = await pool.connect();
    const query = `
      SELECT DISTINCT expirydate
      FROM INDEX_OPTIONS_TRADES
      ORDER BY expirydate;
    `;
    const result = await client.query(query);

    // Format the dates as YYYY-MM-DD
    const expiryDates = result.rows.map(row => {
      const date = row.expirydate;
      if (date instanceof Date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      } else if (typeof date === 'string') {
        // If it's already a string, check if it's in ISO format
        if (date.includes('T')) {
          // It's an ISO string, extract just the date part
          return date.split('T')[0];
        } else {
          // It's already a date string, use it directly
          return date;
        }
      }
      return null;
    }).filter(date => date !== null);

    client.release();

    res.status(200).json({ expiryDates });
  } catch (error) {
    console.error('Error fetching expiry dates:', error);
    res.status(500).json({
      error: true,
      message: 'Error fetching expiry dates',
      details: error.message
    });
  }
});

// API endpoint to get trades filtered by expiry date
app.get('/api/trades-by-expiry/:expiryDate', async (req, res) => {
  try {
    const { expiryDate } = req.params;

    if (!expiryDate) {
      return res.status(400).json({
        error: true,
        message: 'Expiry date parameter is required'
      });
    }

    const client = await pool.connect();
    const query = `
      SELECT id, quantity, call_put, strike, expirydate, premium, debit_credit, created_date
      FROM INDEX_OPTIONS_TRADES
      WHERE expirydate = $1
      ORDER BY strike;
    `;
    const result = await client.query(query, [expiryDate]);

    // Process the results to ensure proper data types
    const trades = result.rows.map(trade => {
      // Format the date as YYYY-MM-DD
      let formattedDate = null;
      if (trade.expirydate) {
        if (trade.expirydate instanceof Date) {
          const year = trade.expirydate.getFullYear();
          const month = String(trade.expirydate.getMonth() + 1).padStart(2, '0');
          const day = String(trade.expirydate.getDate()).padStart(2, '0');
          formattedDate = `${year}-${month}-${day}`;
        } else if (typeof trade.expirydate === 'string') {
          if (trade.expirydate.includes('T')) {
            formattedDate = trade.expirydate.split('T')[0];
          } else {
            formattedDate = trade.expirydate;
          }
        }
      }

      return {
        id: parseInt(trade.id),
        quantity: parseInt(trade.quantity),
        type: trade.call_put.trim(), // Trim to remove any padding from CHAR(5)
        strike: parseFloat(trade.strike),
        ExpiryDate: formattedDate,
        premium: parseFloat(trade.premium),
        debitCredit: parseFloat(trade.debit_credit),
        created_at: trade.created_date
      };
    });

    client.release();

    res.status(200).json({ trades });
  } catch (error) {
    console.error('Error fetching trades by expiry date:', error);
    res.status(500).json({
      error: true,
      message: 'Error fetching trades by expiry date',
      details: error.message
    });
  }
});

// This endpoint has been replaced by the implementation below
// that handles filtering by stock and expiry date

// API endpoint to get unique expiry dates from Firestore for a specific stock
app.get('/api/firebase-expiry-dates', async (req, res) => {
  console.log('=== RECEIVED REQUEST FOR /api/firebase-expiry-dates ===');
  try {
    const { stock } = req.query;
    console.log('Query parameter stock:', stock);

    // Check if stock parameter is provided and valid
    if (!stock) {
      console.error('ERROR: Missing stock parameter');
      return res.status(400).json({
        error: true,
        message: 'Stock parameter is required'
      });
    }

    // Ensure stock parameter is at least 3 characters
    if (stock.length < 3) {
      console.error(`ERROR: Stock parameter too short: "${stock}"`);
      return res.status(400).json({
        error: true,
        message: 'Stock parameter must be at least 3 characters'
      });
    }

    console.log(`Processing request for stock: "${stock}" (will use first 3 chars: "${stock.substring(0, 3)}")`);

    // Check if Firestore is initialized
    if (!db) {
      console.error('ERROR: Firestore database is not initialized');
      return res.status(500).json({
        error: true,
        message: 'Firestore database is not initialized',
      });
    }
    console.log('Firestore database is initialized');

    // Get all trades from the 'trades' collection
    const tradesSnapshot = await db.collection('trades').get();

    if (tradesSnapshot.empty) {
      console.log('No trades found in Firestore');
      return res.status(200).json({ expiryDates: [] });
    }

    // Use a Map to store expiry dates and count of valid trades for each
    const expiryDatesMap = new Map();

    console.log(`Filtering expiry dates for stock: "${stock}"`);

    // First pass: count valid trades for each expiry date
    console.log(`\n=== PROCESSING TRADES FOR STOCK: "${stock}" ===`);

    // First, extract all unique expiry dates to initialize the map
    const allExpiryDates = new Set();
    tradesSnapshot.forEach(doc => {
      const trade = doc.data();
      // Check both old and new field names
      const expiryValue = trade.expiryDate || trade.expiry;
      if (expiryValue) {
        allExpiryDates.add(expiryValue);
      }
    });

    // Initialize the map with all expiry dates set to 0 count
    allExpiryDates.forEach(expiryDate => {
      expiryDatesMap.set(expiryDate, 0);
    });

    console.log(`Found ${allExpiryDates.size} unique expiry dates in the database:`, [...allExpiryDates].sort());

    // Flag to track if we found any trades for the requested stock
    let hasTradesForRequestedStock = false;

    // Process each trade
    tradesSnapshot.forEach(doc => {
      const trade = doc.data();

      // Only include trades that match the exact stock code AND are Call or Put options
      const expiryValue = trade.expiryDate || trade.expiry;
      if (expiryValue && trade.ticker) {
        const tradeType = (trade.type || '').toLowerCase();
        const isValidType = tradeType === 'call' || tradeType === 'put' || tradeType === 'future';

        // Only process valid trade types
        if (isValidType) {
          const tradeStockCode = trade.ticker.substring(0, 3);
          const stockToCompare = stock.substring(0, 3); // Ensure we only use first 3 chars

          // IMPORTANT: Only count trades where the ticker prefix EXACTLY matches the requested stock code
          // This ensures we only return expiry dates that have trades for the exact requested stock
          if (tradeStockCode === stockToCompare) {
            console.log(`MATCH: ${tradeType.toUpperCase()} ${tradeType === 'future' ? 'trade' : 'option'} for ${trade.ticker} matches requested stock: "${stockToCompare}"`);

            // Set the flag to indicate that we found at least one trade for the requested stock
            hasTradesForRequestedStock = true;

            // Increment the count for this expiry date
            const currentCount = expiryDatesMap.get(expiryValue) || 0;
            expiryDatesMap.set(expiryValue, currentCount + 1);
          } else {
            // DO NOT count trades with mismatched ticker prefixes
            // This is the key fix - we don't want to include expiry dates that only have trades for other stocks
          }
        } else {
           console.log(`Skipping invalid trade type: ${trade.ticker} (type: ${trade.type})`);
        }
      }
    });

    // Check if we found any trades for the requested stock
    if (!hasTradesForRequestedStock) {
      console.log(`\n⚠️ NO TRADES FOUND FOR STOCK: "${stock}"`);
      console.log(`Returning empty array of expiry dates`);
      return res.status(200).json({ expiryDates: [] });
    }

    // Filter out expiry dates with no valid trades
    console.log(`\n=== FILTERING EXPIRY DATES FOR STOCK: "${stock}" ===`);

    const validExpiryDates = [];
    expiryDatesMap.forEach((count, expiryDate) => {
      if (count > 0) {
        validExpiryDates.push(expiryDate);
        console.log(`VALID EXPIRY DATE: "${expiryDate}" has ${count} valid Call/Put/Future trades for stock ${stock} - KEEPING`);
      } else {
        console.log(`INVALID EXPIRY DATE: "${expiryDate}" has NO valid Call/Put/Future trades for stock ${stock} - EXCLUDING`);
      }
    });

    // Sort the valid expiry dates
    const expiryDates = validExpiryDates.sort();

    console.log(`\nFinal expiry dates for stock ${stock}:`, expiryDates);

    return res.status(200).json({ expiryDates });
  } catch (error) {
    console.error('ERROR fetching expiry dates from Firestore:', error);
    return res.status(500).json({
      error: true,
      message: 'Error fetching expiry dates',
      details: error.message
    });
  }
});

// API endpoint to get unique stocks from Firestore
app.get('/api/firebase-stocks', async (_, res) => {
  console.log('Received request for /api/firebase-stocks');
  try {
    // Check if Firestore is initialized
    if (!db) {
      console.error('Firestore database is not initialized');
      return res.status(500).json({
        error: true,
        message: 'Firestore database is not initialized',
      });
    }
    console.log('Firestore database is initialized');

    // Get all trades from the 'trades' collection
    const tradesSnapshot = await db.collection('trades').get();

    if (tradesSnapshot.empty) {
      return res.status(200).json({ stocks: [] });
    }

    // Extract unique stocks (first 3 characters of ticker)
    const stocksSet = new Set();

    tradesSnapshot.forEach(doc => {
      const trade = doc.data();
      if (trade.ticker && trade.ticker.length >= 3) {
        // Extract exactly the first 3 characters as the stock code
        const stock = trade.ticker.substring(0, 3);
        stocksSet.add(stock);
      }
    });

    // Convert Set to Array and sort
    const stocks = [...stocksSet].sort();
    console.log('Found unique stocks:', stocks);

    return res.status(200).json({ stocks });
  } catch (error) {
    console.error('Error fetching stocks from Firestore:', error);
    return res.status(500).json({
      error: true,
      message: 'Error fetching stocks',
      details: error.message
    });
  }
});

// API endpoint to get trades filtered by stock and expiry date from Firestore
app.get('/api/firebase-trades-by-expiry', async (req, res) => {
  console.log('Received request for /api/firebase-trades-by-expiry');
  try {
    const { expiryDate, stock } = req.query;
    console.log('Query parameters - expiryDate:', expiryDate, 'stock:', stock);

    if (!expiryDate) {
      console.error('Missing expiryDate parameter');
      return res.status(400).json({
        error: true,
        message: 'Expiry date parameter is required'
      });
    }

    if (!stock) {
      console.error('Missing stock parameter');
      return res.status(400).json({
        error: true,
        message: 'Stock parameter is required'
      });
    }

    // Ensure stock parameter is at least 3 characters
    if (stock.length < 3) {
      console.error(`Stock parameter too short: "${stock}"`);
      return res.status(400).json({
        error: true,
        message: 'Stock parameter must be at least 3 characters'
      });
    }

    console.log(`Processing trades request for stock: "${stock}" (will use first 3 chars: "${stock.substring(0, 3)}")`);


    // Check if Firestore is initialized
    if (!db) {
      console.error('Firestore database is not initialized');
      return res.status(500).json({
        error: true,
        message: 'Firestore database is not initialized',
      });
    }

    // Query trades by expiry date - check both old and new field names
    let tradesSnapshot;
    try {
      // First try with the new field name
      let tradesQuery = db.collection('trades').where('expiryDate', '==', expiryDate);
      tradesSnapshot = await tradesQuery.get();

      // If no results, try with the old field name
      if (tradesSnapshot.empty) {
        tradesQuery = db.collection('trades').where('expiry', '==', expiryDate);
        tradesSnapshot = await tradesQuery.get();
      }
    } catch (error) {
      console.error('Error querying trades:', error);
      // Fallback to old field name if there's an error
      let tradesQuery = db.collection('trades').where('expiry', '==', expiryDate);
      tradesSnapshot = await tradesQuery.get();
    }

    if (tradesSnapshot.empty) {
      return res.status(200).json({ trades: [] });
    }

    // Process the results to match the expected format in the frontend
    // Filter by stock with detailed logging
    // console.log(`Filtering trades for stock code: "${stock}" (exactly 3 characters) and expiry date: "${expiryDate}"`);

    // First, log all the tickers we're about to filter
    const allTickers = tradesSnapshot.docs.map(doc => doc.data().ticker).filter(ticker => ticker);
    // console.log(`All tickers before filtering (up to 10): ${allTickers.slice(0, 10).join(', ')}`);

    // Now filter and log the process
    const filteredDocs = tradesSnapshot.docs.filter(doc => {
      const trade = doc.data();
      if (!trade.ticker) {
        console.log(`Skipping trade with no ticker`);
        addLog(`Skipping trade with no ticker`); // Add to server logs
        return false;
      }

      // Check if the trade is a Call, Put, or Future (case-insensitive)
      const tradeType = (trade.type || '').toLowerCase();
      const isValidType = tradeType === 'call' || tradeType === 'put' || tradeType === 'future';

      // Make sure we're comparing exactly 3 characters
      const tradeStockCode = trade.ticker.substring(0, 3);
      const stockToCompare = stock.substring(0, 3); // Ensure we only use first 3 chars of stock parameter

      // Case-sensitive exact match for both stock and expiry date
      const isStockMatch = tradeStockCode === stockToCompare;
      // Check both old and new field names for expiry date
      const isExpiryMatch = (trade.expiryDate === expiryDate) || (trade.expiry === expiryDate);
      const isMatch = isStockMatch && isExpiryMatch;

      // Debug log for all trades and filter conditions
      const tradeExpiryValue = trade.expiryDate || trade.expiry || 'N/A';
      // console.log(`TRADE CHECK: Ticker: ${trade.ticker}, Type: "${trade.type}", Expiry: ${tradeExpiryValue}, isStockMatch: ${isStockMatch}, isExpiryMatch: ${isExpiryMatch}, isMatch: ${isMatch}`);
      // addLog(`TRADE CHECK: Ticker: ${trade.ticker}, Type: "${trade.type}", Expiry: ${tradeExpiryValue}, isStockMatch: ${isStockMatch}, isExpiryMatch: ${isExpiryMatch}, isMatch: ${isMatch}`); // Add to server logs


      if (!isValidType) {
        console.log(`TYPE MISMATCH: Trade type "${trade.type}" is not a Call, Put, or Future - skipping`);
        addLog(`TYPE MISMATCH: Trade type "${trade.type}" is not a Call, Put, or Future - skipping`); // Add to server logs
        return false;
      }


      // if (!isMatch) {
      //   if (!isStockMatch) {
      //     console.log(`STOCK MISMATCH: Trade ticker ${trade.ticker} (stock code: "${tradeStockCode}") does NOT match requested stock: "${stockToCompare}"`);
      //     addLog(`STOCK MISMATCH: Trade ticker ${trade.ticker} (stock code: "${tradeStockCode}") does NOT match requested stock: "${stockToCompare}"`); // Add to server logs
      //   }
      //   if (!isExpiryMatch) {
      //     console.log(`EXPIRY MISMATCH: Trade expiry ${trade.expiry} does NOT match requested expiry: "${expiryDate}"`);
      //     addLog(`EXPIRY MISMATCH: Trade expiry ${trade.expiry} does NOT match requested expiry: "${expiryDate}"`); // Add to server logs
      //   }
      // } else {
      //   console.log(`MATCH: ${tradeType.toUpperCase()} ${tradeType === 'future' ? 'trade' : 'option'} for ${trade.ticker} (stock code: "${tradeStockCode}") and expiry ${trade.expiry} MATCH requested stock: "${stockToCompare}" and expiry: "${expiryDate}"`);
      //   addLog(`MATCH: ${tradeType.toUpperCase()} ${tradeType === 'future' ? 'trade' : 'option'} for ${trade.ticker} (stock code: "${tradeStockCode}") and expiry ${trade.expiry} MATCH requested stock: "${stockToCompare}" and expiry: "${expiryDate}"`); // Add to server logs
      // }

      return isMatch;
    });

    console.log(`After filtering: ${filteredDocs.length} trades match stock code "${stock}"`);

    const trades = filteredDocs.map(doc => {
      const trade = doc.data();

      // Map Firestore fields to the expected frontend format
      // Special handling for futures trades
      const tradeType = (trade.type || '').toLowerCase();
      const isFuture = tradeType === 'future';

      // Handle strike field - keep 'N/A' as is for futures
      let strikeValue = trade.strike;
      if (strikeValue !== 'N/A' && !isFuture) {
        strikeValue = parseFloat(strikeValue) || 0;
      }

      // Log the trade for debugging
      console.log('Processing trade:', {
        id: doc.id,
        type: trade.type,
        ticker: trade.ticker,
        strike: strikeValue,
        isFuture
      });

      // Extract stock from ticker using getUnderlyingSymbol if not already present
      const stockFromTicker = trade.ticker ? getUnderlyingSymbol(trade.ticker) : '';
      const stockField = trade.stock || stockFromTicker;

      return {
        id: doc.id,
        quantity: trade.quantity || 0,
        type: trade.type || '',  // 'Call', 'Put', or 'Future'
        strike: strikeValue,
        ExpiryDate: trade.expiryDate || trade.expiry || '',
        premium: trade.cost || trade.price || 0,
        debitCredit: (trade.quantity || 0) * (trade.cost || trade.price || 0),
        created_at: trade.uploadedAt ? trade.uploadedAt.toDate().toISOString() : new Date().toISOString(),
        ticker: trade.ticker || '',
        originalLine: trade.originalLine || '',
        stock: stockField // Add stock field using getUnderlyingSymbol
      };
    });

    console.log(`Found ${trades.length} trades for stock ${stock} and expiry ${expiryDate}`);
    if (trades.length > 0) {
      console.log('Sample trade tickers:', trades.slice(0, 5).map(t => t.ticker));
    }

    return res.status(200).json({ trades });
  } catch (error) {
    console.error('Error fetching trades by expiry date from Firestore:', error);
    return res.status(500).json({
      error: true,
      message: 'Error fetching trades by expiry date',
      details: error.message
    });
  }
});

// API endpoint to get all trades from Firestore
app.get('/api/firebase-all-trades', async (req, res) => {
  console.log('Received request for /api/firebase-all-trades');
  try {
    // Check if Firestore is initialized
    if (!db) {
      console.error('Firestore database is not initialized');
      return res.status(500).json({
        error: true,
        message: 'Firestore database is not initialized',
      });
    }
    console.log('Firestore database is initialized');

    // Get all trades from the 'trades' collection
    console.log('Querying Firestore trades collection...');
    const tradesSnapshot = await db.collection('trades').get();
    console.log(`Firestore query completed. Found ${tradesSnapshot.size} documents.`);

    if (tradesSnapshot.empty) {
      console.log('No trades found in Firestore');
      return res.status(200).json({ trades: [] });
    }

    console.log(`Processing ${tradesSnapshot.size} trades from Firestore...`);

    // Process the results to match the expected format in the frontend
    const trades = [];

    tradesSnapshot.forEach(doc => {
      const trade = doc.data();

      // Check if the trade is a Call, Put, or Future (case-insensitive)
      const tradeType = (trade.type || '').toLowerCase();
      const isValidType = tradeType === 'call' || tradeType === 'put' || tradeType === 'future';

      if (!isValidType) {
        console.log(`TYPE MISMATCH: Trade type "${trade.type}" is not a Call, Put, or Future - skipping`);
        return;
      }

      // Special handling for futures trades
      const isFuture = tradeType === 'future';

      // Handle strike field - keep 'N/A' as is for futures
      let strikeValue = trade.strike;
      if (strikeValue !== 'N/A' && !isFuture) {
        strikeValue = parseFloat(strikeValue) || 0;
      }

      // Add special handling for futures with short tickers
      if (trade.ticker && trade.ticker.length <= 5) {
        strikeValue = 'N/A';
      }

      // Extract stock from ticker (first 3 characters) if not already present in the trade
      const stockFromTicker = trade.ticker ? trade.ticker.substring(0, 3) : '';
      const stockField = trade.stock || stockFromTicker;

      console.log(`Processing trade ${doc.id}: ticker=${trade.ticker}, extracted stock=${stockFromTicker}, trade.stock=${trade.stock}, using stock=${stockField}`);

      trades.push({
        id: doc.id,
        quantity: trade.quantity || 0,
        type: trade.type || '',
        strike: strikeValue,
        ExpiryDate: trade.expiryDate || trade.expiry || '',
        premium: trade.cost || trade.price || 0,
        debitCredit: (trade.quantity || 0) * (trade.cost || trade.price || 0),
        created_at: trade.uploadedAt ? trade.uploadedAt.toDate().toISOString() : new Date().toISOString(),
        ticker: trade.ticker || '',
        originalLine: trade.originalLine || '',
        stock: stockField // Add stock field (first 3 characters of ticker or from trade.stock)
      });
    });

    console.log(`Found ${trades.length} total trades`);

    // Log futures trades for debugging
    const futureTrades = trades.filter(t =>
      t.type === 'Future' ||
      t.type === 'future' ||
      t.strike === 'N/A' ||
      (t.ticker && t.ticker.length <= 5)
    );
    console.log(`Found ${futureTrades.length} futures trades`);

    // Log stock distribution
    const stockCounts = {};
    trades.forEach(trade => {
      const stock = trade.stock || 'unknown';
      stockCounts[stock] = (stockCounts[stock] || 0) + 1;
    });
    console.log('Stock distribution in trades:');
    Object.entries(stockCounts).forEach(([stock, count]) => {
      console.log(`  ${stock}: ${count} trades`);
    });

    // Log sample trades for debugging
    if (trades.length > 0) {
      console.log('Sample trades (first 3):');
      trades.slice(0, 3).forEach((trade, index) => {
        console.log(`Trade ${index + 1}:`, {
          id: trade.id,
          ticker: trade.ticker,
          stock: trade.stock,
          type: trade.type,
          ExpiryDate: trade.ExpiryDate
        });
      });
    }

    return res.status(200).json({ trades });
  } catch (error) {
    console.error('Error fetching all trades from Firestore:', error);
    return res.status(500).json({
      error: true,
      message: 'Error fetching trades from Firestore',
      details: error.message
    });
  }
});

// API endpoint to add a trade to Firestore
app.post('/api/firebase-add-trade', async (req, res) => {
  console.log('Received request for /api/firebase-add-trade');
  console.log('Request body:', req.body);

  try {
    // Check if Firestore is initialized
    if (!db) {
      console.error('Firestore database is not initialized');
      return res.status(500).json({
        error: true,
        message: 'Firestore database is not initialized',
      });
    }

    // Validate required fields
    const { ticker, type, expiry, expiryDate, strike, quantity, price, cost } = req.body;

    if (!ticker) {
      return res.status(400).json({ error: true, message: 'Ticker is required' });
    }

    if (!type) {
      return res.status(400).json({ error: true, message: 'Type is required' });
    }

    if (!expiryDate && !expiry) {
      return res.status(400).json({ error: true, message: 'ExpiryDate is required' });
    }

    if (strike === undefined) {
      return res.status(400).json({ error: true, message: 'Strike is required' });
    }

    if (quantity === undefined) {
      return res.status(400).json({ error: true, message: 'Quantity is required' });
    }

    if (cost === undefined && price === undefined) {
      return res.status(400).json({ error: true, message: 'Cost is required' });
    }

    // Extract stock from ticker (first 3 characters) if not provided
    const stock = req.body.stock || ticker.substring(0, 3);

    // Prepare the trade data
    const tradeData = {
      ticker,
      type,
      expiryDate: expiryDate || expiry,
      strike,
      quantity,
      cost: cost || price,
      stock, // Add stock field (first 3 characters of ticker)
      uploadedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    console.log('Adding trade to Firestore:', tradeData);

    // Add the trade to Firestore
    const docRef = await db.collection('trades').add(tradeData);

    console.log('Trade added with ID:', docRef.id);

    return res.status(200).json({
      success: true,
      message: 'Trade added successfully',
      tradeId: docRef.id
    });
  } catch (error) {
    console.error('Error adding trade to Firestore:', error);
    return res.status(500).json({
      error: true,
      message: 'Error adding trade to Firestore',
      details: error.message
    });
  }
});

// Store server logs for debugging
const serverLogs = [];

// Function to add a log entry
const addLog = (message) => {
  const timestamp = new Date().toISOString();
  const logEntry = `${timestamp}: ${message}`;
  console.log(logEntry);
  serverLogs.push(logEntry);

  // Keep only the last 100 log entries
  if (serverLogs.length > 100) {
    serverLogs.shift();
  }
};

// API endpoint to get server logs
app.get('/api/server-logs', (_, res) => {
  res.json({ logs: serverLogs.join('\n') });
});

// API endpoint to get raw Firestore data
app.get('/api/firebase-raw-data', async (_, res) => {
  try {
    // Check if Firestore is initialized
    if (!db) {
      return res.status(500).json({
        error: true,
        message: 'Firestore database is not initialized',
      });
    }

    addLog('Fetching raw Firestore data');

    // Get all trades from the 'trades' collection
    const snapshot = await db.collection('trades').get();

    if (snapshot.empty) {
      addLog('No trades found in Firestore');
      return res.status(200).json({ trades: [] });
    }

    const trades = [];

    snapshot.forEach(doc => {
      const trade = doc.data();

      // Add the document ID
      trades.push({
        id: doc.id,
        ...trade,
        // Convert Firestore timestamp to ISO string
        uploadedAt: trade.uploadedAt ? trade.uploadedAt.toDate().toISOString() : null
      });
    });

    addLog(`Found ${trades.length} trades in Firestore`);

    // Check for future trades
    const futureTrades = trades.filter(trade =>
      trade.type === 'Future' ||
      trade.type === 'future' ||
      trade.strike === 'N/A' ||
      (trade.ticker && trade.ticker.length <= 5)
    );

    addLog(`Found ${futureTrades.length} future trades in Firestore`);

    // Check for HTIM5 ticker
    const htim5Trades = trades.filter(trade => trade.ticker === 'HTIM5');

    addLog(`Found ${htim5Trades.length} trades with ticker HTIM5 in Firestore`);

    // Check for HTI trades with expiry 2025-06
    const htiTrades = trades.filter(trade =>
      trade.ticker &&
      trade.ticker.substring(0, 3) === 'HTI' &&
      trade.expiry === '2025-06'
    );

    addLog(`Found ${htiTrades.length} HTI trades with expiry 2025-06 in Firestore`);

    return res.status(200).json({ trades });
  } catch (error) {
    addLog(`Error fetching raw Firestore data: ${error.message}`);
    return res.status(500).json({
      error: true,
      message: 'Error fetching raw Firestore data',
      details: error.message
    });
  }
});



// API endpoint to parse trades
app.post('/api/parse-trades', async (req, res) => {
  console.log('Received request for /api/parse-trades');

  // Set the response content type explicitly
  res.setHeader('Content-Type', 'application/json');

  try {
    // Ensure we have a body
    if (!req.body) {
      console.error('No request body received');
      return res.status(400).json({
        error: true,
        message: 'Request body is required'
      });
    }

    const { rawText } = req.body;

    if (!rawText) {
      console.error('No raw text received in request body');
      return res.status(400).json({
        error: true,
        message: 'Raw text is required'
      });
    }

    // Trim the raw text and check if it's empty
    const trimmedText = String(rawText).trim();
    if (trimmedText === '') {
      console.error('Raw text is empty after trimming');
      return res.status(400).json({
        error: true,
        message: 'Raw text cannot be empty'
      });
    }

    console.log(`Received raw text of length ${trimmedText.length}`);
    console.log('First 100 chars:', trimmedText.substring(0, 100) + (trimmedText.length > 100 ? '...' : ''));

    console.log('About to parse trades with text:', trimmedText);

    // Parse the raw text into trade objects
    console.log('About to call parseTrades with text:', trimmedText);

    // Log the test case but don't force the strike anymore - our improved parser should handle it correctly
    if (trimmedText.includes('HHI7200R5') && trimmedText.includes('7200 Put')) {
      console.log('*** DETECTED TEST CASE - USING IMPROVED PARSER ***');
    }

    const trades = parseTrades(trimmedText);

    console.log(`Parsed ${trades.length} trades`);

    // Log a sample trade for debugging
    if (trades.length > 0) {
      console.log('Sample parsed trade:', JSON.stringify(trades[0]));
    }

    // Return a JSON response
    return res.status(200).json({
      success: true,
      trades
    });
  } catch (error) {
    console.error('Error parsing trades:', error);
    console.error('Error stack:', error.stack);

    // Return a JSON error response instead of letting Express handle it
    return res.status(500).json({
      error: true,
      message: 'Error parsing trades',
      details: error.message || 'Unknown error'
    });
  }
});

// Duplicate endpoint removed - using the properly mapped version above (lines 1022-1142)

// API endpoint to upload multiple trades to Firestore
app.post('/api/upload-trades', async (req, res) => {
  console.log('Received request for /api/upload-trades');

  // Set the response content type explicitly
  res.setHeader('Content-Type', 'application/json');

  try {
    const { trades } = req.body;

    if (!trades || !Array.isArray(trades) || trades.length === 0) {
      return res.status(400).json({
        error: true,
        message: 'Trades array is required and must not be empty'
      });
    }

    console.log(`Uploading ${trades.length} trades to Firestore`);

    // Check if Firestore is initialized
    if (!db) {
      console.error('Firestore database is not initialized');
      return res.status(500).json({
        error: true,
        message: 'Firestore database is not initialized',
      });
    }

    // Create a batch to upload multiple trades at once
    const batch = db.batch();

    // Add each trade to the batch
    trades.forEach(trade => {
      // Create a new document reference with auto-generated ID
      const docRef = db.collection('trades').doc();

      // Prepare the trade data according to the ParsedTrade interface
      const tradeData = {
        ticker: trade.ticker,
        stock: trade.stock || getUnderlyingSymbol(trade.ticker),
        type: trade.type || 'Unknown',
        expiryDate: trade.expiryDate || trade.expiry || '',
        strike: typeof trade.strike === 'string' ? trade.strike : String(trade.strike || ''),
        quantity: Number(trade.quantity) || 0,
        cost: Number(trade.cost) || Number(trade.price) || 0,
        originalLine: trade.originalLine || '',
        uploadedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      // Ensure stock field is always set (first 3 characters of ticker)
      if (!tradeData.stock && tradeData.ticker) {
        tradeData.stock = tradeData.ticker.substring(0, 3);
      }

      // Add the trade to the batch
      batch.set(docRef, tradeData);
    });

    // Commit the batch
    await batch.commit();

    console.log(`Successfully uploaded ${trades.length} trades to Firestore`);

    return res.status(200).json({
      success: true,
      message: `Successfully uploaded ${trades.length} trades to Firestore`,
      count: trades.length
    });
  } catch (error) {
    console.error('Error uploading trades to Firestore:', error);
    return res.status(500).json({
      error: true,
      message: 'Error uploading trades to Firestore',
      details: error.message
    });
  }
});

// Strategy Management API Endpoints

// Get all strategies
app.get('/api/strategies', async (req, res) => {
  console.log('Received request for /api/strategies');
  try {
    if (!db) {
      return res.status(500).json({ error: 'Firestore database is not initialized' });
    }

    const strategiesRef = db.collection('strategies');
    const snapshot = await strategiesRef.orderBy('createdAt', 'desc').get();

    const strategies = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      strategies.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        lastModified: data.lastModified?.toDate() || new Date()
      });
    });

    console.log(`Found ${strategies.length} strategies`);
    res.json(strategies);
  } catch (error) {
    console.error('Error fetching strategies:', error);
    res.status(500).json({ error: 'Failed to fetch strategies' });
  }
});

// Create new strategy
app.post('/api/strategies', async (req, res) => {
  console.log('Received request for POST /api/strategies');
  console.log('Request body:', req.body);

  try {
    if (!db) {
      return res.status(500).json({ error: 'Firestore database is not initialized' });
    }

    const { id, name, stockSymbol, description } = req.body;

    if (!id || !name || !stockSymbol) {
      return res.status(400).json({ error: 'Missing required fields: id, name, stockSymbol' });
    }

    const strategyData = {
      name: name.trim(),
      stockSymbol: stockSymbol.trim().toUpperCase(),
      description: description?.trim() || '',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      lastModified: admin.firestore.FieldValue.serverTimestamp()
    };

    const strategiesRef = db.collection('strategies');
    await strategiesRef.doc(id).set(strategyData);

    // Return the created strategy with proper date formatting
    const createdStrategy = {
      id,
      ...strategyData,
      createdAt: new Date(),
      lastModified: new Date()
    };

    console.log('Strategy created successfully:', id);
    res.status(201).json(createdStrategy);
  } catch (error) {
    console.error('Error creating strategy:', error);
    res.status(500).json({ error: 'Failed to create strategy' });
  }
});

// Update strategy
app.put('/api/strategies/:id', async (req, res) => {
  console.log('Received request for PUT /api/strategies/:id');
  console.log('Strategy ID:', req.params.id);
  console.log('Request body:', req.body);

  try {
    if (!db) {
      return res.status(500).json({ error: 'Firestore database is not initialized' });
    }

    const strategyId = req.params.id;
    const updates = req.body;

    // Remove id from updates if present
    delete updates.id;
    delete updates.createdAt;

    // Add lastModified timestamp
    updates.lastModified = admin.firestore.FieldValue.serverTimestamp();

    const strategyRef = db.collection('strategies').doc(strategyId);
    const doc = await strategyRef.get();

    if (!doc.exists) {
      return res.status(404).json({ error: 'Strategy not found' });
    }

    await strategyRef.update(updates);

    // Get the updated strategy
    const updatedDoc = await strategyRef.get();
    const updatedData = updatedDoc.data();

    const updatedStrategy = {
      id: strategyId,
      ...updatedData,
      createdAt: updatedData.createdAt?.toDate() || new Date(),
      lastModified: new Date()
    };

    console.log('Strategy updated successfully:', strategyId);
    res.json(updatedStrategy);
  } catch (error) {
    console.error('Error updating strategy:', error);
    res.status(500).json({ error: 'Failed to update strategy' });
  }
});

// Delete strategy
app.delete('/api/strategies/:id', async (req, res) => {
  console.log('Received request for DELETE /api/strategies/:id');
  console.log('Strategy ID:', req.params.id);

  try {
    if (!db) {
      return res.status(500).json({ error: 'Firestore database is not initialized' });
    }

    const strategyId = req.params.id;
    const strategyRef = db.collection('strategies').doc(strategyId);
    const doc = await strategyRef.get();

    if (!doc.exists) {
      return res.status(404).json({ error: 'Strategy not found' });
    }

    await strategyRef.delete();

    console.log('Strategy deleted successfully:', strategyId);
    res.json({ message: 'Strategy deleted successfully' });
  } catch (error) {
    console.error('Error deleting strategy:', error);
    res.status(500).json({ error: 'Failed to delete strategy' });
  }
});

// Get single strategy
app.get('/api/strategies/:id', async (req, res) => {
  console.log('Received request for GET /api/strategies/:id');
  console.log('Strategy ID:', req.params.id);

  try {
    if (!db) {
      return res.status(500).json({ error: 'Firestore database is not initialized' });
    }

    const strategyId = req.params.id;
    const strategyRef = db.collection('strategies').doc(strategyId);
    const doc = await strategyRef.get();

    if (!doc.exists) {
      return res.status(404).json({ error: 'Strategy not found' });
    }

    const data = doc.data();
    const strategy = {
      id: doc.id,
      ...data,
      createdAt: data.createdAt?.toDate() || new Date(),
      lastModified: data.lastModified?.toDate() || new Date()
    };

    console.log('Strategy found:', strategy.id);
    res.json(strategy);
  } catch (error) {
    console.error('Error fetching strategy:', error);
    res.status(500).json({ error: 'Failed to fetch strategy' });
  }
});

// Add trade to strategy collection
app.post('/api/strategy-trades', async (req, res) => {
  console.log('Received request for POST /api/strategy-trades');
  console.log('Request body:', req.body);

  try {
    if (!db) {
      return res.status(500).json({ error: 'Firestore database is not initialized' });
    }

    const tradeData = req.body;

    if (!tradeData.strategyId) {
      return res.status(400).json({ error: 'Strategy ID is required' });
    }

    // Add timestamp
    const tradeWithTimestamp = {
      ...tradeData,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    };

    console.log('Adding trade to strategy collection:', tradeWithTimestamp);

    // Add the trade to strategy-trades collection
    const docRef = await db.collection('strategy-trades').add(tradeWithTimestamp);

    console.log('Strategy trade added with ID:', docRef.id);

    res.status(201).json({
      success: true,
      message: 'Trade added to strategy successfully',
      tradeId: docRef.id
    });
  } catch (error) {
    console.error('Error adding strategy trade:', error);
    res.status(500).json({ error: 'Failed to add strategy trade' });
  }
});

// Batch add trades to strategy collection
app.post('/api/strategy-trades/batch', async (req, res) => {
  console.log('Received request for POST /api/strategy-trades/batch');
  console.log('Request body:', req.body);

  try {
    if (!db) {
      return res.status(500).json({ error: 'Firestore database is not initialized' });
    }

    const { strategyId, trades } = req.body;

    if (!strategyId) {
      return res.status(400).json({ error: 'Strategy ID is required' });
    }

    if (!trades || !Array.isArray(trades) || trades.length === 0) {
      return res.status(400).json({ error: 'Trades array is required and must not be empty' });
    }

    console.log(`Batch adding ${trades.length} trades to strategy ${strategyId}`);

    // Create a batch to add multiple trades at once
    const batch = db.batch();
    const tradeIds = [];

    // Add each trade to the batch
    trades.forEach(trade => {
      const docRef = db.collection('strategy-trades').doc();
      tradeIds.push(docRef.id);

      const tradeWithTimestamp = {
        ...trade,
        strategyId,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      };

      batch.set(docRef, tradeWithTimestamp);
    });

    // Commit the batch
    await batch.commit();

    console.log(`Successfully added ${trades.length} trades to strategy ${strategyId}`);

    res.status(201).json({
      success: true,
      message: `Successfully added ${trades.length} trades to strategy`,
      count: trades.length,
      tradeIds
    });
  } catch (error) {
    console.error('Error batch adding strategy trades:', error);
    res.status(500).json({ error: 'Failed to batch add strategy trades' });
  }
});

// Get strategy trades
app.get('/api/strategies/:id/trades', async (req, res) => {
  console.log('Received request for GET /api/strategies/:id/trades');
  console.log('Strategy ID:', req.params.id);

  try {
    if (!db) {
      return res.status(500).json({ error: 'Firestore database is not initialized' });
    }

    const strategyId = req.params.id;
    const tradesRef = db.collection('strategy-trades');
    const snapshot = await tradesRef.where('strategyId', '==', strategyId).get();

    const trades = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      trades.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date()
      });
    });

    console.log(`Found ${trades.length} trades for strategy ${strategyId}`);
    res.json(trades);
  } catch (error) {
    console.error('Error fetching strategy trades:', error);
    res.status(500).json({ error: 'Failed to fetch strategy trades' });
  }
});

// Statement Import API Endpoints

// API endpoint to parse PDF statement
app.post('/api/parse-statement', upload.single('statement'), async (req, res) => {
  console.log('Received request for /api/parse-statement');

  try {
    if (!req.file) {
      return res.status(400).json({
        error: true,
        message: 'No PDF file uploaded'
      });
    }

    const password = req.body.password || null;
    console.log(`Processing PDF statement: ${req.file.originalname}, Size: ${req.file.size} bytes`);
    console.log('Password provided:', password ? 'Yes' : 'No');

    // Parse the PDF statement
    const rawParsedData = await parseStatement(req.file.buffer, password);

    // Sanitize and validate the parsed data
    const sanitizedData = sanitizeStatementData(rawParsedData);
    const validation = validateStatementData(sanitizedData);

    if (!validation.isValid) {
      console.warn('Statement validation failed:', validation.errors);
      return res.status(400).json({
        error: true,
        message: 'Statement data validation failed',
        validationErrors: validation.errors,
        data: sanitizedData // Still return data for user review
      });
    }

    console.log('Statement parsed and validated successfully');
    res.json({
      success: true,
      message: 'Statement parsed successfully',
      data: sanitizedData
    });

  } catch (error) {
    console.error('Error parsing statement:', error);

    // Handle specific PDF parsing errors
    if (error.message.includes('password')) {
      return res.status(400).json({
        error: true,
        message: 'Invalid password for encrypted PDF. Please check the password and try again.'
      });
    }

    if (error.message.includes('Invalid PDF')) {
      return res.status(400).json({
        error: true,
        message: 'Invalid PDF file. Please ensure the file is a valid PDF document.'
      });
    }

    res.status(500).json({
      error: true,
      message: error.message || 'Failed to parse PDF statement'
    });
  }
});

// Removed /api/save-statement-data endpoint to avoid confusion with trade-confirmations saving

// API endpoint to save trade confirmations to database
app.post('/api/save-trade-confirmation', async (req, res) => {
  console.log('Received request for /api/save-trade-confirmation');

  try {
    if (!db) {
      return res.status(500).json({
        error: true,
        message: 'Database is not initialized'
      });
    }

    const { header, tradeConfirmations } = req.body;

    if (!header || !header.accountNumber || !header.statementDate) {
      return res.status(400).json({
        error: true,
        message: 'Account number and statement date are required'
      });
    }

    if (!tradeConfirmations || !Array.isArray(tradeConfirmations) || tradeConfirmations.length === 0) {
      return res.status(400).json({
        error: true,
        message: 'Trade confirmation records are required'
      });
    }

    const { accountNumber, statementDate } = header;

    // Check for duplicate trade confirmations
    const existingConfirmations = await db.collection('trade-confirmations')
      .where('accountNumber', '==', accountNumber)
      .where('statementDate', '==', statementDate)
      .get();

    if (!existingConfirmations.empty) {
      return res.status(409).json({
        error: true,
        message: `Trade confirmations for account ${accountNumber} on ${statementDate} already exist.`
      });
    }

    // Create batch to save all trade confirmations
    const batch = db.batch();
    const savedRecords = [];

    tradeConfirmations.forEach((trade, index) => {
      const docRef = db.collection('trade-confirmations').doc();
      const tradeData = {
        ...trade,
        accountNumber,
        statementDate,
        recordIndex: index,
        importedAt: admin.firestore.FieldValue.serverTimestamp()
      };

      batch.set(docRef, tradeData);
      savedRecords.push({ id: docRef.id, ...tradeData });
    });

    // Commit the batch
    await batch.commit();

    console.log(`Saved ${tradeConfirmations.length} trade confirmation records for account ${accountNumber}`);

    res.status(201).json({
      success: true,
      message: `Successfully saved ${tradeConfirmations.length} trade confirmation records`,
      count: tradeConfirmations.length,
      accountNumber,
      statementDate,
      records: savedRecords
    });

  } catch (error) {
    console.error('Error saving trade confirmations:', error);
    res.status(500).json({
      error: true,
      message: 'Error saving trade confirmations to database',
      details: error.message
    });
  }
});

// Serve static files from the React app in production
if (process.env.NODE_ENV === 'production') {
  const path = require('path');
  app.use(express.static(path.join(__dirname, 'build')));

  // Handle React routing, return all requests to React app
  app.get('*', function(req, res) {
    // Skip API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.sendFile(path.join(__dirname, 'build', 'index.html'));
  });
}

// Start the server
const server = app.listen(port, async () => {
  console.log(`Server running on port ${port}`);
  console.log(`Firebase API endpoints available at:
  - GET /api/firebase-stocks
  - GET /api/firebase-expiry-dates?stock=XXX
  - GET /api/firebase-trades-by-expiry?expiryDate=YYYY-MM-DD&stock=XXX
  - GET /api/firebase-all-trades
  - POST /api/firebase-add-trade
  - POST /api/parse-trades
  - POST /api/upload-trades
  - POST /api/parse-statement
  - POST /api/save-trade-confirmation
  - GET /api/server-logs
  - GET /api/firebase-raw-data
  - GET /api/exchange-holidays`);

  // Initialize exchange holiday data
  console.log('Initializing exchange holiday data...');
  try {
    await initializeHolidayData();
    console.log('Exchange holiday data initialization complete');
  } catch (error) {
    console.error('Failed to initialize exchange holiday data:', error);
  }
});

// Keep the server running
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  server.close(() => {
    console.log('Server shut down');
    process.exit(0);
  });
});
