// Zustand store for PnL Chart Data
import { create } from 'zustand';

const usePnlChartStore = create((set) => ({
  // Data structure: Array of objects, where each object is a trade's P&L data
  // e.g., [{ id: 'trade1', trade: {...}, pnl_5000: 100, pnl_5100: 120, ... }, ...]
  pnlData: [],
  // Price points used for the P&L calculation, e.g., [5000, 5100, 5200]
  pricePoints: [],
  isLoading: true,
  error: null,

  setData: (data, points) => set({ pnlData: data, pricePoints: points, isLoading: false, error: null }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error: error, isLoading: false }),
}));

export default usePnlChartStore;
