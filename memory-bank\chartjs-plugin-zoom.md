# Chart.js Zoom Plugin Documentation

## Overview

The `chartjs-plugin-zoom` is a plugin for Chart.js that enables zooming and panning functionality in charts. It supports mouse wheel zooming, pinch zooming on touch devices, and panning via mouse or touch.

## Installation

```bash
npm install chartjs-plugin-zoom
```

## Integration

### Script Tag

```html
<script src="path/to/chartjs/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/hammerjs@2.0.8"></script>
<script src="path/to/chartjs-plugin-zoom/dist/chartjs-plugin-zoom.min.js"></script>
<script>
    var myChart = new Chart(ctx, {...});
</script>
```

### Bundlers (Webpack, Rollup, etc.)

```javascript
import { Chart } from 'chart.js';
import zoomPlugin from 'chartjs-plugin-zoom';
Chart.register(zoomPlugin);
```

## Basic Usage

```javascript
const config = {
  type: 'line',
  data: {
    labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
    datasets: [{
      label: 'My First Dataset',
      data: [65, 59, 80, 81, 56, 55, 40],
      fill: false,
      borderColor: 'rgb(75, 192, 192)',
      tension: 0.1
    }]
  },
  options: {
    plugins: {
      zoom: {
        zoom: {
          wheel: {
            enabled: true,
          },
          pinch: {
            enabled: true
          },
          mode: 'xy',
        }
      }
    }
  }
};
```

## Configuration Options

### Zoom Options

```javascript
zoom: {
  // Enable zooming
  enabled: true,
  
  // Zooming directions
  mode: 'xy', // 'x', 'y', or 'xy'
  
  // Speed of zoom via mouse wheel
  // Higher values = faster zoom
  speed: 0.1,
  
  // Minimal zoom distance required before triggering
  threshold: 2,
  
  // Mouse wheel configuration
  wheel: {
    enabled: true,
  },
  
  // Pinch configuration (for touch devices)
  pinch: {
    enabled: true,
  },
}
```

### Pan Options

```javascript
pan: {
  // Enable panning
  enabled: true,
  
  // Panning directions
  mode: 'xy', // 'x', 'y', or 'xy'
  
  // Modifiers to use while panning (e.g., hold 'shift' key)
  modifierKey: 'shift',
}
```

### Limits

You can set limits to restrict how far users can zoom in or out:

```javascript
limits: {
  x: {
    min: 'original', // 'original', number, or Date
    max: 'original', // 'original', number, or Date
    minRange: 1      // Minimum allowed range (in data units)
  },
  y: {
    min: 'original',
    max: 'original',
    minRange: 1
  }
}
```

## Methods

The plugin adds methods to the Chart instance:

### Reset Zoom

```javascript
// Reset the zoom to the original state
myChart.resetZoom();
```

### Zoom

```javascript
// Programmatically zoom the chart
myChart.zoom(scaleAmountX, scaleAmountY);
```

### Pan

```javascript
// Programmatically pan the chart
myChart.pan(panAmountX, panAmountY);
```

## Example Implementation

```javascript
import React, { useRef } from 'react';
import { Chart } from 'chart.js';
import zoomPlugin from 'chartjs-plugin-zoom';
import { Line } from 'react-chartjs-2';

Chart.register(zoomPlugin);

function ZoomableChart() {
  const chartRef = useRef(null);
  
  const resetZoom = () => {
    if (chartRef.current) {
      chartRef.current.resetZoom();
    }
  };
  
  const options = {
    plugins: {
      zoom: {
        pan: {
          enabled: true,
          mode: 'x',
        },
        zoom: {
          wheel: {
            enabled: true,
          },
          pinch: {
            enabled: true
          },
          mode: 'x',
        },
        limits: {
          x: {
            min: 'original',
            max: 'original',
          }
        }
      }
    }
  };
  
  return (
    <div>
      <button onClick={resetZoom}>Reset Zoom</button>
      <Line ref={chartRef} data={data} options={options} />
    </div>
  );
}
```

## Resources

- [Official Documentation](https://www.chartjs.org/chartjs-plugin-zoom/latest/guide/)
- [GitHub Repository](https://github.com/chartjs/chartjs-plugin-zoom)
- [Samples](https://www.chartjs.org/chartjs-plugin-zoom/latest/samples/)
