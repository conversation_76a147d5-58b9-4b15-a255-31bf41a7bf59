# Statement Import Database Schema

## Overview
The Statement Import feature uses Firestore collections to store parsed statement data. The schema is designed to prevent duplicate imports and efficiently query statement data by account number and date.

## Collections Structure

### 1. statements (Main Collection)
Stores the complete statement data with metadata.

```javascript
{
  // Document ID: auto-generated
  accountNumber: "string",           // Required - Account number from statement header
  statementDate: "string",           // Required - Statement date (YYYY-MM-DD format)
  importedAt: "timestamp",           // Auto-generated import timestamp
  
  // Statement Header Information
  header: {
    accountNumber: "string",
    statementDate: "string",         // YYYY-MM-DD format
    periodStart: "string",           // YYYY-MM-DD format
    periodEnd: "string",             // YYYY-MM-DD format
    baseCurrency: "string"           // e.g., "HKD", "USD"
  },
  
  // Account Movement Data
  accountMovement: [
    {
      date: "string",                // YYYY-MM-DD format
      description: "string",
      reference: "string",
      debit: "number",
      credit: "number", 
      balance: "number",
      currency: "string"
    }
  ],
  
  // Trade Confirmation Data
  tradeConfirmation: [
    {
      date: "string",                // YYYY-MM-DD format
      instrument: "string",
      buySell: "string",             // "Buy" or "Sell"
      quantity: "number",
      price: "number",
      amount: "number",
      commission: "number",
      currency: "string"
    }
  ],
  
  // Position Closed Data
  positionClosed: [
    {
      instrument: "string",
      openDate: "string",            // YYYY-MM-DD format
      closeDate: "string",           // YYYY-MM-DD format
      quantity: "number",
      openPrice: "number",
      closePrice: "number",
      pnl: "number",
      currency: "string"
    }
  ],
  
  // Open Position Data
  openPosition: [
    {
      instrument: "string",
      position: "string",            // "Long" or "Short"
      quantity: "number",
      averagePrice: "number",
      marketPrice: "number",
      marketValue: "number",
      unrealizedPnl: "number",
      currency: "string"
    }
  ],
  
  // Financial Summary Data
  financialSummary: {
    openingBalance: "number",
    closingBalance: "number",
    netChange: "number",
    totalDeposits: "number",
    totalWithdrawals: "number",
    currency: "string"
  },
  
  // Margin Summary Data
  marginSummary: {
    initialMargin: "number",
    maintenanceMargin: "number",
    availableMargin: "number",
    marginUtilization: "string",     // e.g., "45.2%"
    currency: "string"
  }
}
```

## Indexes

### Composite Indexes
1. **accountNumber + statementDate** (ascending)
   - Purpose: Prevent duplicate imports
   - Query: Check if statement already exists for specific account and date

2. **accountNumber + importedAt** (descending)
   - Purpose: Retrieve statements for an account ordered by import date
   - Query: Get latest statements for an account

3. **statementDate + importedAt** (descending)
   - Purpose: Retrieve statements by date range
   - Query: Get all statements for a specific period

### Single Field Indexes
1. **accountNumber** (ascending)
   - Purpose: Query all statements for a specific account

2. **statementDate** (ascending)
   - Purpose: Query statements by date

3. **importedAt** (descending)
   - Purpose: Query recently imported statements

## Data Validation Rules

### Required Fields
- `accountNumber`: Must be present and non-empty
- `statementDate`: Must be present and in YYYY-MM-DD format
- `header.accountNumber`: Must match top-level accountNumber
- `header.statementDate`: Must match top-level statementDate

### Duplicate Prevention
- Before saving, check if a document exists with the same `accountNumber` and `statementDate`
- If duplicate found, return error with appropriate message

### Data Types
- All dates stored as strings in YYYY-MM-DD format for consistency
- All monetary amounts stored as numbers (not strings)
- Currency codes stored as 3-letter uppercase strings (e.g., "HKD", "USD")

## Query Patterns

### 1. Check for Duplicate Statement
```javascript
db.collection('statements')
  .where('accountNumber', '==', accountNumber)
  .where('statementDate', '==', statementDate)
  .get()
```

### 2. Get All Statements for Account
```javascript
db.collection('statements')
  .where('accountNumber', '==', accountNumber)
  .orderBy('statementDate', 'desc')
  .get()
```

### 3. Get Recent Statements
```javascript
db.collection('statements')
  .orderBy('importedAt', 'desc')
  .limit(10)
  .get()
```

### 4. Get Statements by Date Range
```javascript
db.collection('statements')
  .where('statementDate', '>=', startDate)
  .where('statementDate', '<=', endDate)
  .orderBy('statementDate', 'desc')
  .get()
```

## Security Rules

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /statements/{statementId} {
      // Allow read/write for authenticated users
      allow read, write: if request.auth != null;
      
      // Validate required fields on create/update
      allow create, update: if request.auth != null 
        && resource.data.accountNumber is string
        && resource.data.statementDate is string
        && resource.data.header.accountNumber == resource.data.accountNumber
        && resource.data.header.statementDate == resource.data.statementDate;
    }
  }
}
```

## Migration Considerations

### From Existing Trade Data
- Existing trade data in the `trades` collection remains unchanged
- Statement import provides an additional data source
- Consider creating views or aggregations that combine both data sources

### Future Enhancements
- Add support for multiple account types
- Implement statement comparison features
- Add audit trail for statement modifications
- Support for statement amendments/corrections

## Performance Considerations

### Document Size
- Each statement document may be large due to multiple arrays
- Consider pagination for large statements
- Monitor document size limits (1MB per document)

### Query Optimization
- Use composite indexes for common query patterns
- Limit result sets using `.limit()` for large datasets
- Consider using subcollections for very large statements

### Caching Strategy
- Cache frequently accessed statements in client-side state
- Implement server-side caching for common queries
- Use Firestore offline persistence for mobile apps
