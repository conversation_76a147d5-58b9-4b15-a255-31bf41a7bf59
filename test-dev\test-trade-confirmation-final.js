/**
 * Final optimized test script for extractTradeConfirmation() function
 * Correctly parses the trade data from the PDF statement format
 */

const fs = require('fs');
const path = require('path');
const { parseStatement } = require('../src/server-utils/statement-parser');
const PDFExtract = require('pdf.js-extract').PDFExtract;

/**
 * Helper function to extract a section between two headers (case insensitive)
 */
function extractSectionCaseInsensitive(text, startHeader, endHeader) {
  const startIndex = text.toUpperCase().indexOf(startHeader.toUpperCase());
  if (startIndex === -1) return null;

  let endIndex = text.length;
  if (endHeader) {
    const endHeaderIndex = text.toUpperCase().indexOf(endHeader.toUpperCase(), startIndex);
    if (endHeaderIndex !== -1) {
      endIndex = endHeaderIndex;
    }
  }

  return text.substring(startIndex, endIndex);
}

/**
 * Extract individual trades from the compressed trade data
 */
function extractTrades(tradeText) {
  console.log('🔍 Extracting individual trades from text...');
  
  // Look for the pattern that indicates trade entries
  // Format: DATE # ORDERNO MARKET INSTRUMENT QUANTITY PRICE etc.
  const tradePattern = /(\d{2}\/\d{2}\/\d{4})\s+(#?\s*\d+)\s+([A-Z]+)\s+([A-Z]{1,3})\s+([^0-9]*?)\s+(\d+)\s+([A-Z]{3})\s+([\d,.]+)\s+([A-Z]+)\s+([\d,.]+)\s+([A-Z]{3})\s+([\d,.-]+)\s+([A-Z]{3})\s+([\d,.-]+)\s+([A-Z]{3})/g;
  
  const trades = [];
  let match;
  
  // First, let's find individual trade entries by looking for date patterns
  const datePattern = /\d{2}\/\d{2}\/\d{4}/g;
  const dateMatches = [...tradeText.matchAll(datePattern)];
  
  console.log(`Found ${dateMatches.length} potential trade dates`);
  
  // Extract trades manually by finding patterns between dates
  const lines = tradeText.split(/\s+/);
  let currentTrade = null;
  let tradeCount = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Look for date pattern (DD/MM/YYYY)
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(line)) {
      // If we have a current trade, save it
      if (currentTrade && currentTrade.orderNo) {
        trades.push(currentTrade);
        tradeCount++;
      }
      
      // Start new trade
      currentTrade = {
        tradeNumber: tradeCount + 1,
        date: line,
        orderNo: '',
        market: '',
        instrument: '',
        description: '',
        quantity: '',
        currency: '',
        strikePrice: '',
        type: '',
        premium: '',
        premiumCurrency: '',
        commission: '',
        commissionCurrency: '',
        fees: '',
        feesCurrency: ''
      };
      
      console.log(`\n📈 Processing Trade #${tradeCount + 1}:`);
      console.log(`   Date: ${line}`);
      
      // Look ahead for order number (might start with #)
      if (i + 1 < lines.length) {
        const nextLine = lines[i + 1];
        if (nextLine.startsWith('#') || /^\d+$/.test(nextLine)) {
          currentTrade.orderNo = nextLine;
          i++;
          console.log(`   Order No: ${nextLine}`);
          
          // Look for market (HKFE, etc.)
          if (i + 1 < lines.length && /^[A-Z]{3,5}$/.test(lines[i + 1])) {
            currentTrade.market = lines[i + 1];
            i++;
            console.log(`   Market: ${currentTrade.market}`);
            
            // Look for instrument code
            if (i + 1 < lines.length && /^[A-Z]{1,3}$/.test(lines[i + 1])) {
              currentTrade.instrument = lines[i + 1];
              i++;
              console.log(`   Instrument: ${currentTrade.instrument}`);
              
              // Collect description parts until we hit a number
              let description = [];
              while (i + 1 < lines.length && !/^\d+$/.test(lines[i + 1]) && !/^[\d,.]+$/.test(lines[i + 1])) {
                i++;
                description.push(lines[i]);
              }
              currentTrade.description = description.join(' ');
              console.log(`   Description: ${currentTrade.description}`);
              
              // Look for quantity (number)
              if (i + 1 < lines.length && /^\d+$/.test(lines[i + 1])) {
                currentTrade.quantity = lines[i + 1];
                i++;
                console.log(`   Quantity: ${currentTrade.quantity}`);
                
                // Look for currency
                if (i + 1 < lines.length && /^[A-Z]{3}$/.test(lines[i + 1])) {
                  currentTrade.currency = lines[i + 1];
                  i++;
                  console.log(`   Currency: ${currentTrade.currency}`);
                  
                  // Look for strike price
                  if (i + 1 < lines.length && /^[\d,.]+$/.test(lines[i + 1])) {
                    currentTrade.strikePrice = lines[i + 1];
                    i++;
                    console.log(`   Strike Price: ${currentTrade.strikePrice}`);
                    
                    // Look for option type (CALL/PUT)
                    if (i + 1 < lines.length && /^(CALL|PUT)$/.test(lines[i + 1])) {
                      currentTrade.type = lines[i + 1];
                      i++;
                      console.log(`   Type: ${currentTrade.type}`);
                      
                      // Look for premium
                      if (i + 1 < lines.length && /^[\d,.]+$/.test(lines[i + 1])) {
                        currentTrade.premium = lines[i + 1];
                        i++;
                        console.log(`   Premium: ${currentTrade.premium}`);
                        
                        // Continue parsing fees and commission...
                        // Look for currency
                        if (i + 1 < lines.length && /^[A-Z]{3}$/.test(lines[i + 1])) {
                          currentTrade.premiumCurrency = lines[i + 1];
                          i++;
                          console.log(`   Premium Currency: ${currentTrade.premiumCurrency}`);
                        }
                        
                        // Look for commission (often has - sign)
                        if (i + 1 < lines.length && /^[\d,.-]+$/.test(lines[i + 1])) {
                          currentTrade.commission = lines[i + 1];
                          i++;
                          console.log(`   Commission: ${currentTrade.commission}`);
                          
                          // Look for commission currency
                          if (i + 1 < lines.length && /^[A-Z]{3}$/.test(lines[i + 1])) {
                            currentTrade.commissionCurrency = lines[i + 1];
                            i++;
                            console.log(`   Commission Currency: ${currentTrade.commissionCurrency}`);
                          }
                          
                          // Look for fees
                          if (i + 1 < lines.length && /^[\d,.-]+$/.test(lines[i + 1])) {
                            currentTrade.fees = lines[i + 1];
                            i++;
                            console.log(`   Fees: ${currentTrade.fees}`);
                            
                            // Look for fees currency
                            if (i + 1 < lines.length && /^[A-Z]{3}$/.test(lines[i + 1])) {
                              currentTrade.feesCurrency = lines[i + 1];
                              i++;
                              console.log(`   Fees Currency: ${currentTrade.feesCurrency}`);
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  
  // Add the last trade if exists
  if (currentTrade && currentTrade.orderNo) {
    trades.push(currentTrade);
  }
  
  console.log(`\n✅ Extracted ${trades.length} trades total`);
  return trades;
}

async function testTradeConfirmationFinal() {
  try {
    console.log('🧪 Final Optimized Trade Confirmation Test');
    console.log('=' .repeat(60));

    // File path and password
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const password = '6369';

    console.log(`📁 PDF File: ${pdfPath}`);
    console.log(`🔐 Password: ${password}`);
    console.log('');

    // Read and parse PDF
    const pdfBuffer = fs.readFileSync(pdfPath);
    console.log(`✅ PDF file loaded (${pdfBuffer.length} bytes)`);

    // Extract full text
    console.log('📖 Extracting full PDF text...');
    const pdfExtract = new PDFExtract();
    const tempFilePath = path.join(__dirname, '../temp_debug.pdf');
    fs.writeFileSync(tempFilePath, pdfBuffer);

    const extractOptions = { password: password.trim() };
    const data = await new Promise((resolve, reject) => {
      pdfExtract.extract(tempFilePath, extractOptions, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });

    const fullText = data.pages.map(page =>
      page.content.map(item => item.str).join(' ')
    ).join('\n');

    fs.unlinkSync(tempFilePath);

    // Extract trade confirmation section
    console.log('🔍 EXTRACTING TRADE CONFIRMATION SECTION:');
    console.log('=' .repeat(60));
    
    const tradeSection = extractSectionCaseInsensitive(fullText, 'LOCAL MARKET', 'POSITION');
    
    if (tradeSection) {
      console.log('✅ Trade section found');
      console.log(`Section length: ${tradeSection.length} characters`);
      
      // Extract individual trades
      const trades = extractTrades(tradeSection);
      
      console.log('');
      console.log('📊 FINAL TRADE RESULTS:');
      console.log('=' .repeat(60));
      
      if (trades.length > 0) {
        trades.forEach((trade, index) => {
          console.log(`\n📈 Trade #${index + 1}:`);
          console.log(`   Date: ${trade.date}`);
          console.log(`   Order No: ${trade.orderNo}`);
          console.log(`   Market: ${trade.market}`);
          console.log(`   Instrument: ${trade.instrument}`);
          console.log(`   Description: ${trade.description}`);
          console.log(`   Quantity: ${trade.quantity}`);
          console.log(`   Currency: ${trade.currency}`);
          console.log(`   Strike Price: ${trade.strikePrice}`);
          console.log(`   Type: ${trade.type}`);
          console.log(`   Premium: ${trade.premium} ${trade.premiumCurrency}`);
          console.log(`   Commission: ${trade.commission} ${trade.commissionCurrency}`);
          console.log(`   Fees: ${trade.fees} ${trade.feesCurrency}`);
        });
        
        console.log('');
        console.log('🔧 RAW JSON OUTPUT:');
        console.log(JSON.stringify(trades, null, 2));
        
      } else {
        console.log('❌ No trades found in parsed data');
      }
      
    } else {
      console.log('❌ Trade section not found');
    }

    console.log('');
    console.log('✅ Final test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testTradeConfirmationFinal()
    .then(() => {
      console.log('🎉 Final test completed!');
    })
    .catch((error) => {
      console.error('💥 Final test failed:', error);
    });
}

module.exports = { testTradeConfirmationFinal };
