import React, { useRef, useMemo } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, TimeScale } from 'chart.js';
import { Line } from 'react-chartjs-2';
import annotationPlugin from 'chartjs-plugin-annotation';
import zoomPlugin from 'chartjs-plugin-zoom';
import { Crosshair } from '../../utils/crosshair-plugin';
import 'chartjs-adapter-date-fns'; // Time scale adapter
import 'hammerjs'; // Required for touch gesture support

// Register Chart.js components and plugins
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
  annotationPlugin,
  zoomPlugin,
  Crosshair
);

/**
 * Strategy Time Decay Chart Component
 * 
 * Displays P&L analysis over time showing theta decay effects with zoom/pan functionality
 */
const StrategyTimeDecayChart = ({ timeDecayData, simulationParams, strategyName }) => {
  const chartRef = useRef(null);

  // Reset zoom function
  const resetZoom = () => {
    if (chartRef.current) {
      chartRef.current.resetZoom();
    }
  };

  // Process data for Chart.js
  const chartData = useMemo(() => {
    if (!timeDecayData || timeDecayData.length === 0) {
      return {
        labels: [],
        datasets: []
      };
    }

    const labels = timeDecayData.map(point => new Date(point.date));
    const pnlData = timeDecayData.map(point => point.totalPnL);

    return {
      labels,
      datasets: [
        {
          label: 'Total P&L',
          data: pnlData,
          borderColor: 'rgb(239, 68, 68)',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.1,
          pointRadius: 3,
          pointHoverRadius: 6,
        }
      ]
    };
  }, [timeDecayData]);

  // Chart options with zoom/pan
  const chartOptions = useMemo(() => {
    const today = new Date();
    
    return {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index',
        intersect: false,
      },
      plugins: {
        title: {
          display: true,
          text: `Time Decay Analysis - ${strategyName || 'Strategy'}`,
          font: {
            size: 16,
            weight: 'bold'
          }
        },
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            title: function(context) {
              const date = new Date(context[0].label);
              const daysFromNow = Math.ceil((date - today) / (1000 * 60 * 60 * 24));
              return `${date.toLocaleDateString()} (${daysFromNow} days)`;
            },
            label: function(context) {
              const value = context.parsed.y;
              const prefix = context.dataset.label;
              return `${prefix}: $${value.toFixed(2)}`;
            }
          }
        },
        annotation: {
          annotations: {
            today: {
              type: 'line',
              xMin: today,
              xMax: today,
              borderColor: 'rgba(34, 197, 94, 0.8)',
              borderWidth: 2,
              borderDash: [5, 5],
              label: {
                content: 'Today',
                enabled: true,
                position: 'top'
              }
            },
            breakeven: {
              type: 'line',
              yMin: 0,
              yMax: 0,
              borderColor: 'rgba(75, 192, 192, 0.8)',
              borderWidth: 1,
              borderDash: [3, 3],
              label: {
                content: 'Breakeven',
                enabled: true,
                position: 'start'
              }
            }
          }
        },
        crosshair: {
          line: {
            color: 'rgba(100, 100, 100, 0.7)',
            width: 1,
            dashPattern: [5, 5]
          },
          sync: {
            enabled: true,
            group: 2,
            suppressTooltips: false
          },
          snap: {
            enabled: true
          }
        },
        zoom: {
          pan: {
            enabled: true,
            mode: 'xy',
          },
          zoom: {
            wheel: {
              enabled: true,
              speed: 0.1
            },
            pinch: {
              enabled: true
            },
            mode: 'xy',
          },
          limits: {
            x: {
              min: 'original',
              max: 'original',
              minRange: 1
            },
            y: {
              min: 'original',
              max: 'original',
              minRange: 100
            }
          }
        }
      },
      scales: {
        x: {
          type: 'time',
          display: true,
          title: {
            display: true,
            text: 'Date'
          },
          grid: {
            display: true,
            color: 'rgba(0, 0, 0, 0.1)'
          },
          time: {
            displayFormats: {
              day: 'MMM dd',
              week: 'MMM dd',
              month: 'MMM yyyy'
            }
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'P&L ($)'
          },
          grid: {
            display: true,
            color: 'rgba(0, 0, 0, 0.1)'
          }
        }
      }
    };
  }, [strategyName]);

  if (!timeDecayData || timeDecayData.length === 0) {
    return (
      <div className="bg-white p-4 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-2">Time Decay Analysis Chart</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          No time decay data available. Click "Calculate P&L" to generate chart.
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      {/* Chart Header */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-800">Time Decay Analysis Chart</h3>
          <p className="text-sm text-gray-600">
            P&L over time (Theta decay) | Stock: ${simulationParams?.stockPrice || 0} | 
            IV: {simulationParams?.volatility || 0}% | RFR: {simulationParams?.riskFreeRate || 0}%
          </p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={resetZoom}
            className="px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 text-xs rounded border"
            title="Reset zoom"
          >
            Reset Zoom
          </button>
        </div>
      </div>

      {/* Chart Container */}
      <div style={{ height: '400px' }}>
        <Line ref={chartRef} options={chartOptions} data={chartData} />
      </div>

      {/* Chart Instructions */}
      <div className="mt-2 text-xs text-gray-500">
        💡 Use mouse wheel to zoom, drag to pan, double-click to reset
      </div>
    </div>
  );
};

export default StrategyTimeDecayChart;
