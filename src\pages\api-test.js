import React, { useState, useEffect } from 'react';

/**
 * API Test Page
 *
 * This page directly tests the API endpoint for HTI trades with expiry 2025-06.
 */
const APITestPage = () => {
  const [trades, setTrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setLoading(true);
        const url = `/api/firebase-trades-by-expiry?expiryDate=2025-06&stock=HTI`;
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('API Response:', data);
        
        setTrades(data.trades || []);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching trades:', error);
        setError(error.message);
        setLoading(false);
      }
    };

    fetchTrades();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">API Test: HTI Trades for 2025-06</h1>
      
      {loading ? (
        <p>Loading...</p>
      ) : error ? (
        <p className="text-red-500">Error: {error}</p>
      ) : (
        <div>
          <h2 className="text-xl font-semibold mb-2">Found {trades.length} trades</h2>
          
          <div className="bg-gray-100 p-4 rounded mb-4">
            <h3 className="font-bold mb-2">Raw API Response:</h3>
            <pre className="whitespace-pre-wrap">{JSON.stringify(trades, null, 2)}</pre>
          </div>
          
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticker</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Strike</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Premium</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {trades.map((trade) => (
                <tr key={trade.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{trade.id}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{trade.ticker}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{trade.type}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{trade.strike}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{trade.ExpiryDate}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{trade.premium}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default APITestPage;
