/**
 * Test script for verifying option premium calculations at expiry
 * 
 * This script tests the calculation of option premiums at expiry,
 * particularly focusing on the case where stock price equals strike price.
 */

import { calculatePositionPremium } from './position-utils.js';
import { calculateOptionPrice } from './black-scholes-robust.js';

// Function to test premium calculation at expiry
function testPremiumAtExpiry() {
  console.log("=== TESTING OPTION PREMIUMS AT EXPIRY ===");
  
  // Test case 1: Put option at expiry with stock price = strike price
  const putAtParityPosition = {
    id: 'test-put-parity-expiry',
    type: 'Put',
    strike: 23200,
    daysToExpiry: 0,
    ExpiryDate: '2024-05-25' // Assuming this is today or in the past
  };
  
  const putAtParityPremium = calculatePositionPremium(
    putAtParityPosition,
    23200, // Stock price equals strike
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  
  console.log(`Put option at parity (S=K=23200) at expiry premium: ${putAtParityPremium.toFixed(2)}`);
  console.log(`Expected value: 0.00`);
  
  // Test case 2: Call option at expiry with stock price = strike price
  const callAtParityPosition = {
    id: 'test-call-parity-expiry',
    type: 'Call',
    strike: 23200,
    daysToExpiry: 0,
    ExpiryDate: '2024-05-25' // Assuming this is today or in the past
  };
  
  const callAtParityPremium = calculatePositionPremium(
    callAtParityPosition,
    23200, // Stock price equals strike
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  
  console.log(`Call option at parity (S=K=23200) at expiry premium: ${callAtParityPremium.toFixed(2)}`);
  console.log(`Expected value: 0.00`);
  
  // Test case 3: Put option at expiry with stock price < strike price (in the money)
  const putInTheMoneyPosition = {
    id: 'test-put-itm-expiry',
    type: 'Put',
    strike: 23300,
    daysToExpiry: 0,
    ExpiryDate: '2024-05-25'
  };
  
  const putInTheMoneyPremium = calculatePositionPremium(
    putInTheMoneyPosition,
    23200, // Stock price < strike
    2,
    30
  );
  
  console.log(`Put option ITM (S=23200, K=23300) at expiry premium: ${putInTheMoneyPremium.toFixed(2)}`);
  console.log(`Expected value: 100.00`);
  
  // Test case 4: Call option at expiry with stock price > strike price (in the money)
  const callInTheMoneyPosition = {
    id: 'test-call-itm-expiry',
    type: 'Call',
    strike: 23100,
    daysToExpiry: 0,
    ExpiryDate: '2024-05-25'
  };
  
  const callInTheMoneyPremium = calculatePositionPremium(
    callInTheMoneyPosition,
    23200, // Stock price > strike
    2,
    30
  );
  
  console.log(`Call option ITM (S=23200, K=23100) at expiry premium: ${callInTheMoneyPremium.toFixed(2)}`);
  console.log(`Expected value: 100.00`);
  
  // Test direct Black-Scholes calculation
  console.log("\n=== TESTING DIRECT BLACK-SCHOLES AT EXPIRY ===");
  
  // Test case 5: Direct Black-Scholes for put at parity at expiry
  const directPutAtParity = calculateOptionPrice(
    'Put',
    23200, // Stock price
    23200, // Strike price
    0.02,  // Risk-free rate (2%)
    0.3,   // Volatility (30%)
    0      // Time to expiry (0 days)
  );
  
  console.log(`Direct B-S Put at parity (S=K=23200) at expiry: ${directPutAtParity.toFixed(2)}`);
  console.log(`Expected value: 0.00`);
  
  // Test case 6: Direct Black-Scholes for call at parity at expiry
  const directCallAtParity = calculateOptionPrice(
    'Call',
    23200, // Stock price
    23200, // Strike price
    0.02,  // Risk-free rate (2%)
    0.3,   // Volatility (30%)
    0      // Time to expiry (0 days)
  );
  
  console.log(`Direct B-S Call at parity (S=K=23200) at expiry: ${directCallAtParity.toFixed(2)}`);
  console.log(`Expected value: 0.00`);
}

// Run the tests
testPremiumAtExpiry();

// Export the test function for potential reuse
export { testPremiumAtExpiry };
