import requests
from urllib.parse import urlparse
import os
import time
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def fetch_html(url, save_to_file=True, filename=None, max_retries=3):
    """
    Fetch HTML content from a URL and optionally save it to a file.
    
    Args:
        url (str): The URL to fetch
        save_to_file (bool): Whether to save the content to a file
        filename (str): Optional filename, if not provided, generates from URL
        max_retries (int): Maximum number of retry attempts
    
    Returns:
        str: The HTML content
    """
    
    # Create a session with retry strategy
    session = requests.Session()
    
    # Define retry strategy
    retry_strategy = Retry(
        total=max_retries,
        status_forcelist=[429, 500, 502, 503, 504],
        method_whitelist=["HEAD", "GET", "OPTIONS"],
        backoff_factor=1
    )
    
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # Multiple sets of headers to try
    headers_options = [
        {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        },
        {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        },
        {
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
    ]
    
    for attempt, headers in enumerate(headers_options, 1):
        try:
            print(f"Attempt {attempt}: Fetching content from: {url}")
            
            # Try different timeout settings
            timeouts = [(10, 30), (15, 45), (20, 60)]  # (connect_timeout, read_timeout)
            
            for connect_timeout, read_timeout in timeouts:
                try:
                    print(f"  Trying with timeout: connect={connect_timeout}s, read={read_timeout}s")
                    
                    response = session.get(
                        url, 
                        headers=headers, 
                        timeout=(connect_timeout, read_timeout),
                        allow_redirects=True
                    )
                    response.raise_for_status()
                    
                    print(f"  Success! Status code: {response.status_code}")
                    break
                    
                except requests.exceptions.Timeout as e:
                    print(f"  Timeout with {connect_timeout}s/{read_timeout}s: {e}")
                    if (connect_timeout, read_timeout) == timeouts[-1]:
                        raise
                    continue
                    
            else:
                continue  # All timeouts failed, try next header set
                
            break  # Success, exit the headers loop
            
        except requests.exceptions.RequestException as e:
            print(f"  Attempt {attempt} failed: {e}")
            if attempt < len(headers_options):
                print(f"  Waiting 2 seconds before next attempt...")
                time.sleep(2)
            else:
                raise
    
    try:
        # Get the HTML content
        html_content = response.text
        
        print(f"Successfully fetched {len(html_content)} characters")
        print(f"Content encoding: {response.encoding}")
        
        # Save to file if requested
        if save_to_file:
            if not filename:
                # Generate filename from URL
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path)
                if not filename or not filename.endswith(('.htm', '.html')):
                    filename = 'fetched_page.html'
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"Content saved to: {filename}")
        
        return html_content
        
    except requests.exceptions.RequestException as e:
        print(f"Error fetching the URL: {e}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None
    finally:
        session.close()

def simple_fetch_alternative(url):
    """
    Alternative simpler approach using urllib
    """
    import urllib.request
    import urllib.error
    
    try:
        print(f"\nTrying alternative method with urllib...")
        
        req = urllib.request.Request(
            url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        
        with urllib.request.urlopen(req, timeout=45) as response:
            html_content = response.read().decode('utf-8')
            print(f"Alternative method succeeded! Fetched {len(html_content)} characters")
            return html_content
            
    except urllib.error.URLError as e:
        print(f"Alternative method failed: {e}")
        return None

# Main execution
if __name__ == "__main__":
    url = "https://www.hkex.com.hk/eng/stat/dmstat/dayrpt/hsiwo250613.htm"
    
    print("="*60)
    print("HTML FETCHER - Multiple Approaches")
    print("="*60)
    
    # Try main method first
    html_content = fetch_html(url)
    
    # If main method fails, try alternative
    if not html_content:
        print("\n" + "-"*40)
        print("Main method failed, trying alternative...")
        print("-"*40)
        html_content = simple_fetch_alternative(url)
    
    # If still no content, try a manual curl-like approach
    if not html_content:
        print("\n" + "-"*40)
        print("Trying manual approach...")
        print("-"*40)
        try:
            import subprocess
            result = subprocess.run([
                'curl', '-L', '--user-agent', 
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                '--connect-timeout', '10',
                '--max-time', '60',
                url
            ], capture_output=True, text=True, timeout=70)
            
            if result.returncode == 0 and result.stdout:
                html_content = result.stdout
                print(f"Manual curl approach succeeded! Fetched {len(html_content)} characters")
                
                # Save the content
                with open('hsiwo250613.htm', 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print("Content saved to: hsiwo250613.htm")
            else:
                print(f"Curl failed with return code: {result.returncode}")
                if result.stderr:
                    print(f"Error: {result.stderr}")
                    
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            print(f"Manual approach failed: {e}")
    
    # Display results
    if html_content:
        print("\n" + "="*50)
        print("PREVIEW OF FETCHED HTML CONTENT:")
        print("="*50)
        # Show first 500 characters as preview
        print(html_content[:500])
        if len(html_content) > 500:
            print("\n... (content truncated)")
        print("="*50)
        print(f"Total length: {len(html_content)} characters")
    else:
        print("\n" + "!"*50)
        print("ALL METHODS FAILED")
        print("!"*50)
        print("Possible issues:")
        print("- The HKEX website might be blocking automated requests")
        print("- Network connectivity issues")
        print("- The specific URL might be temporarily unavailable")
        print("- Geographic restrictions")
        print("\nSuggestions:")
        print("- Try accessing the URL manually in a browser first")
        print("- Check if you need to be on a specific network")
        print("- The URL might require cookies/session from main site")
        print("- Try using a VPN if there are geographic restrictions")
