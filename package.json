{"name": "options-analyzer", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "body-parser": "^2.2.0", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-annotation": "^3.1.0", "chartjs-plugin-zoom": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase-admin": "^13.4.0", "hammerjs": "^2.0.8", "jsdom": "^25.0.1", "multer": "^2.0.2", "node-fetch": "^2.7.0", "pdf-parse": "^1.1.1", "pdf.js-extract": "^0.2.1", "pg": "^8.15.6", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "recharts": "^2.5.0", "web-vitals": "^2.1.4", "yahoo-finance2": "^2.13.3", "yahoo-stock-api": "^2.2.1", "zustand": "^5.0.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server.js", "server:dev": "nodemon server.js", "dev": "concurrently \"npm run server:dev\" \"npm start\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "concurrently": "^8.2.2", "http-proxy-middleware": "^2.0.9", "nodemon": "^3.1.10", "postcss": "^8.4.23", "tailwindcss": "^3.3.2"}}