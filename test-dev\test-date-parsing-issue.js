/**
 * Specific test for the date parsing issue with 2025-10-07
 */

function testDateParsing() {
  console.log('🧪 TESTING DATE PARSING SPECIFICALLY');
  console.log('=' .repeat(40));
  
  // Test the exact parsing logic from position-utils
  const testDateStr = '7/10/2025';
  console.log(`🎯 Testing date string: "${testDateStr}"`);
  
  // Parse the same way position-utils does
  const [day, month, year] = testDateStr.split('/');
  console.log(`   Split parts: day="${day}", month="${month}", year="${year}"`);
  
  const parsedInts = [parseInt(day), parseInt(month), parseInt(year)];
  console.log(`   Parsed integers: day=${parsedInts[0]}, month=${parsedInts[1]}, year=${parsedInts[2]}`);
  
  // Create the date the same way
  const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  console.log(`   Created Date: ${holidayDate.toISOString()}`);
  console.log(`   Date string: ${holidayDate.toISOString().split('T')[0]}`);
  console.log(`   Expected: 2025-10-07`);
  
  const isCorrect = holidayDate.toISOString().split('T')[0] === '2025-10-07';
  console.log(`   ✅ Parsing correct: ${isCorrect ? 'YES' : 'NO'}`);
  
  // Test different date formats to be sure
  const testCases = [
    '7/10/2025',    // Single digit day
    '07/10/2025',   // Double digit day
    '7/1/2025',     // Single digit month
    '07/01/2025'    // Double digit everything
  ];
  
  console.log('\n🔍 Testing various date formats:');
  testCases.forEach(dateStr => {
    try {
      const [d, m, y] = dateStr.split('/');
      const date = new Date(parseInt(y), parseInt(m) - 1, parseInt(d));
      const result = date.toISOString().split('T')[0];
      console.log(`   ${dateStr} → ${result}`);
    } catch (e) {
      console.log(`   ${dateStr} → ERROR: ${e.message}`);
    }
  });
}

function testXMLExtraction() {
  console.log('\n📄 TESTING XML EXTRACTION');
  console.log('=' .repeat(30));
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    const xmlPath = path.join(process.cwd(), 'output', 'hkex_holidays.xml');
    const xmlContent = fs.readFileSync(xmlPath, 'utf8');
    
    // Find the specific line with 7/10/2025
    const lines = xmlContent.split('\n');
    const targetLine = lines.find(line => line.includes('7/10/2025'));
    
    if (targetLine) {
      console.log(`🎯 Found line with 7/10/2025:`);
      console.log(`   "${targetLine.trim()}"`);
      
      // Simulate how DOMParser would extract this
      // Since we're in Node.js, simulate the cell extraction
      const cellMatch = targetLine.match(/>([^<]*7\/10\/2025[^<]*)</);
      if (cellMatch) {
        const cellContent = cellMatch[1];
        console.log(`   Cell content: "${cellContent}"`);
        
        const dateText = cellContent.trim();
        const datePart = dateText.split(' ')[0];
        
        console.log(`   Date text: "${dateText}"`);
        console.log(`   Date part: "${datePart}"`);
        
        // Parse it
        if (datePart && datePart.includes('/')) {
          const [day, month, year] = datePart.split('/');
          const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
          
          console.log(`   Final parsed date: ${holidayDate.toISOString().split('T')[0]}`);
          console.log(`   ✅ Should be 2025-10-07: ${holidayDate.toISOString().split('T')[0] === '2025-10-07'}`);
        }
      }
    } else {
      console.log('❌ Could not find line with 7/10/2025 in XML');
    }
    
  } catch (error) {
    console.error('❌ Error testing XML extraction:', error.message);
  }
}

function testBrowserEnvironmentSimulation() {
  console.log('\n🌐 TESTING BROWSER ENVIRONMENT SIMULATION');
  console.log('=' .repeat(45));
  
  try {
    // Try to use jsdom to simulate exactly what happens in the browser
    const { JSDOM } = require('jsdom');
    const fs = require('fs');
    const path = require('path');
    
    const xmlPath = path.join(process.cwd(), 'output', 'hkex_holidays.xml');
    const xmlContent = fs.readFileSync(xmlPath, 'utf8');
    
    const dom = new JSDOM();
    const DOMParser = dom.window.DOMParser;
    
    console.log('🔍 Using jsdom DOMParser to parse XML...');
    
    const parser = new DOMParser();
    const doc = parser.parseFromString(xmlContent, 'text/xml');
    
    // Check for parsing errors
    const parseError = doc.querySelector('parsererror');
    if (parseError) {
      console.log('❌ XML parsing error:', parseError.textContent);
      return;
    }
    
    console.log('✅ XML parsed successfully with jsdom');
    
    // Find all holiday dates
    const holidays = [];
    const rows = doc.querySelectorAll('tr');
    
    console.log(`📊 Found ${rows.length} table rows`);
    
    let targetDateFound = false;
    let targetDateParsed = null;
    
    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length > 0) {
        const dateText = cells[0].textContent.trim();
        const datePart = dateText.split(' ')[0];
        
        if (datePart && datePart.includes('/')) {
          try {
            const [day, month, year] = datePart.split('/');
            const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
            holidays.push(holidayDate);
            
            // Check if this is our target date
            if (datePart === '7/10/2025') {
              targetDateFound = true;
              targetDateParsed = holidayDate;
              
              console.log(`🎯 FOUND TARGET DATE:`);
              console.log(`   Raw text: "${dateText}"`);
              console.log(`   Date part: "${datePart}"`);
              console.log(`   Parsed as: ${holidayDate.toISOString().split('T')[0]}`);
              console.log(`   ✅ Correct: ${holidayDate.toISOString().split('T')[0] === '2025-10-07'}`);
            }
          } catch (e) {
            console.warn(`   Could not parse date: ${datePart}`);
          }
        }
      }
    }
    
    console.log(`\n📅 Total holidays parsed: ${holidays.length}`);
    console.log(`🎯 Target date (7/10/2025) found: ${targetDateFound}`);
    
    if (targetDateFound && targetDateParsed) {
      // Test if this date would be detected as a holiday
      const testDate = new Date('2025-10-07');
      const isHoliday = holidays.some(h => 
        h.getFullYear() === testDate.getFullYear() &&
        h.getMonth() === testDate.getMonth() &&
        h.getDate() === testDate.getDate()
      );
      
      console.log(`🔍 Would 2025-10-07 be detected as holiday: ${isHoliday ? '✅ YES' : '❌ NO'}`);
      
      if (!isHoliday) {
        console.log('❌ PROBLEM: Date exists in XML but not detected as holiday!');
        console.log(`   Expected: 2025-10-07`);
        console.log(`   Parsed as: ${targetDateParsed.toISOString().split('T')[0]}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error with jsdom simulation:', error.message);
    
    if (error.message.includes("Cannot find module 'jsdom'")) {
      console.log('💡 jsdom not installed - run "npm install jsdom" to test this');
    }
  }
}

// Run all tests
function runDateParsingTests() {
  console.log('🚀 DEBUGGING DATE PARSING FOR 2025-10-07');
  console.log('=' .repeat(50));
  
  testDateParsing();
  testXMLExtraction();
  testBrowserEnvironmentSimulation();
  
  console.log('\n📋 SUMMARY:');
  console.log('If parsing is correct but detection fails, the issue is in:');
  console.log('1. XML loading/caching mechanism');
  console.log('2. Holiday comparison logic');
  console.log('3. calculateDaysToExpiry not using holidays properly');
}

if (require.main === module) {
  runDateParsingTests();
}

module.exports = { testDateParsing, testXMLExtraction, testBrowserEnvironmentSimulation };