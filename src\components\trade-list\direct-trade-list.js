import React, { useState, useEffect } from 'react';
import useDatabaseStore from '../../store/useDatabaseStore';
import DatabaseOfflineIndicator from '../database-offline-indicator';

/**
 * Direct Trade List Component
 *
 * This component directly accesses Firestore to fetch and display trades.
 */
const DirectTradeList = () => {
  // Access database status context
  const { isOffline } = useDatabaseStore();

  // State for all trades
  const [allTrades, setAllTrades] = useState([]);
  // State for filtered trades
  const [filteredTrades, setFilteredTrades] = useState([]);
  // State for unique stocks
  const [stocks, setStocks] = useState([]);
  // State for selected stock
  const [selectedStock, setSelectedStock] = useState('');
  // State for unique expiry dates
  const [expiryDates, setExpiryDates] = useState([]);
  // State for selected expiry date
  const [selectedExpiryDate, setSelectedExpiryDate] = useState('');
  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  // State for error message
  const [error, setError] = useState(null);

  // Fetch all stocks from the API
  useEffect(() => {
    const fetchStocks = async () => {
      try {
        setIsLoading(true);

        // Get all stocks from the API
        const response = await fetch('/api/firebase-stocks');

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        setStocks(data.stocks || []);

        // We'll fetch trades for each stock and expiry date combination
        const allTrades = [];

        // For each stock, fetch the expiry dates
        for (const stockCode of data.stocks) {
          try {
            const expiryResponse = await fetch(`/api/firebase-expiry-dates?stock=${stockCode}`);

            if (!expiryResponse.ok) {
              console.error(`Error fetching expiry dates for stock ${stockCode}`);
              continue;
            }

            const expiryData = await expiryResponse.json();

            // For each expiry date, fetch the trades
            for (const expiryDate of expiryData.expiryDates) {
              try {
                const tradesResponse = await fetch(`/api/firebase-trades-by-expiry?expiryDate=${expiryDate}&stock=${stockCode}`);

                if (!tradesResponse.ok) {
                  console.error(`Error fetching trades for stock ${stockCode} and expiry ${expiryDate}`);
                  continue;
                }

                const tradesData = await tradesResponse.json();
                allTrades.push(...tradesData.trades);
              } catch (error) {
                console.error(`Error fetching trades for stock ${stockCode} and expiry ${expiryDate}:`, error);
              }
            }
          } catch (error) {
            console.error(`Error fetching expiry dates for stock ${stockCode}:`, error);
          }
        }

        console.log('All trades:', allTrades);

        // Process trades to ensure futures are correctly identified
        const processedTrades = allTrades.map(trade => {
          // Check if this is a futures trade based on various indicators
          const isFuture =
            trade.type === 'Future' ||
            trade.type === 'future' ||
            trade.strike === 'N/A' ||
            (trade.ticker && trade.ticker.length <= 5);

          if (isFuture) {
            return {
              ...trade,
              type: 'Future',
              strike: 'N/A'
            };
          }

          return trade;
        });

        setAllTrades(processedTrades);
        setIsLoading(false);

        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOnline();
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to fetch data');
        setIsLoading(false);

        // Update database status using Zustand
        useDatabaseStore.getState().setDatabaseOffline(error);
      }
    };

    fetchStocks();
  }, []);

  // Filter expiry dates when stock changes
  useEffect(() => {
    if (!selectedStock) {
      setExpiryDates([]);
      setSelectedExpiryDate('');
      setFilteredTrades([]);
      return;
    }

    // Find all unique expiry dates for the selected stock
    const uniqueExpiryDates = new Set();

    allTrades.forEach(trade => {
      const stockCode = (trade.ticker || '').substring(0, 3);

      if (stockCode === selectedStock && trade.expiry) {
        uniqueExpiryDates.add(trade.expiry);
      }
    });

    // Sort expiry dates
    const sortedDates = Array.from(uniqueExpiryDates).sort((a, b) => {
      const dateA = new Date(a.includes('-') && a.length === 7 ? `${a}-01` : a);
      const dateB = new Date(b.includes('-') && b.length === 7 ? `${b}-01` : b);

      if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
        return dateA.getTime() - dateB.getTime();
      }
      return a.localeCompare(b);
    });

    setExpiryDates(sortedDates);
    setSelectedExpiryDate('');
    setFilteredTrades([]);
  }, [selectedStock, allTrades]);

  // Filter trades when expiry date changes
  useEffect(() => {
    if (!selectedStock || !selectedExpiryDate) {
      setFilteredTrades([]);
      return;
    }

    // Filter trades by stock and expiry date
    const filtered = allTrades.filter(trade => {
      const stockCode = (trade.ticker || '').substring(0, 3);
      return stockCode === selectedStock && trade.expiry === selectedExpiryDate;
    });

    console.log(`Found ${filtered.length} trades for ${selectedStock} with expiry ${selectedExpiryDate}`);
    console.log('Filtered trades:', filtered);

    setFilteredTrades(filtered);
  }, [selectedExpiryDate, selectedStock, allTrades]);

  // Clear filters
  const clearFilters = () => {
    setSelectedStock('');
    setSelectedExpiryDate('');
    setFilteredTrades([]);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const parts = dateString.split('-');
    if (parts.length >= 3) {
      const month = parseInt(parts[1]);
      const day = parseInt(parts[2]);
      return `${month}/${day}`;
    }
    return dateString;
  };

  return (
    <div className="space-y-8">
      <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold text-gray-800">Direct Trade List</h2>
          <DatabaseOfflineIndicator />
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-blue-100 p-2 rounded">
            <div className="font-bold text-xs mb-1">Stock</div>
            <select
              className="w-full p-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              value={selectedStock}
              onChange={(e) => setSelectedStock(e.target.value)}
              disabled={isLoading || isOffline}
            >
              <option value="">Select a stock</option>
              {stocks.map(stock => (
                <option key={stock} value={stock}>{stock}</option>
              ))}
            </select>
          </div>

          <div className="bg-blue-100 p-2 rounded">
            <div className="font-bold text-xs mb-1">Expiry Date</div>
            <select
              className="w-full p-1 text-xs border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
              value={selectedExpiryDate}
              onChange={(e) => setSelectedExpiryDate(e.target.value)}
              disabled={!selectedStock || isLoading || isOffline}
            >
              <option value="">Select an expiry date</option>
              {expiryDates.map(date => (
                <option key={date} value={date}>{formatDate(date)}</option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={clearFilters}
              className="w-full p-1 text-xs bg-gray-200 hover:bg-gray-300 rounded"
              disabled={!selectedStock && !selectedExpiryDate}
            >
              Clear Filters
            </button>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-4">
            <p className="text-gray-500">Loading trades...</p>
          </div>
        ) : error ? (
          <div className="text-center py-4">
            <p className="text-red-500">{error}</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <div className="h-[600px] overflow-y-auto border rounded-md">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ticker
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expiry
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Strike
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Uploaded At
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTrades.length === 0 ? (
                    <tr>
                      <td colSpan="7" className="px-3 py-4 text-center text-sm text-gray-500">
                        No trades match the current filters, or no trades have been uploaded yet.
                      </td>
                    </tr>
                  ) : (
                    filteredTrades.map((trade) => (
                      <tr key={trade.id} className={
                        trade.type === 'Future' || trade.type === 'future' ||
                        (trade.ticker && trade.ticker.length <= 5 && trade.strike === 'N/A') ? 'bg-yellow-50' :
                        trade.type === 'Call' || trade.type === 'call' ? 'bg-cyan-50' :
                        trade.type === 'Put' || trade.type === 'put' ? 'bg-red-50' :
                        'bg-gray-50'
                      }>
                        <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900" title={trade.ticker}>
                          {trade.ticker}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.ticker && trade.ticker.length <= 5 && trade.strike === 'N/A' ? 'Future' : trade.type}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.expiry}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.ticker && trade.ticker.length <= 5 ? 'N/A' : trade.strike}
                        </td>
                        <td className={`px-3 py-2 whitespace-nowrap text-sm text-right ${trade.quantity < 0 ? 'text-red-500' : 'text-green-600'}`}>
                          {trade.quantity}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {trade.price != null ? trade.price.toFixed(2) : 'N/A'}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                          {trade.created_at ? new Date(trade.created_at).toLocaleDateString() : 'N/A'}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Showing {filteredTrades.length} trade{filteredTrades.length === 1 ? "" : "s"}.
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DirectTradeList;
