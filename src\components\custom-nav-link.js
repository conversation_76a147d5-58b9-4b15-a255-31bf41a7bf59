import React from 'react';
import { Link, useLocation } from 'react-router-dom';

/**
 * Custom NavLink Component
 * 
 * This component extends the Link component from react-router-dom
 * to provide active state styling and ensure proper navigation.
 * 
 * @param {Object} props - Component props
 * @param {string} props.to - Target path
 * @param {React.ReactNode} props.children - Child elements
 * @param {string} props.className - CSS class names
 * @param {string} props.activeClassName - CSS class names to apply when active
 */
const CustomNavLink = ({ 
  to, 
  children, 
  className = '', 
  activeClassName = 'text-blue-800 font-bold' 
}) => {
  const location = useLocation();
  const isActive = location.pathname === to;
  
  return (
    <Link 
      to={to}
      className={`${className} ${isActive ? activeClassName : ''}`}
      onClick={(e) => {
        // If we're already on this page, prevent default behavior
        // and force a reload of the component
        if (isActive) {
          e.preventDefault();
          window.location.href = to;
        }
      }}
    >
      {children}
    </Link>
  );
};

export default CustomNavLink;
