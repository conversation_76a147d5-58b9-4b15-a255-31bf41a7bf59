# Tile Button Implementation

## Overview
Added a "Tile" button to the strategy window header that provides smart window management functionality:
- **Single window**: Toggles between maximize and restore
- **Multiple windows**: Organizes all visible windows in a tile layout

## Implementation Details

### 1. Store Functions Added (`src/store/useStrategyWindowStore.js`)

#### `maximizeWindow(windowId)`
- Maximizes a single window to fill the viewport (with padding)
- Stores original position/size for restoration
- Sets `isMaximized: true` flag

#### `restoreWindowSize(windowId)`
- Restores a maximized window to its original position and size
- Clears the `isMaximized` flag and `originalState`

#### `tileOrMaximize(windowId)`
- **Smart behavior**:
  - If only 1 visible window: toggles maximize/restore
  - If multiple visible windows: tiles all visible windows

#### `tileWindows()` (Enhanced)
- Calculates optimal grid layout based on number of visible windows
- Uses actual viewport dimensions instead of hardcoded values
- Arranges windows in a grid pattern with equal sizes
- Ignores minimized windows
- Resets maximized state for all windows

### 2. UI Components Updated

#### `src/components/strategy-planner/StrategyHeader.jsx`
- Added `onTileOrMaximize` prop
- Added tile button between minimize and close buttons
- Uses different icons for maximize vs restore states:
  - **Maximize**: Single square icon
  - **Restore**: Overlapping squares icon
- Dynamic tooltip text based on window state

#### `src/components/strategy-planner/StrategyWindow.jsx`
- Added `tileOrMaximize` function from store
- Added `handleTileOrMaximize` handler
- Passed handler to StrategyHeader component

#### `src/types/strategy.js`
- Updated `DEFAULT_WINDOW` to include:
  - `isMaximized: false`
  - `originalState: undefined`

## Button Behavior

### Single Window Scenario
1. **First click**: Maximizes window to full viewport
2. **Second click**: Restores window to original size/position

### Multiple Windows Scenario
1. **Any click**: Tiles all visible windows in optimal grid layout
2. Grid calculation:
   - `cols = Math.ceil(Math.sqrt(windowCount))`
   - `rows = Math.ceil(windowCount / cols)`
   - Equal-sized windows with small gaps

## Visual Design
- Button positioned between minimize (-) and close (×) buttons
- Hover effects consistent with other window controls
- Icons:
  - **Maximize**: `□` (single square)
  - **Restore**: `⧉` (overlapping squares)

## Testing

### Manual Testing Steps
1. **Navigate to Strategy Planner**: http://localhost:5003/strategy-planner
2. **Create a strategy** and open its window
3. **Test single window maximize**:
   - Click the tile button (square icon)
   - Window should maximize to full screen
   - Icon should change to overlapping squares
   - Click again to restore
4. **Test multiple window tiling**:
   - Create and open 2-4 strategy windows
   - Click tile button on any window
   - All windows should arrange in grid layout
   - Try with different numbers of windows

### Expected Results
- **1 window**: Toggles maximize/restore
- **2 windows**: Side-by-side layout
- **3 windows**: 2×2 grid with one empty slot
- **4 windows**: Perfect 2×2 grid
- **5+ windows**: Optimal grid based on square root

### Edge Cases Handled
- Minimized windows are ignored during tiling
- Viewport size is dynamically calculated
- Windows maintain proper padding from screen edges
- Original window states are preserved for restoration

## Files Modified
1. `src/store/useStrategyWindowStore.js` - Added tiling logic
2. `src/components/strategy-planner/StrategyHeader.jsx` - Added tile button
3. `src/components/strategy-planner/StrategyWindow.jsx` - Connected tile handler
4. `src/types/strategy.js` - Updated window type definition
5. `src/store/useStrategyWindowStore.test.js` - Added comprehensive tests

## Browser Compatibility
- Uses `document.documentElement.clientWidth/Height` for viewport detection
- Fallback values (1200×800) for older browsers
- Standard CSS positioning and sizing

The implementation provides a professional window management experience similar to modern desktop applications.
