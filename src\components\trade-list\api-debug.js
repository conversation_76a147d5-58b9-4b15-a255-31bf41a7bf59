import React, { useState, useEffect } from 'react';

/**
 * API Debug Component
 *
 * This component specifically debugs the /api/firebase-trades-by-expiry endpoint.
 * The issue has been fixed and the API now correctly returns both the Call option and the Future trade.
 */
const ApiDebug = () => {
  // State for API response
  const [apiResponse, setApiResponse] = useState(null);
  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  // State for error message
  const [error, setError] = useState(null);
  // State for debug logs
  const [logs, setLogs] = useState([]);
  // State for server logs
  const [serverLogs, setServerLogs] = useState('');
  // State for raw Firestore data
  const [firestoreData, setFirestoreData] = useState(null);

  // Add a log message
  const addLog = (message) => {
    setLogs(prevLogs => [...prevLogs, { time: new Date().toISOString(), message }]);
  };

  // Fetch server logs
  const fetchServerLogs = async () => {
    try {
      addLog('Fetching server logs...');
      const response = await fetch('/api/server-logs');

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      setServerLogs(data.logs || 'No logs available');
      addLog('Server logs fetched successfully');
    } catch (error) {
      console.error('Error fetching server logs:', error);
      addLog(`Error fetching server logs: ${error.message}`);
      setServerLogs('Failed to fetch server logs');
    }
  };

  // Fetch raw Firestore data
  const fetchFirestoreData = async () => {
    try {
      addLog('Fetching raw Firestore data...');
      const response = await fetch('/api/firebase-raw-data');

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      setFirestoreData(data);
      addLog('Raw Firestore data fetched successfully');
    } catch (error) {
      console.error('Error fetching raw Firestore data:', error);
      addLog(`Error fetching raw Firestore data: ${error.message}`);
      setFirestoreData(null);
    }
  };

  // Debug the API endpoint
  const debugApiEndpoint = async () => {
    try {
      setIsLoading(true);
      addLog('Starting API endpoint debug');

      // Step 1: Make a direct request to the API endpoint
      addLog('Step 1: Making direct request to /api/firebase-trades-by-expiry?expiryDate=2025-06&stock=HTI');
      const response = await fetch('/api/firebase-trades-by-expiry?expiryDate=2025-06&stock=HTI');

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json();
      addLog(`API response received with ${data.trades?.length || 0} trades`);

      // Step 2: Check for future trades in the response
      addLog('Step 2: Checking for future trades in the response');
      const futureTrades = data.trades?.filter(trade =>
        trade.type === 'Future' ||
        trade.type === 'future' ||
        trade.strike === 'N/A' ||
        (trade.ticker && trade.ticker.length <= 5)
      ) || [];

      if (futureTrades.length === 0) {
        addLog('No future trades found in the API response');
      } else {
        addLog(`Found ${futureTrades.length} future trades in the API response`);
        futureTrades.forEach(trade => {
          addLog(`- Ticker: ${trade.ticker}, Type: ${trade.type}, Strike: ${trade.strike}`);
        });
      }

      // Step 3: Check for HTIM5 ticker in the response
      addLog('Step 3: Checking for HTIM5 ticker in the response');
      const htim5Trades = data.trades?.filter(trade => trade.ticker === 'HTIM5') || [];

      if (htim5Trades.length === 0) {
        addLog('No trades with ticker HTIM5 found in the API response');
      } else {
        addLog(`Found ${htim5Trades.length} trades with ticker HTIM5 in the API response`);
        htim5Trades.forEach(trade => {
          addLog(`- Type: ${trade.type}, Strike: ${trade.strike}, Expiry: ${trade.expiry || trade.ExpiryDate}`);
        });
      }

      // Save the API response
      setApiResponse(data);
      setIsLoading(false);
    } catch (error) {
      console.error('Error debugging API endpoint:', error);
      addLog(`Error: ${error.message}`);
      setError(error.message);
      setIsLoading(false);
    }
  };

  // Run the debug process on component mount
  useEffect(() => {
    debugApiEndpoint();
  }, []);

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
      <h2 className="text-lg font-bold text-gray-800 mb-4">API Endpoint Debug: /api/firebase-trades-by-expiry</h2>

      {isLoading ? (
        <div className="text-center py-4">
          <p className="text-gray-500">Running API diagnostics...</p>
        </div>
      ) : error ? (
        <div className="text-center py-4">
          <p className="text-red-500">Error: {error}</p>
        </div>
      ) : (
        <div>
          <div className="mb-4">
            <h3 className="text-md font-semibold mb-2">Debug Logs:</h3>
            <div className="bg-gray-100 p-4 rounded mb-4 h-[200px] overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="mb-1">
                  <span className="text-gray-500 text-xs">{new Date(log.time).toLocaleTimeString()}</span>
                  <span className={`ml-2 text-sm ${log.message.startsWith('Error') ? 'text-red-500 font-bold' : ''}`}>
                    {log.message}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-4">
            <h3 className="text-md font-semibold mb-2">API Response:</h3>
            <div className="bg-gray-100 p-4 rounded mb-4 h-[200px] overflow-y-auto">
              <pre className="text-xs">{JSON.stringify(apiResponse, null, 2)}</pre>
            </div>
          </div>

          <div className="flex space-x-4 mb-4">
            <button
              onClick={fetchServerLogs}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Fetch Server Logs
            </button>

            <button
              onClick={fetchFirestoreData}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Fetch Raw Firestore Data
            </button>

            <button
              onClick={debugApiEndpoint}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              Re-run API Debug
            </button>
          </div>

          {serverLogs && (
            <div className="mb-4">
              <h3 className="text-md font-semibold mb-2">Server Logs:</h3>
              <div className="bg-gray-100 p-4 rounded mb-4 h-[200px] overflow-y-auto">
                <pre className="text-xs">{serverLogs}</pre>
              </div>
            </div>
          )}

          {firestoreData && (
            <div>
              <h3 className="text-md font-semibold mb-2">Raw Firestore Data:</h3>
              <div className="bg-gray-100 p-4 rounded mb-4 h-[200px] overflow-y-auto">
                <pre className="text-xs">{JSON.stringify(firestoreData, null, 2)}</pre>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ApiDebug;
