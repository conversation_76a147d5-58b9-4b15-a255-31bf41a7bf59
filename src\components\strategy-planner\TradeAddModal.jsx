import React, { useState } from 'react';

/**
 * Trade Add Modal Component
 * 
 * Modal for adding new trades to a strategy with form validation.
 */
const TradeAddModal = ({ isOpen, onClose, onAddTrade, strategyMetadata }) => {
  const [formData, setFormData] = useState({
    call_put: 'Call',
    strike: '',
    expirydate: '',
    quantity: '',
    premium: '',
    debit_credit: 'Debit'
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.strike || isNaN(formData.strike) || parseFloat(formData.strike) <= 0) {
      newErrors.strike = 'Strike price must be a positive number';
    }
    
    if (!formData.expirydate) {
      newErrors.expirydate = 'Expiry date is required';
    } else {
      const expiryDate = new Date(formData.expirydate);
      const today = new Date();
      if (expiryDate <= today) {
        newErrors.expirydate = 'Expiry date must be in the future';
      }
    }
    
    if (!formData.quantity || isNaN(formData.quantity) || parseInt(formData.quantity) === 0) {
      newErrors.quantity = 'Quantity must be a non-zero number';
    }
    
    if (!formData.premium || isNaN(formData.premium) || parseFloat(formData.premium) <= 0) {
      newErrors.premium = 'Premium must be a positive number';
    }
    
    return newErrors;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Validate form data
      const validationErrors = validateForm();
      
      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        setIsSubmitting(false);
        return;
      }
      
      // Prepare trade data for the existing API format
      const stockSymbol = strategyMetadata?.stockSymbol || 'UNKNOWN';
      const tradeData = {
        ticker: `${stockSymbol}${formData.expirydate.replace(/-/g, '')}${formData.call_put.charAt(0)}${formData.strike}`,
        type: formData.call_put,
        expiryDate: formData.expirydate,
        strike: parseFloat(formData.strike),
        quantity: formData.debit_credit === 'Credit' ? -Math.abs(parseInt(formData.quantity)) : Math.abs(parseInt(formData.quantity)),
        cost: parseFloat(formData.premium),
        stock: stockSymbol,
        strategyId: strategyMetadata?.id || null,
        // Additional fields for compatibility
        call_put: formData.call_put,
        premium: parseFloat(formData.premium),
        debit_credit: formData.debit_credit
      };
      
      // Call the add trade function
      await onAddTrade(tradeData);
      
      // Reset form and close modal
      setFormData({
        call_put: 'Call',
        strike: '',
        expirydate: '',
        quantity: '',
        premium: '',
        debit_credit: 'Debit'
      });
      setErrors({});
      onClose();
    } catch (error) {
      console.error('Error adding trade:', error);
      setErrors({ submit: 'Failed to add trade. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({
        call_put: 'Call',
        strike: '',
        expirydate: '',
        quantity: '',
        premium: '',
        debit_credit: 'Debit'
      });
      setErrors({});
      onClose();
    }
  };

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      onClick={handleBackdropClick}
    >
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Modal Header */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Add Trade to Strategy
            </h3>
            <button
              onClick={handleClose}
              disabled={isSubmitting}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Strategy Info */}
          {strategyMetadata && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-800">
                <strong>Strategy:</strong> {strategyMetadata.name} ({strategyMetadata.stockSymbol})
              </p>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Option Type */}
            <div>
              <label htmlFor="call_put" className="block text-sm font-medium text-gray-700 mb-1">
                Option Type *
              </label>
              <select
                id="call_put"
                name="call_put"
                value={formData.call_put}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
              >
                <option value="Call">Call</option>
                <option value="Put">Put</option>
              </select>
            </div>

            {/* Strike Price */}
            <div>
              <label htmlFor="strike" className="block text-sm font-medium text-gray-700 mb-1">
                Strike Price *
              </label>
              <input
                type="text"
                id="strike"
                name="strike"
                value={formData.strike}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.strike ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="e.g., 100.00"
                disabled={isSubmitting}
              />
              {errors.strike && (
                <p className="mt-1 text-sm text-red-600">{errors.strike}</p>
              )}
            </div>

            {/* Expiry Date */}
            <div>
              <label htmlFor="expirydate" className="block text-sm font-medium text-gray-700 mb-1">
                Expiry Date *
              </label>
              <input
                type="date"
                id="expirydate"
                name="expirydate"
                value={formData.expirydate}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.expirydate ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={isSubmitting}
              />
              {errors.expirydate && (
                <p className="mt-1 text-sm text-red-600">{errors.expirydate}</p>
              )}
            </div>

            {/* Position Type */}
            <div>
              <label htmlFor="debit_credit" className="block text-sm font-medium text-gray-700 mb-1">
                Position Type *
              </label>
              <select
                id="debit_credit"
                name="debit_credit"
                value={formData.debit_credit}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSubmitting}
              >
                <option value="Debit">Long (Debit)</option>
                <option value="Credit">Short (Credit)</option>
              </select>
            </div>

            {/* Quantity */}
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                Quantity *
              </label>
              <input
                type="text"
                id="quantity"
                name="quantity"
                value={formData.quantity}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.quantity ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="e.g., 1"
                disabled={isSubmitting}
              />
              {errors.quantity && (
                <p className="mt-1 text-sm text-red-600">{errors.quantity}</p>
              )}
            </div>

            {/* Premium */}
            <div>
              <label htmlFor="premium" className="block text-sm font-medium text-gray-700 mb-1">
                Premium *
              </label>
              <input
                type="text"
                id="premium"
                name="premium"
                value={formData.premium}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.premium ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="e.g., 5.50"
                disabled={isSubmitting}
              />
              {errors.premium && (
                <p className="mt-1 text-sm text-red-600">{errors.premium}</p>
              )}
            </div>

            {/* Submit Error */}
            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600">{errors.submit}</p>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Adding...
                  </div>
                ) : (
                  'Add Trade'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default TradeAddModal;
