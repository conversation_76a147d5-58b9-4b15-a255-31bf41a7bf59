const statementParser = require('../src/server-utils/statement-parser');
const fs = require('fs');
const path = require('path');

async function debugTradeExtraction() {
  console.log('🔍 DEBUGGING TRADE CONFIRMATION EXTRACTION');
  console.log('==========================================');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, '../import/8453525352545020250826.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    const password = '6369';
    
    console.log('📄 Parsing PDF...');
    const statement = await statementParser.parseStatement(pdfBuffer, password);
    
    console.log('\n📊 STATEMENT HEADER:');
    console.log('Account:', statement.header.accountHolder);
    console.log('Account Number:', statement.header.accountNumber);
    console.log('Statement Date:', statement.header.statementDate);
    
    console.log('\n🎯 TRADE CONFIRMATION SECTION:');
    console.log('Total trades found:', statement.tradeConfirmation.length);
    
    // Group trades by date
    const tradesByDate = {};
    statement.tradeConfirmation.forEach(trade => {
      if (!tradesByDate[trade.date]) {
        tradesByDate[trade.date] = [];
      }
      tradesByDate[trade.date].push(trade);
    });
    
    console.log('\n📅 TRADES BY DATE:');
    Object.keys(tradesByDate).sort().forEach(date => {
      console.log(`${date}: ${tradesByDate[date].length} trades`);
      tradesByDate[date].forEach((trade, idx) => {
        console.log(`  ${idx + 1}. Order: ${trade.orderNo}, Market: ${trade.market}, Instrument: ${trade.instrument}, Qty: ${trade.quantity}`);
      });
    });
    
    // Filter trades that match statement date (26 AUG 2025 = 2025-08-26)
    const statementDate = statement.header.statementDate;
    const matchingTrades = statement.tradeConfirmation.filter(trade => trade.date === statementDate);
    
    console.log(`\n✅ TRADES MATCHING STATEMENT DATE (${statementDate}):`);
    console.log(`Found ${matchingTrades.length} trades`);
    
    matchingTrades.forEach((trade, idx) => {
      console.log(`\nTrade ${idx + 1}:`);
      console.log(`  Date: ${trade.date}`);
      console.log(`  Order No: ${trade.orderNo}`);
      console.log(`  Market: ${trade.market}`);
      console.log(`  Instrument: ${trade.instrument}`);
      console.log(`  Expiry: ${trade.expiry}`);
      console.log(`  Quantity: ${trade.quantity}`);
      console.log(`  Strike Price: ${trade.strikePrice}`);
      console.log(`  Option Type: ${trade.optionType}`);
      console.log(`  Premium: ${trade.premium}`);
      console.log(`  Commission: ${trade.commission}`);
      console.log(`  Fees: ${trade.fees}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugTradeExtraction();
