import React, { useState, useRef, useEffect, useCallback } from 'react';
import useStrategyWindowStore from '../../store/useStrategyWindowStore';
import { useStrategyStore } from '../../store/useStrategyStore';
import StrategyHeader from './StrategyHeader';
import StrategyTabs from './StrategyTabs';

/**
 * Strategy Window Component
 * 
 * A draggable, resizable window for managing individual strategies.
 * Supports minimize/maximize/close operations and contains tabs for
 * trades management and P&L analysis.
 */
const StrategyWindow = ({ windowId }) => {
  const {
    getWindow,
    closeWindow,
    setActiveWindow,
    updateWindowPosition,
    updateWindowSize,
    toggleMinimize,
    tileOrMaximize
  } = useStrategyWindowStore();

  const window = getWindow(windowId);
  const strategyStore = useStrategyStore(window?.strategyId);
  const {
    strategyMetadata,
    activeTab,
    setActiveTab,
    initialize,
    updateStrategyMetadata,
    isLoading,
    error
  } = strategyStore();

  // Dragging state
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isResizing, setIsResizing] = useState(false);
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 });

  // Refs
  const windowRef = useRef(null);
  const headerRef = useRef(null);

  // Mouse event handlers
  const handleMouseMove = useCallback((e) => {
    if (isDragging && window) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;

      // Constrain to viewport (use document.documentElement for viewport size)
      const viewportWidth = document.documentElement.clientWidth || 1200;
      const viewportHeight = document.documentElement.clientHeight || 800;
      const constrainedX = Math.max(0, Math.min(newX, viewportWidth - window.size.width));
      const constrainedY = Math.max(0, Math.min(newY, viewportHeight - window.size.height));

      updateWindowPosition(windowId, { x: constrainedX, y: constrainedY });
    }

    if (isResizing) {
      const deltaX = e.clientX - resizeStart.x;
      const deltaY = e.clientY - resizeStart.y;

      let newWidth = resizeStart.width;
      let newHeight = resizeStart.height;

      // Handle different resize directions
      if (resizeStart.direction === 'se') {
        // Southeast corner (bottom-right)
        newWidth = Math.max(400, resizeStart.width + deltaX);
        newHeight = Math.max(300, resizeStart.height + deltaY);
      } else if (resizeStart.direction === 'e') {
        // East edge (right side only)
        newWidth = Math.max(400, resizeStart.width + deltaX);
        newHeight = resizeStart.height; // Keep height unchanged
      }

      updateWindowSize(windowId, { width: newWidth, height: newHeight });
    }
  }, [isDragging, isResizing, dragOffset, resizeStart, windowId, updateWindowPosition, updateWindowSize, window]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
  }, []);

  // Initialize strategy data when component mounts
  useEffect(() => {
    if (window?.strategyId) {
      initialize();
    }
  }, [window?.strategyId, initialize]);

  // Add global mouse event listeners
  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  // Handle window activation
  const handleWindowClick = () => {
    setActiveWindow(windowId);
  };

  // Handle close
  const handleClose = () => {
    closeWindow(windowId);
  };

  // Handle minimize/restore
  const handleMinimize = () => {
    toggleMinimize(windowId);
  };

  // Handle tile/maximize
  const handleTileOrMaximize = () => {
    tileOrMaximize(windowId);
  };

  // Dragging handlers
  const handleMouseDown = (e) => {
    if (e.target.closest('.window-controls') || e.target.closest('.resize-handle')) {
      return;
    }

    setActiveWindow(windowId);
    setIsDragging(true);
    
    const rect = windowRef.current.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };



  // Resize handlers
  const handleResizeStart = (e, direction = 'se') => {
    e.stopPropagation();
    setIsResizing(true);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: window.size.width,
      height: window.size.height,
      direction
    });
  };

  // Handle right edge resize
  const handleRightResizeStart = (e) => {
    handleResizeStart(e, 'e');
  };



  // Early returns after all hooks
  if (!window) {
    return null;
  }

  // Don't render if minimized
  if (window.isMinimized) {
    return null;
  }

  const windowStyle = {
    position: 'fixed',
    left: window.position.x,
    top: window.position.y,
    width: window.size.width,
    height: window.size.height,
    zIndex: window.zIndex,
    backgroundColor: 'white',
    border: '1px solid #d1d5db',
    borderRadius: '8px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden'
  };

  return (
    <div
      ref={windowRef}
      style={windowStyle}
      onClick={handleWindowClick}
      className="strategy-window"
    >
      {/* Window Header */}
      <div
        ref={headerRef}
        className="window-header bg-gray-50 border-b border-gray-200 px-4 py-2 cursor-move select-none"
        onMouseDown={handleMouseDown}
      >
        <StrategyHeader
          window={window}
          strategyMetadata={strategyMetadata}
          onClose={handleClose}
          onMinimize={handleMinimize}
          onTileOrMaximize={handleTileOrMaximize}
          onUpdateStrategy={updateStrategyMetadata}
          isLoading={isLoading}
          error={error}
        />
      </div>

      {/* Window Content */}
      <div className="window-content flex-1 overflow-hidden">
        {error ? (
          <div className="p-4 bg-red-50 border border-red-200 m-4 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Error loading strategy
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  {error}
                </div>
              </div>
            </div>
          </div>
        ) : isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-600">Loading strategy...</span>
            </div>
          </div>
        ) : (
          <StrategyTabs
            strategyStore={strategyStore}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />
        )}
      </div>

      {/* Resize Handles */}
      {/* Right edge resize handle */}
      <div
        className="resize-handle-right absolute top-0 right-0 w-2 h-full cursor-ew-resize hover:bg-blue-200 transition-colors"
        onMouseDown={handleRightResizeStart}
        style={{
          background: 'transparent',
        }}
        title="Resize width"
      />

      {/* Bottom-right corner resize handle */}
      <div
        className="resize-handle absolute bottom-0 right-0 w-4 h-4 cursor-se-resize"
        onMouseDown={(e) => handleResizeStart(e, 'se')}
        style={{
          background: 'linear-gradient(-45deg, transparent 0%, transparent 40%, #9ca3af 40%, #9ca3af 60%, transparent 60%)',
        }}
        title="Resize width and height"
      />
    </div>
  );
};

export default StrategyWindow;
