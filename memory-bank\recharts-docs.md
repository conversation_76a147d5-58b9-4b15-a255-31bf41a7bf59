# Recharts Documentation

## Basic Usage

```jsx
<LineChart width={400} height={400} data={data}>
  <XAxis dataKey="name" />
  <Tooltip />
  <CartesianGrid stroke="#f5f5f5" />
  <Line type="monotone" dataKey="uv" stroke="#ff7300" />
  <Line type="monotone" dataKey="pv" stroke="#387908" />
</LineChart>
```

## Responsive Container

```jsx
<ResponsiveContainer width="100%" height={400}>
  <LineChart {...args}>
    <Line dataKey="uv" />
  </LineChart>
</ResponsiveContainer>
```

## Customizing Chart Elements

### Customizing Axis Ticks

```jsx
const CustomizedAxisTick = (...args) => {
  const { x, y, stroke, payload } = args[0];
  return (
    <g transform={`translate(${x},${y})`}>
      <text 
        x={0} 
        y={0} 
        dy={16} 
        textAnchor="end" 
        fill="#666" 
        transform="rotate(-35)"
        style={{ fontSize: '10px' }}
      >
        {payload.value}
      </text>
    </g>
  );
};

// Usage
<XAxis dataKey="name" tick={<CustomizedAxisTick />} />
```

### Customizing Tooltip

```jsx
const CustomTooltip = ({ payload, label, active }) => {
  if (active) {
    return (
      <div className="custom-tooltip" style={{ fontSize: '10px' }}>
        <p className="label">{`${label} : ${payload[0].value}`}</p>
        <p className="desc">Custom tooltip content</p>
      </div>
    );
  }
  return null;
};

// Usage
<Tooltip content={<CustomTooltip />} />
```

## Font Size Customization

### XAxis and YAxis

```jsx
<XAxis 
  dataKey="name" 
  style={{ fontSize: '10px' }}
  tick={{ fontSize: '10px' }}
  label={{ value: 'Price', position: 'insideBottom', offset: -5, fontSize: '10px' }}
/>

<YAxis
  style={{ fontSize: '10px' }}
  tick={{ fontSize: '10px' }}
  label={{ value: 'PROFIT / LOSS', angle: -90, position: 'insideLeft', fontSize: '10px' }}
/>
```

### Legend and Tooltip

```jsx
<Legend 
  wrapperStyle={{ fontSize: '10px' }}
  iconSize={8}
/>

<Tooltip 
  wrapperStyle={{ fontSize: '10px' }}
  itemStyle={{ fontSize: '10px' }}
  labelStyle={{ fontSize: '10px' }}
/>
```

### Chart Title and Labels

```jsx
<text 
  x={width / 2} 
  y={10} 
  textAnchor="middle" 
  style={{ fontSize: '12px', fontWeight: 'bold' }}
>
  Chart Title
</text>
```

## Accessibility

```jsx
<LineChart
  data={data}
  title="Line chart showing values"
  accessibilityLayer
>
  {/* Chart components */}
</LineChart>
```
