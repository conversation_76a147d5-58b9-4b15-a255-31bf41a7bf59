import React, { useState, useEffect } from 'react';

/**
 * Direct Test Page
 *
 * This page directly tests the API endpoint for HTI trades with expiry 2025-06.
 */
const DirectTestPage = () => {
  const [trades, setTrades] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTradesDirectly = async () => {
      try {
        setLoading(true);

        // Get HTI trades with expiry 2025-06 from the API
        const response = await fetch('/api/firebase-trades-by-expiry?expiryDate=2025-06&stock=HTI');

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log('API response:', data);

        // Process trades to ensure futures are correctly identified
        const processedTrades = data.trades.map(trade => {
          // Check if this is a futures trade based on various indicators
          const isFuture =
            trade.type === 'Future' ||
            trade.type === 'future' ||
            trade.strike === 'N/A' ||
            (trade.ticker && trade.ticker.length <= 5);

          if (isFuture) {
            return {
              ...trade,
              type: 'Future',
              strike: 'N/A'
            };
          }

          return trade;
        });

        console.log('Processed trades:', processedTrades);
        setTrades(processedTrades);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching trades:', error);
        setError(error.message);
        setLoading(false);
      }
    };

    fetchTradesDirectly();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">Direct Firestore Test: HTI Trades for 2025-06</h1>

      {loading ? (
        <p>Loading...</p>
      ) : error ? (
        <p className="text-red-500">Error: {error}</p>
      ) : (
        <div>
          <h2 className="text-xl font-semibold mb-2">Found {trades.length} trades</h2>

          <div className="bg-gray-100 p-4 rounded mb-4">
            <h3 className="font-bold mb-2">Raw Firestore Data:</h3>
            <pre className="whitespace-pre-wrap">{JSON.stringify(trades, null, 2)}</pre>
          </div>

          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticker</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Strike</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {trades.map((trade) => (
                <tr key={trade.id} className={
                  trade.type === 'Future' || trade.type === 'future' || trade.ticker === 'HTIM5' ? 'bg-yellow-50' :
                  trade.type === 'Call' || trade.type === 'call' ? 'bg-cyan-50' :
                  trade.type === 'Put' || trade.type === 'put' ? 'bg-red-50' :
                  'bg-gray-50'
                }>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{trade.ticker}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{trade.type}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{trade.expiry}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {trade.ticker === 'HTIM5' || trade.strike === 'N/A' ? 'N/A' : trade.strike}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{trade.quantity}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{trade.price}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default DirectTestPage;
