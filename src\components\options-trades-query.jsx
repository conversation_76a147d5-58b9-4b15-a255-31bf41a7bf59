import { useState } from 'react';
import DatabaseOfflineIndicator from './database-offline-indicator';

/**
 * Options Trades Query Component
 *
 * This component displays options trades in a read-only table format,
 * with call options on the left, put options on the right, and strike prices in the middle.
 * It uses the filtered positions from the position table instead of fetching trades separately.
 *
 * The component is now linked to the position table and only displays what is showing in the position table.
 * Trades are grouped by expiry date in an accordion-style table.
 */
const OptionsTradesQuery = ({
  positions = [],
  symbol = '',
  selectedPrice = 0,
  volatility = 15,
  daysToVisualize = 0,
  riskFreeRate = 0,
  stockSymbol = null // Stock symbol for filtering (first 3 chars of ticker)
}) => {
  // Debug log for props
  console.log(`OptionsTradesQuery props - positions: ${positions.length}, stockSymbol: ${stockSymbol}`);

  // Log all positions for debugging
  positions.forEach(position => {
    console.log(`OptionsTradesQuery - Position ${position.id}: ticker=${position.ticker}, stock=${position.stock}`);
  });
  // Use positions directly instead of fetching trades
  // Filter by stock symbol if provided
  const filteredTrades = stockSymbol
    ? positions.filter(position => {
        // Always ensure we have a stock field
        if (!position.stock && position.ticker) {
          // If stock field is missing but ticker is available, set it
          position.stock = position.ticker.substring(0, 3);
          console.log(`OptionsTradesQuery - Setting missing stock field for position ${position.id}: ticker=${position.ticker}, new stock=${position.stock}`);
        }

        // Use the stock field if available, otherwise extract from ticker
        const positionStock = position.stock || (position.ticker ? position.ticker.substring(0, 3) : null);

        // Check if stockSymbol is null or undefined
        if (!stockSymbol) {
          console.log(`OptionsTradesQuery - WARNING: stockSymbol is ${stockSymbol}, cannot filter properly`);
          return true; // Include all positions if stockSymbol is not set
        }

        // Use the same consistent matching approach as in positions-table.jsx
        // 1. Normalize both strings for comparison (uppercase)
        // Make sure positionStock is a string before calling toUpperCase()
        const normalizedPositionStock = typeof positionStock === 'string' ? positionStock.toUpperCase() : '';
        const normalizedStockSymbol = typeof stockSymbol === 'string' ? stockSymbol.toUpperCase() : '';

        // 2. Match on the first 3 characters to be consistent
        // Make sure we have at least 3 characters, otherwise use the full string
        const positionStockPrefix = normalizedPositionStock.substring(0, Math.min(3, normalizedPositionStock.length));
        const stockSymbolPrefix = normalizedStockSymbol.substring(0, Math.min(3, normalizedStockSymbol.length));
        const stockMatch = positionStockPrefix === stockSymbolPrefix;

        console.log(`OptionsTradesQuery - Position ${position.id}: ticker=${position.ticker}, stock=${positionStock}, stockSymbol=${stockSymbol}, stockMatch=${stockMatch}`);

        return stockMatch;
      })
    : positions;

  console.log(`OptionsTradesQuery - Filtered trades: ${filteredTrades.length} of ${positions.length} positions`);

  // Format date for display (e.g., "YY-MM-DD" from "YYYY-MM-DD")
  const formatDate = (dateString) => {
    if (!dateString) return '';

    // For YYYY-MM format (monthly expiry - end of month)
    if (dateString.length === 7) {
      const [year, month] = dateString.split('-');
      // Get the last day of the month
      const lastDay = new Date(parseInt(year), parseInt(month), 0).getDate();
      return `${year.substring(2)}-${month}-${lastDay} (EOM)`;
    }

    // For YYYY-MM-DD format
    if (dateString.length >= 10) {
      const [year, month, day] = dateString.split('-');
      return `${year.substring(2)}-${month}-${day}`;
    }

    return dateString;
  };

  // Group positions by expiry date
  const groupPositionsByExpiryDate = () => {
    const grouped = {};

    // Get all unique expiry dates
    const expiryDates = [...new Set(filteredTrades
      .filter(position => position.ExpiryDate)
      .map(position => position.ExpiryDate))]
      .sort((a, b) => {
        // Special case: If one is a month-only format (YYYY-MM) and the other has the same year/month prefix,
        // the month-only format should come last (as it implies end of month)
        if (a.length === 7 && b.startsWith(a)) {
          return 1; // a (month format) comes after b
        }
        if (b.length === 7 && a.startsWith(b)) {
          return -1; // b (month format) comes after a
        }

        // For different months or years, or same format, use standard string comparison
        return a.localeCompare(b);
      });

    // Initialize grouped object with positions for each expiry date
    expiryDates.forEach(expiryDate => {
      grouped[expiryDate] = filteredTrades.filter(position =>
        position.ExpiryDate === expiryDate
      );
    });

    return { grouped, expiryDates };
  };

  // Group positions by expiry date
  const { grouped: groupedByExpiryDate, expiryDates } = groupPositionsByExpiryDate();

  // State to track which expiry dates are expanded
  // Auto-expand the first expiry date if available
  const [expandedExpiryDates, setExpandedExpiryDates] = useState(() => {
    if (expiryDates.length > 0) {
      return { [expiryDates[0]]: true };
    }
    return {};
  });

  // Group trades by strike price and type (call/put) for a specific expiry date
  const groupTradesByStrike = (positionsForExpiry) => {
    const grouped = {};

    // Get all unique strike prices, filtering out 'N/A' for Future trades
    const strikes = [...new Set(positionsForExpiry
      .filter(position => position.type !== 'Future' && position.strike !== 'N/A')
      .map(position => position.strike))]
      .sort((a, b) => a - b);

    // Initialize grouped object with empty arrays for each strike
    strikes.forEach(strike => {
      grouped[strike] = {
        calls: [],
        puts: []
      };
    });

    // Group positions by strike and type
    positionsForExpiry.forEach(position => {
      // Skip Future trades as they don't have a strike price
      if (position.type === 'Future' || position.strike === 'N/A') {
        return;
      }

      if (position.type === 'Call') {
        grouped[position.strike].calls.push(position);
      } else if (position.type === 'Put') {
        grouped[position.strike].puts.push(position);
      }
    });

    return { grouped, strikes };
  };

  // Calculate totals for calls and puts for a specific expiry date
  const calculateTotalsForExpiry = (positionsForExpiry) => {
    const callTotal = {
      quantity: 0,
      pnl: 0
    };

    const putTotal = {
      quantity: 0,
      pnl: 0
    };

    positionsForExpiry.forEach(position => {
      if (position.type === 'Call') {
        callTotal.quantity += position.quantity;
        callTotal.pnl += position.debitCredit;
      } else if (position.type === 'Put') {
        putTotal.quantity += position.quantity;
        putTotal.pnl += position.debitCredit;
      }
    });

    return { callTotal, putTotal };
  };

  // Toggle expansion of an expiry date section
  const toggleExpiryDateExpansion = (expiryDate) => {
    setExpandedExpiryDates(prev => ({
      ...prev,
      [expiryDate]: !prev[expiryDate]
    }));
  };



  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden">
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold text-gray-800">
            {symbol} Options Trades
            <span className="ml-2 text-xs text-gray-500">
              @{selectedPrice ? selectedPrice.toLocaleString() : '0'}, IV: {volatility}%, {daysToVisualize > 0 ? new Date(new Date().getTime() + daysToVisualize * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]}, RFR: {riskFreeRate}% ({filteredTrades.length} positions)
            </span>
          </h2>
          <DatabaseOfflineIndicator />
        </div>

        {filteredTrades.length === 0 ? (
          <div className="text-center py-2">
            <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 rounded">
              No positions to display. Add positions in the positions table above.
            </span>
          </div>
        ) : (
          <div className="space-y-2">
            {/* Accordion sections for each expiry date */}
            {expiryDates.map(expiryDate => {
              const positionsForExpiry = groupedByExpiryDate[expiryDate];
              const { grouped, strikes } = groupTradesByStrike(positionsForExpiry);
              const { callTotal, putTotal } = calculateTotalsForExpiry(positionsForExpiry);
              const isExpanded = expandedExpiryDates[expiryDate] || false;

              return (
                <div key={expiryDate} className="border border-gray-200 rounded overflow-hidden">
                  {/* Accordion Header */}
                  <div
                    className="flex justify-between items-center p-2 bg-gray-100 cursor-pointer hover:bg-gray-200"
                    onClick={() => toggleExpiryDateExpansion(expiryDate)}
                  >
                    <div className="font-bold text-sm">
                      Expiry: {formatDate(expiryDate)}
                      <span className="ml-2 text-xs text-gray-500">
                        ({positionsForExpiry.length} positions)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="text-xs mr-4">
                        <span className="bg-cyan-100 px-1 py-0.5 rounded mr-1">
                          Calls: {positionsForExpiry.filter(p => p.type === 'Call').length}
                        </span>
                        <span className="bg-red-100 px-1 py-0.5 rounded">
                          Puts: {positionsForExpiry.filter(p => p.type === 'Put').length}
                        </span>
                      </div>
                      <svg
                        className={`w-4 h-4 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </div>
                  </div>

                  {/* Accordion Content - Only shown when expanded */}
                  {isExpanded && (
                    <div className="p-2">
                      {/* Future Trades Section (if any) - Now at the top */}
                      {positionsForExpiry.some(p => p.type === 'Future') && (
                        <div className="mb-3">
                          <div className="bg-yellow-100 p-1 text-center font-bold text-xs rounded">
                            FUTURES
                          </div>
                          <div className="grid grid-cols-4 gap-0 mb-1 text-center font-bold border-b border-gray-300 text-xs">
                            <div className="p-1 border-r border-gray-300">Ticker</div>
                            <div className="p-1 border-r border-gray-300">Qty</div>
                            <div className="p-1 border-r border-gray-300">Price</div>
                            <div className="p-1">debitCredit</div>
                          </div>
                          {positionsForExpiry.filter(p => p.type === 'Future').map(future => (
                            <div key={future.id} className="grid grid-cols-4 gap-0 text-center border-b border-gray-200 text-xs bg-yellow-50">
                              <div className="p-1 border-r border-gray-300">{future.ticker}</div>
                              <div className="p-1 border-r border-gray-300">{future.quantity}</div>
                              <div className="p-1 border-r border-gray-300">{future.premium.toFixed(2)}</div>
                              <div className="p-1">{future.debitCredit.toFixed(2)}</div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Options Section */}
                      {strikes.length > 0 ? (
                        <>
                          {/* Table Header */}
                          <div className="grid grid-cols-7 gap-0 mb-1 text-center font-bold text-xs">
                            <div className="col-span-3 bg-cyan-500 p-1 text-white">CALL</div>
                            <div className="col-span-1 bg-gray-200 p-1">Strike</div>
                            <div className="col-span-3 bg-red-300 p-1">PUT</div>
                          </div>

                          {/* Column Headers */}
                          <div className="grid grid-cols-7 gap-0 mb-1 text-center font-bold border-b border-gray-300 text-xs">
                            <div className="p-1 border-r border-gray-300">Qty</div>
                            <div className="p-1 border-r border-gray-300">Prem</div>
                            <div className="p-1 border-r border-gray-300">debitCredit</div>
                            <div className="p-1 border-r border-gray-300">Strike</div>
                            <div className="p-1 border-r border-gray-300">Qty</div>
                            <div className="p-1 border-r border-gray-300">Prem</div>
                            <div className="p-1">debitCredit</div>
                          </div>

                          {/* Table Rows */}
                          {strikes.map(strike => (
                            <div key={strike} className="grid grid-cols-7 gap-0 text-center border-b border-gray-200 text-xs">
                              {/* Call Side */}
                              <div className="p-1 border-r border-gray-300">
                                {grouped[strike].calls.reduce((sum, trade) => sum + trade.quantity, 0) || ''}
                              </div>
                              <div className="p-1 border-r border-gray-300">
                                {grouped[strike].calls.length > 0 ?
                                  grouped[strike].calls[0].premium.toFixed(2) : ''}
                              </div>
                              <div className="p-1 border-r border-gray-300">
                                {grouped[strike].calls.reduce((sum, trade) => sum + trade.debitCredit, 0).toFixed(2) || ''}
                              </div>

                              {/* Strike */}
                              <div className="p-1 border-r border-gray-300 font-bold bg-gray-100">
                                {typeof strike === 'number' ? strike.toFixed(0) : strike}
                              </div>

                              {/* Put Side */}
                              <div className="p-1 border-r border-gray-300">
                                {grouped[strike].puts.reduce((sum, trade) => sum + trade.quantity, 0) || ''}
                              </div>
                              <div className="p-1 border-r border-gray-300">
                                {grouped[strike].puts.length > 0 ?
                                  grouped[strike].puts[0].premium.toFixed(2) : ''}
                              </div>
                              <div className="p-1">
                                {grouped[strike].puts.reduce((sum, trade) => sum + trade.debitCredit, 0).toFixed(2) || ''}
                              </div>
                            </div>
                          ))}

                          {/* Totals Row */}
                          <div className="grid grid-cols-7 gap-0 text-center font-bold bg-gray-100 text-xs">
                            <div className="p-1 border-r border-gray-300">{callTotal.quantity}</div>
                            <div className="p-1 border-r border-gray-300"></div>
                            <div className="p-1 border-r border-gray-300">{callTotal.pnl.toFixed(2)}</div>
                            <div className="p-1 border-r border-gray-300">Total</div>
                            <div className="p-1 border-r border-gray-300">{putTotal.quantity}</div>
                            <div className="p-1 border-r border-gray-300"></div>
                            <div className="p-1">{putTotal.pnl.toFixed(2)}</div>
                          </div>
                        </>
                      ) : (
                        !positionsForExpiry.some(p => p.type === 'Future') && (
                          <div className="text-center py-2 text-xs text-gray-500">
                            No positions for this expiry date.
                          </div>
                        )
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default OptionsTradesQuery;
