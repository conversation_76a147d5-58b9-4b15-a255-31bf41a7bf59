/**
 * Node.js-compatible holiday loader
 * This version reads directly from the XML file instead of localStorage
 */

const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

/**
 * Load exchange holidays for Node.js environment
 * @returns {Date[]} Array of holiday dates
 */
function loadExchangeHolidaysNodeJS() {
  try {
    console.log('🔍 Loading holidays in Node.js environment...');
    
    // Try to read the XML file directly
    const xmlFilePath = path.join(__dirname, '../../output/hkex_holidays.xml');
    
    if (!fs.existsSync(xmlFilePath)) {
      console.warn('❌ XML file not found:', xmlFilePath);
      return getFallbackHolidaysNodeJS();
    }
    
    const xmlContent = fs.readFileSync(xmlFilePath, 'utf8');
    console.log('✅ XML file loaded successfully');
    
    // Use JSDOM for Node.js XML parsing
    const dom = new JSDOM(xmlContent, { contentType: 'text/xml' });
    const document = dom.window.document;
    
    // Check for parsing errors
    const parseError = document.querySelector('parsererror');
    if (parseError) {
      throw new Error('XML parsing error: ' + parseError.textContent);
    }
    
    const holidays = [];
    const rows = document.querySelectorAll('tr');
    
    console.log(`🔍 Found ${rows.length} table rows in XML`);
    
    for (const row of rows) {
      const cells = row.querySelectorAll('td');
      if (cells.length > 0) {
        // Get the first cell which should contain the date
        const dateText = cells[0].textContent.trim();
        
        // Extract date part (before any space)
        const datePart = dateText.split(' ')[0];
        
        // Parse date in DD/MM/YYYY format
        if (datePart && datePart.includes('/')) {
          try {
            const [day, month, year] = datePart.split('/');
            // Create date using local timezone, set to noon to avoid timezone issues
            const holidayDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day), 12, 0, 0, 0);
            holidays.push(holidayDate);
          } catch (e) {
            console.warn('Could not parse holiday date:', datePart);
          }
        }
      }
    }
    
    console.log(`✅ Loaded ${holidays.length} exchange holidays from XML file`);
    
    // Verify the critical date is included
    const oct7_2025 = holidays.find(h => 
      h.getFullYear() === 2025 && 
      h.getMonth() === 9 && 
      h.getDate() === 7
    );
    
    if (oct7_2025) {
      console.log('✅ 2025-10-07 found in holidays (Mid-Autumn Festival following day)');
    } else {
      console.warn('⚠️ 2025-10-07 NOT found in XML holidays');
    }
    
    return holidays;
    
  } catch (error) {
    console.error('❌ Error loading exchange holidays from XML:', error.message);
    console.log('🔄 Falling back to hardcoded holidays...');
    return getFallbackHolidaysNodeJS();
  }
}

/**
 * Node.js version of fallback holidays
 * @returns {Date[]} Array of holiday dates
 */
function getFallbackHolidaysNodeJS() {
  const currentYear = new Date().getFullYear();
  
  // If we're in 2025, use the actual 2025 HKEX holidays
  if (currentYear === 2025) {
    const holidays2025 = [
      new Date(2025, 0, 1),   // New Year's Day (1/1/2025)
      new Date(2025, 0, 29),  // Lunar New Year's Day (29/1/2025)
      new Date(2025, 0, 30),  // The second day of Lunar New Year (30/1/2025)
      new Date(2025, 0, 31),  // The third day of Lunar New Year (31/1/2025)
      new Date(2025, 3, 4),   // Ching Ming Festival (4/4/2025)
      new Date(2025, 3, 18),  // Good Friday (18/4/2025)
      new Date(2025, 3, 21),  // Easter Monday (21/4/2025)
      new Date(2025, 4, 1),   // Labour Day (1/5/2025)
      new Date(2025, 4, 5),   // The Birthday of the Buddha (5/5/2025)
      new Date(2025, 6, 1),   // HKSAR Establishment Day (1/7/2025)
      new Date(2025, 9, 1),   // National Day (1/10/2025)
      new Date(2025, 9, 7),   // The day following the Chinese Mid-Autumn Festival (7/10/2025) ⭐
      new Date(2025, 9, 29),  // Chung Yeung Festival (29/10/2025)
      new Date(2025, 11, 25)  // Christmas Day (25/12/2025)
    ];
    
    // Set all dates to noon to avoid timezone issues
    holidays2025.forEach(holiday => {
      holiday.setHours(12, 0, 0, 0);
    });
    
    console.log(`✅ Using 2025 HKEX fallback holidays (${holidays2025.length} holidays) [Node.js]`);
    return holidays2025;
  }
  
  // For other years, use generic fallback
  const fallbackHolidays = [
    new Date(currentYear, 0, 1),   // New Year's Day
    new Date(currentYear, 4, 1),   // Labour Day
    new Date(currentYear, 6, 1),   // HKSAR Establishment Day
    new Date(currentYear, 9, 1),   // National Day
    new Date(currentYear, 11, 25), // Christmas Day
    new Date(currentYear, 11, 26)  // Boxing Day
  ];

  // Set all dates to noon to avoid timezone issues
  fallbackHolidays.forEach(holiday => {
    holiday.setHours(12, 0, 0, 0);
  });

  console.log(`✅ Using fallback holidays for ${currentYear} (${fallbackHolidays.length} holidays) [Node.js]`);
  return fallbackHolidays;
}

/**
 * Environment-aware holiday loader
 * Automatically detects if running in Node.js or browser and uses appropriate method
 */
function loadExchangeHolidaysUniversal() {
  // Check if we're in Node.js environment
  if (typeof window === 'undefined' && typeof global !== 'undefined') {
    // Node.js environment
    return loadExchangeHolidaysNodeJS();
  } else {
    // Browser environment - would need to import the browser version
    throw new Error('Browser environment detected - use loadExchangeHolidays() from position-utils.js');
  }
}

module.exports = {
  loadExchangeHolidaysNodeJS,
  getFallbackHolidaysNodeJS,
  loadExchangeHolidaysUniversal
};