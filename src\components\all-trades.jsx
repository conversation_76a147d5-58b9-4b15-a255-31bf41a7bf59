import React, { useMemo, useEffect, useState } from 'react';
import useAllTradesStore from '../store/useAllTradesStore';
import useAnalysisStore from '../store/useAnalysisStore';
import { StrategyTypes } from '../types/strategy';

/**
 * AllTrades Component (Consolidated)
 * Fetches all trades, stocks, and expiry dates from the store, and displays a full-featured table with filtering.
 * This is the new single source of truth for all trades display.
 */
const AllTrades = ({ onFiltersChange }) => {
  const { symbol: selectedStockSymbol } = useAnalysisStore();
  const { allTrades, expiryDates, isLoading, error } = useAllTradesStore();

  // Set selected stock to selectedStockSymbol if exists, otherwise set ALL
  const [selectedStock, setSelectedStock] = useState(selectedStockSymbol || 'ALL');
  const [isCreatingStrategy, setIsCreatingStrategy] = useState(false);

  // Keep selectedStock in sync with selectedStockSymbol prop
  useEffect(() => {
    setSelectedStock(selectedStockSymbol || 'ALL');
  }, [selectedStockSymbol]);
  const [selectedExpiry, setSelectedExpiry] = useState('ALL');
  const [isTableExpanded, setIsTableExpanded] = useState(false);
  // Filtering logic
  const filteredTrades = useMemo(() => {
    return allTrades.filter(trade => {
      const tradeStock = trade.stock || (trade.ticker ? trade.ticker.substring(0, 3) : null);
      const stockMatch = selectedStock === 'ALL' || tradeStock === selectedStock;
      const expiryMatch = selectedExpiry === 'ALL' || trade.ExpiryDate === selectedExpiry;
      return stockMatch && expiryMatch;
    });
  }, [allTrades, selectedStock, selectedExpiry]);

  // Notify parent component when filters change
  useEffect(() => {
    if (onFiltersChange) {
      onFiltersChange({
        selectedStock,
        selectedExpiry,
        filteredTrades
      });
    }
  }, [selectedStock, selectedExpiry, filteredTrades, onFiltersChange]);

  // Function to create a new strategy from filtered trades
  const handleCreateStrategy = async () => {
    if (filteredTrades.length === 0) {
      alert('No trades available to create a strategy. Please adjust your filters.');
      return;
    }

    setIsCreatingStrategy(true);

    try {
      // Determine stock symbol for strategy name
      const stockSymbol = selectedStock === 'ALL'
        ? (filteredTrades[0]?.stock || filteredTrades[0]?.ticker?.substring(0, 3) || 'MIXED')
        : selectedStock;

      // Generate strategy name with timestamp
      const now = new Date();
      const timestamp = now.toISOString()
        .replace('T', '_')
        .replace(/:/g, '-')
        .substring(0, 19); // YYYY-MM-DD_HH-mm-ss format

      const strategyName = `${stockSymbol}_${timestamp}`;

      // Create strategy data
      const strategyData = {
        name: strategyName,
        stockSymbol: stockSymbol,
        description: `Auto-generated strategy from ${filteredTrades.length} filtered trades`
      };

      console.log('Creating strategy with data:', strategyData);

      // Create the strategy first
      const newStrategy = {
        ...StrategyTypes.DEFAULT_STRATEGY,
        ...strategyData,
        id: StrategyTypes.generateStrategyId(),
        createdAt: new Date(),
        lastModified: new Date()
      };

      const strategyResponse = await fetch('/api/strategies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newStrategy),
      });

      if (!strategyResponse.ok) {
        throw new Error(`Failed to create strategy: ${strategyResponse.statusText}`);
      }

      const createdStrategy = await strategyResponse.json();
      console.log('Strategy created successfully:', createdStrategy);

      // Prepare trades for the strategy
      const strategyTrades = filteredTrades.map(trade => ({
        type: trade.type || trade.call_put,
        strike: parseFloat(trade.strike) || 0,
        ExpiryDate: trade.ExpiryDate || trade.expiryDate || trade.expiry,
        quantity: parseInt(trade.quantity) || 0,
        premium: parseFloat(trade.premium || trade.cost) || 0,
        debitCredit: parseFloat(trade.premium || trade.cost) * Math.abs(parseInt(trade.quantity) || 0),
        stock: trade.stock || stockSymbol,
        ticker: trade.ticker,
        // Additional fields for compatibility
        call_put: trade.type || trade.call_put,
        expiryDate: trade.ExpiryDate || trade.expiryDate || trade.expiry,
        cost: parseFloat(trade.premium || trade.cost) || 0
      }));

      console.log('Adding trades to strategy:', strategyTrades);

      // Add trades to the strategy using batch endpoint
      const tradesResponse = await fetch('/api/strategy-trades/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          strategyId: createdStrategy.id,
          trades: strategyTrades
        }),
      });

      if (!tradesResponse.ok) {
        console.error('Failed to add trades to strategy, but strategy was created');
        alert('Strategy created successfully, but failed to add trades. You can add them manually in the Strategy Planner.');
      } else {
        const tradesResult = await tradesResponse.json();
        console.log('Trades added successfully:', tradesResult);
      }

      // Navigate to strategy planner page and open the new strategy
      setTimeout(async () => {
        try {
          // Navigate to strategy planner page with the strategy ID in the URL
          window.location.href = `/strategy-planner?openStrategy=${createdStrategy.id}`;
        } catch (error) {
          console.error('Error navigating to strategy planner:', error);
          alert('Strategy created successfully! Please go to Strategy Planner to view it.');
        }
      }, 500);

    } catch (error) {
      console.error('Error creating strategy:', error);
      alert('Failed to create strategy. Please try again.');
    } finally {
      setIsCreatingStrategy(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
        {/* <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold text-gray-800">
            Trades List for {selectedStock || 'All Symbols'}
            <span className="ml-2 text-xs text-gray-500">
               ( {filteredTrades.length} of {(allTrades || []).length} trades )            
            </span>
          </h2>
        </div> */}

        {isLoading ? (
          <div className="text-center py-4">
            <p className="text-gray-500">Loading trades...</p>
          </div>
        ) : error ? (
          <div className="text-center py-4">
            <p className="text-red-500">{error}</p>
          </div>        ) : (
          <div className="overflow-x-auto">
            <div className="p-4 border-b border-gray-200">
              <div className="flex flex-wrap gap-4 items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <label className="text-sm font-medium text-gray-700 whitespace-nowrap">Select Expiry:</label>
                      <select
                        value={selectedExpiry}
                        onChange={(e) => setSelectedExpiry(e.target.value)}
                        className="border border-gray-300 rounded px-2 py-1 text-xs focus:ring-blue-500 focus:border-blue-500" 
                      >
                        <option value="ALL">All</option>
                        {Array.from(expiryDates || []).sort().map((date) => (
                          <option key={date} value={date}>{date}</option>
                        ))}
                      </select>
                      <span className="ml-2 text-xs text-gray-500">
                        ( {filteredTrades.length} of {(allTrades || []).length} trades )            
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={handleCreateStrategy}
                    disabled={isCreatingStrategy || filteredTrades.length === 0}
                    className="flex items-center gap-2 px-3 py-1 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors"
                    title={filteredTrades.length === 0 ? "No trades available to create strategy" : "Create new strategy from filtered trades"}
                  >
                    {isCreatingStrategy ? (
                      <>
                        <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Creating...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        New Strategy
                      </>
                    )}
                  </button>
                  <button
                    onClick={() => setIsTableExpanded(!isTableExpanded)}
                    className="flex items-center gap-2 px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                  >
                    {isTableExpanded ? (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                        </svg>
                        Hide Table
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                        Show Table
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
            {isTableExpanded && (
              <div className="h-[600px] overflow-y-auto border rounded-md">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ticker
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stock
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Expiry
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Strike
                      </th>
                      <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Uploaded At
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredTrades.length === 0 ? (
                      <tr>
                        <td colSpan="8" className="px-3 py-4 text-center text-sm text-gray-500">
                          {isLoading ? "Loading trades..." : error ? error : "No trades found matching the selected filters."}
                        </td>
                      </tr>
                    ) : (
                      filteredTrades.map((trade) => (
                        <tr key={trade.id || `${trade.ticker}-${trade.ExpiryDate || trade.expiry}`}
                          className={
                            trade.type === 'Future' || trade.type === 'future' ||
                            (trade.ticker && trade.ticker.length <= 5 && trade.strike === 'N/A') ? 'bg-yellow-50' :
                            trade.type === 'Call' || trade.type === 'call' ? 'bg-cyan-50' :
                            trade.type === 'Put' || trade.type === 'put' ? 'bg-red-50' :
                            'bg-gray-50'
                          }
                        >
                          <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900" title={trade.ticker}>
                            {trade.ticker}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500" title={trade.stock}>
                            {trade.stock}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                            {trade.type}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                            {trade.ExpiryDate || trade.expiry}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                            {trade.strike === 'N/A' || (trade.ticker && trade.ticker.length <= 5) ? 'N/A' : trade.strike}
                          </td>
                          <td className={`px-3 py-2 whitespace-nowrap text-sm text-right ${trade.quantity < 0 ? 'text-red-500' : 'text-green-600'}`}> 
                            {trade.quantity}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                            {trade.premium != null ? trade.premium.toFixed(2) : 'N/A'}
                          </td>
                          <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                            {trade.created_at ? new Date(trade.created_at).toLocaleDateString() : 'N/A'}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AllTrades;
