import React from 'react';

/**
 * Trade Data Table Component
 *
 * This component displays a table of trades with columns for ticker, type, expiry, strike, quantity, price, and uploaded date.
 *
 * @param {Object} props - Component props
 * @param {Array} props.trades - Array of trade objects to display
 */
const TradeDataTable = ({ trades }) => {
  return (
    <div className="overflow-x-auto">
      <div className="h-[600px] overflow-y-auto border rounded-md">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50 sticky top-0">
            <tr>
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stock
              </th>
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ticker
              </th>
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Expiry
              </th>
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Strike
              </th>
              <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Uploaded At
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {trades.length === 0 ? (
              <tr>
                <td colSpan="7" className="px-3 py-4 text-center text-sm text-gray-500">
                  No trades match the current filters, or no trades have been uploaded yet.
                </td>
              </tr>
            ) : (
              trades.map((trade) => (
                <tr key={trade.id} className={
                  trade.type === 'Call' || trade.type === 'call' ? 'bg-cyan-50' :
                  trade.type === 'Put' || trade.type === 'put' ? 'bg-red-50' :
                  trade.type === 'Future' || trade.type === 'future' ? 'bg-yellow-50' :
                  'bg-gray-50' // fallback
                }>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500" title={trade.stock}>
                    {trade.stock}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900" title={trade.ticker}>
                    {trade.ticker}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                    {trade.type}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                    {trade.ExpiryDate || trade.expiry}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                    {trade.type === 'Future' || trade.strike === 'N/A' ? 'N/A' : trade.strike}
                  </td>
                  <td className={`px-3 py-2 whitespace-nowrap text-sm text-right ${trade.quantity < 0 ? 'text-red-500' : 'text-green-600'}`}>
                    {trade.quantity}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                    {trade.premium != null ? trade.premium.toFixed(2) : 'N/A'}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                    {trade.created_at ? new Date(trade.created_at).toLocaleDateString() : 'N/A'}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      <div className="mt-2 text-xs text-gray-500">
        Showing {trades.length} trade{trades.length === 1 ? "" : "s"}.
      </div>
    </div>
  );
};

export default TradeDataTable;
