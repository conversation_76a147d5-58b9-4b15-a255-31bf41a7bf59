
import fs from 'node:fs';
import path from 'node:path';
import admin from 'firebase-admin';
import type { ServiceAccount } from 'firebase-admin';
import type { ParsedTrade } from '@/types/trade';

// Path to your service account key file.
// firebase.json is expected to be in the project root.
const serviceAccountPath = path.join(process.cwd(), 'firebase.json');
let serviceAccount: ServiceAccount | undefined;

try {
  const fileContents = fs.readFileSync(serviceAccountPath, 'utf8');
  const parsedJson = JSON.parse(fileContents);
  // Perform a basic check to ensure it looks like a service account
  if (parsedJson && typeof parsedJson.project_id === 'string' && typeof parsedJson.private_key === 'string' && typeof parsedJson.client_email === 'string') {
    serviceAccount = parsedJson as ServiceAccount;
  } else {
    console.error(
      `Parsed content from ${serviceAccountPath} does not appear to be a valid Firebase service account key. It might be missing required fields like project_id, private_key, or client_email.`
    );
  }
} catch (e: any) {
  let errorMessage = `Failed to load or parse Firebase service account key from ${serviceAccountPath}. `;
  if (e instanceof Error) {
    errorMessage += `Error: ${e.message}`;
    if (e.message.includes('ENOENT')) {
      errorMessage += ` (File not found at ${serviceAccountPath})`;
    }
  } else {
    errorMessage += `Error: ${String(e)}`;
  }
  console.error(`${errorMessage} Firebase Admin SDK will not be initialized.`);
}

let firestoreDbInstance: admin.firestore.Firestore | null = null;

if (serviceAccount) {
  if (!admin.apps.length) {
    try {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
      firestoreDbInstance = admin.firestore();
      // console.log("Firebase Admin SDK initialized successfully."); // Optional log
    } catch (initError: any) {
      console.error("Firebase Admin SDK initialization error:", initError instanceof Error ? initError.message : String(initError));
    }
  } else {
    firestoreDbInstance = admin.firestore(); // App already initialized
  }
} else {
    // Error related to serviceAccount loading/parsing is logged above.
    // This else implies serviceAccount is undefined, so db will remain null.
}

export const db = firestoreDbInstance;

export async function uploadTradesToFirebase(trades: ParsedTrade[]): Promise<{ success: boolean; count: number; error?: string }> {
  if (!db) {
    let warningMessage = "Firebase Admin SDK (db) is not initialized. Upload to Firebase will be simulated. ";
    if (!serviceAccount) {
        warningMessage += `This is likely because the service account key could not be loaded/parsed from ${serviceAccountPath}. Please ensure 'firebase.json' is in the project root and is a valid service account key. `;
    } else if (!admin.apps.length || !admin.firestore()) {
        warningMessage += "This might be due to an SDK initialization failure or Firestore service being unavailable. Check previous logs for initialization errors. ";
    } else {
        warningMessage += "The reason for db not being initialized is unclear. ";
    }
    console.warn(`${warningMessage}Simulating successful upload for ${trades.length} trades as fallback.`);
    // Simulate network delay for fallback
    await new Promise(resolve => setTimeout(resolve, 1000)); 
    return { success: true, count: trades.length, error: "Firebase not initialized, upload simulated." };
  }

  if (!trades || trades.length === 0) {
    return { success: true, count: 0, message: "No trades provided to upload." };
  }

  try {
    const tradesCollection = db.collection('trades');
    const batch = db.batch();

    trades.forEach((trade) => {
      const docRef = tradesCollection.doc(); // Auto-generate ID
      // Ensure all fields are correctly mapped and no undefined values are sent if not desired
      const tradeData: any = { ...trade };
      if (tradeData.originalLine === undefined) delete tradeData.originalLine; // Example: Firestore errors on `undefined`

      batch.set(docRef, {
        ...tradeData,
        uploadedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
    });

    await batch.commit();
    // console.log(`${trades.length} trades uploaded successfully using Admin SDK.`); // Optional success log
    return { success: true, count: trades.length };

  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error uploading trades to Firebase with Admin SDK:', errorMessage, error);
    return { success: false, count: 0, error: errorMessage };
  }
}
