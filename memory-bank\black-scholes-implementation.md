# Black-Scholes Implementation

## Overview
This document details the Black-Scholes model implementation used in the options-analyzer application, including testing for edge cases and extreme input parameters.

## Implementation Details

### Standard Implementation
- Located in `src/utils/black-scholes.js`
- Implements the standard Black-Scholes model for option pricing
- Provides accurate calculations for normal input parameters
- Validated against known test cases:
  - At stock price 22600, strike 22200, IV=30%, 3 days to expiry:
    - Call premium: ~554.81
    - Put premium: ~151.16

### Robust Implementation
- Located in `src/utils/black-scholes-robust.js`
- Enhanced version of the standard implementation
- Handles all edge cases and extreme input parameters without crashing
- Provides reasonable defaults for invalid inputs
- Maintains accuracy for valid inputs
- Includes comprehensive input validation and error handling

## Key Features of Robust Implementation

1. **Input Validation and Sanitization**
   - Validates all input parameters before calculation
   - Provides reasonable defaults for invalid inputs
   - Handles null, undefined, NaN, and Infinity values

2. **Option Type Handling**
   - Case-insensitive option type recognition
   - Supports alternative formats (e.g., 'c', 'p', 'CALL', 'PUT')
   - Defaults to 'Call' for unrecognized types

3. **Edge Case Handling**
   - Zero stock price: Returns 0 for calls, discounted strike for puts
   - Zero strike price: Returns stock price for calls, 0 for puts
   - Zero volatility: Returns intrinsic value
   - Zero time to expiry: Returns intrinsic value
   - Negative values: Properly handled with reasonable defaults
   - Very large values: Capped at reasonable limits

4. **Error Prevention**
   - Prevents division by zero
   - Handles numerical overflow/underflow
   - Provides fallback values when calculations fail

## Test Suite

### Edge Case Test Script
- Located in `test-black-scholes-edge-cases.js`
- Tests the following categories of edge cases:
  - Extreme Values: Zero, very large, and negative values
  - Null/Undefined Values: null and undefined for each parameter
  - Invalid Option Types: Various invalid option type formats
  - NaN Values: NaN values for each numeric parameter
  - Infinity Values: Infinity values for each numeric parameter

### Validation Test Script
- Located in `test-black-scholes-validation.js`
- Verifies that the implementation produces expected results for specific test cases
- Confirms accuracy of the Black-Scholes calculations

## Test Results

### Edge Case Test Results
- Original Implementation:
  - Failed 10 out of 60 test cases
  - Particularly struggled with invalid option types and null/undefined values
  - Crashed or produced NaN for some extreme values

- Robust Implementation:
  - Passed all 60 test cases
  - Successfully handled all edge cases without crashing
  - Provided reasonable values even with extreme or invalid inputs

### Validation Test Results
- Both implementations produced accurate results for the validation test case
- Call premium: 554.81
- Put premium: 151.16
- These values are within acceptable tolerance ranges of the expected values

## Implementation Recommendations

The robust Black-Scholes implementation is recommended for production use because:
1. It handles all edge cases successfully without crashing
2. It provides reasonable values even with extreme or invalid inputs
3. It maintains accuracy for valid inputs
4. It includes comprehensive input validation and error handling

## Usage Example

```javascript
const blackScholes = require('./src/utils/black-scholes-robust');

// Calculate option price with standard parameters
const price = blackScholes.calculateOptionPrice(
  'Call',           // Option type: 'Call' or 'Put'
  100,              // Stock price
  95,               // Strike price
  0.05,             // Risk-free rate (5%)
  0.2,              // Volatility (20%)
  0.25              // Time to expiry (3 months)
);

// The implementation handles edge cases gracefully
const edgeCasePrice = blackScholes.calculateOptionPrice(
  'call',           // Case-insensitive
  0,                // Zero stock price
  100,              // Strike price
  null,             // Invalid risk-free rate (defaults to 0)
  -0.1,             // Negative volatility (defaults to 0)
  Infinity          // Infinite time (capped at 30 years)
);
```

## Validation Behavior

The robust implementation applies the following validation rules:

| Parameter | Invalid Input | Default Value | Notes |
|-----------|---------------|---------------|-------|
| Option Type | null, undefined, non-string | 'Call' | Case-insensitive, accepts 'c'/'p' |
| Stock Price | null, undefined, NaN, negative | 0 | |
| Strike Price | null, undefined, NaN, negative | Current stock price or 0 | |
| Risk-Free Rate | null, undefined, NaN | 0 | Values >100% capped at 100% |
| Volatility | null, undefined, NaN, negative | 20% | Values >200% capped at 200% |
| Time to Expiry | null, undefined, NaN, negative | 0 | Values >30 years capped at 30 years |

## Key Learnings

1. The Black-Scholes model is sensitive to input parameters, and edge cases can cause calculations to fail or produce unexpected results.
2. Proper input validation is essential for preventing crashes and ensuring reliable calculations.
3. Providing reasonable defaults for invalid inputs improves user experience and prevents errors.
4. Handling edge cases gracefully makes the application more robust and reliable.
5. Zero stock price is a valid value and all components should work correctly with it.
6. Comprehensive testing is crucial for ensuring the reliability of financial calculations.
