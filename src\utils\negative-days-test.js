/**
 * Test script for verifying option premium calculations with negative days to expiry
 * 
 * This script tests the calculation of option premiums when days to expiry is negative,
 * ensuring that the model treats negative days as expired options.
 */

import { calculatePositionPremium } from './position-utils.js';
import { calculateOptionPrice } from './black-scholes-robust.js';

// Function to test premium calculation with negative days to expiry
function testNegativeDaysToExpiry() {
  console.log("=== TESTING OPTION PREMIUMS WITH NEGATIVE DAYS TO EXPIRY ===");
  
  // Test case 1: Put option with negative days to expiry (in the money)
  const putNegativeDaysPosition = {
    id: 'test-put-negative-days',
    type: 'Put',
    strike: 23300,
    daysToExpiry: -5, // 5 days past expiry
    ExpiryDate: '2024-05-20' // Date in the past
  };
  
  const putNegativeDaysPremium = calculatePositionPremium(
    putNegativeDaysPosition,
    23200, // Stock price < strike (in the money)
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  
  console.log(`Put option with negative days (${putNegativeDaysPosition.daysToExpiry}) premium: ${putNegativeDaysPremium.toFixed(2)}`);
  console.log(`Expected value: 100.00 (intrinsic value at expiry)`);
  
  // Test case 2: Call option with negative days to expiry (at the money)
  const callNegativeDaysPosition = {
    id: 'test-call-negative-days',
    type: 'Call',
    strike: 23200,
    daysToExpiry: -10, // 10 days past expiry
    ExpiryDate: '2024-05-15' // Date in the past
  };
  
  const callNegativeDaysPremium = calculatePositionPremium(
    callNegativeDaysPosition,
    23200, // Stock price = strike (at the money)
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  
  console.log(`Call option with negative days (${callNegativeDaysPosition.daysToExpiry}) premium: ${callNegativeDaysPremium.toFixed(2)}`);
  console.log(`Expected value: 0.00 (no value at expiry when stock = strike)`);
  
  // Test case 3: Put option with negative days to expiry (out of the money)
  const putOtmNegativeDaysPosition = {
    id: 'test-put-otm-negative-days',
    type: 'Put',
    strike: 23100,
    daysToExpiry: -3, // 3 days past expiry
    ExpiryDate: '2024-05-22' // Date in the past
  };
  
  const putOtmNegativeDaysPremium = calculatePositionPremium(
    putOtmNegativeDaysPosition,
    23200, // Stock price > strike (out of the money)
    2,     // Risk-free rate (2%)
    30     // Volatility (30%)
  );
  
  console.log(`Put option OTM with negative days (${putOtmNegativeDaysPosition.daysToExpiry}) premium: ${putOtmNegativeDaysPremium.toFixed(2)}`);
  console.log(`Expected value: 0.00 (no value at expiry when out of the money)`);
  
  // Test direct Black-Scholes calculation with negative time
  console.log("\n=== TESTING DIRECT BLACK-SCHOLES WITH NEGATIVE TIME ===");
  
  // Test case 4: Direct Black-Scholes for put with negative time
  const directPutNegativeTime = calculateOptionPrice(
    'Put',
    23200, // Stock price
    23300, // Strike price (in the money)
    0.02,  // Risk-free rate (2%)
    0.3,   // Volatility (30%)
    -0.01  // Negative time to expiry (-3.65 days)
  );
  
  console.log(`Direct B-S Put with negative time: ${directPutNegativeTime.toFixed(2)}`);
  console.log(`Expected value: 100.00 (intrinsic value at expiry)`);
  
  // Test case 5: Direct Black-Scholes for call with negative time
  const directCallNegativeTime = calculateOptionPrice(
    'Call',
    23200, // Stock price
    23200, // Strike price (at the money)
    0.02,  // Risk-free rate (2%)
    0.3,   // Volatility (30%)
    -0.03  // Negative time to expiry (-10.95 days)
  );
  
  console.log(`Direct B-S Call with negative time: ${directCallNegativeTime.toFixed(2)}`);
  console.log(`Expected value: 0.00 (no value at expiry when stock = strike)`);
}

// Run the tests
testNegativeDaysToExpiry();

// Export the test function for potential reuse
export { testNegativeDaysToExpiry };
