// Zustand store for stock info and analysis parameters
import { create } from 'zustand';

const defaultSymbol = 'HSI';
const defaultAnalysisParams = {
  targetStockPrice: 23000,
  volatility: 20,
  riskFreeRate: 0,
};

const useAnalysisStore = create((set, get) => ({
  // Stock Info State
  symbol: defaultSymbol,
  setSymbol: (symbol) => set({ symbol }),
  stockInfo: null,
  setStockInfo: (stockInfo) => set({ stockInfo }),
  isStockLoading: false,
  setIsStockLoading: (isLoading) => set({ isStockLoading: isLoading }),
  stockError: null,
  setStockError: (error) => set({ stockError: error }),

  // Function to fetch stock data
  fetchStockData: async (symbol) => {
    get().setIsStockLoading(true);
    get().setStockError(null);
    try {
      const response = await fetch(`/api/stock/${symbol}`);
      if (!response.ok) {
        let errorMsg = `Error fetching stock data: ${response.status} ${response.statusText}`;
        try {
            const errorData = await response.json();
            errorMsg = errorData.message || errorData.error || errorMsg;
        } catch (e) {
            // Ignore if response is not json
        }
        throw new Error(errorMsg);
      }
      const data = await response.json();
      // Assuming the data from /api/stock/:symbol is directly usable for stockInfo
      // and matches the structure previously mocked, e.g., { response: { regularMarketPrice: ... }, currency: ... }
      get().setStockInfo(data);
    } catch (error) {
      console.error("fetchStockData error:", error);
      get().setStockError(error.message || 'Failed to fetch stock data');
      get().setStockInfo(null); // Clear stock info on error
    } finally {
      get().setIsStockLoading(false);
    }
  },

  // Analysis Parameters State
  ...defaultAnalysisParams,
  setTargetStockPrice: (v) => set({ targetStockPrice: v }),
  setDateToVisualize: (v) => set({ dateToVisualize: v }),
  setVolatility: (v) => set({ volatility: v }),
  setRiskFreeRate: (v) => set({ riskFreeRate: v }),
  daysToVisualize: 0,
  setDaysToVisualize: (days) => set({ daysToVisualize: days }),
}));

export default useAnalysisStore;
