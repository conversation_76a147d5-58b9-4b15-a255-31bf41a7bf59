<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Expiry Premium Calculation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-case {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .pass {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        h1 {
            color: #333;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test Expiry Premium Calculation</h1>
    <div id="results"></div>

    <script type="module">
        // Import the function to test
        import { calculatePositionPremium } from './position-utils.js';

        // Test cases
        const testCases = [
            {
                name: 'Call option at expiry (in the money)',
                position: {
                    type: 'Call',
                    strike: 22000,
                    daysToExpiry: 0, // At expiry
                },
                stockPrice: 22500,
                riskFreeRate: 2,
                volatility: 30,
                expectedResult: 500 // 22500 - 22000 = 500
            },
            {
                name: 'Call option at expiry (out of the money)',
                position: {
                    type: 'Call',
                    strike: 23000,
                    daysToExpiry: 0, // At expiry
                },
                stockPrice: 22500,
                riskFreeRate: 2,
                volatility: 30,
                expectedResult: 0
            },
            {
                name: 'Put option at expiry (in the money)',
                position: {
                    type: 'Put',
                    strike: 23000,
                    daysToExpiry: 0, // At expiry
                },
                stockPrice: 22500,
                riskFreeRate: 2,
                volatility: 30,
                expectedResult: 500 // 23000 - 22500 = 500
            },
            {
                name: 'Put option at expiry (out of the money)',
                position: {
                    type: 'Put',
                    strike: 22000,
                    daysToExpiry: 0, // At expiry
                },
                stockPrice: 22500,
                riskFreeRate: 2,
                volatility: 30,
                expectedResult: 0
            }
        ];

        // Run tests
        const resultsDiv = document.getElementById('results');
        
        testCases.forEach(testCase => {
            try {
                const result = calculatePositionPremium(
                    testCase.position,
                    testCase.stockPrice,
                    testCase.riskFreeRate,
                    testCase.volatility
                );
                
                const passed = Math.abs(result - testCase.expectedResult) < 0.01; // Allow small floating point differences
                
                const testDiv = document.createElement('div');
                testDiv.className = `test-case ${passed ? 'pass' : 'fail'}`;
                
                testDiv.innerHTML = `
                    <h3>${testCase.name}</h3>
                    <p><strong>Expected:</strong> ${testCase.expectedResult}</p>
                    <p><strong>Actual:</strong> ${result}</p>
                    <p><strong>Result:</strong> ${passed ? 'PASS' : 'FAIL'}</p>
                    <pre>${JSON.stringify(testCase.position, null, 2)}</pre>
                `;
                
                resultsDiv.appendChild(testDiv);
            } catch (error) {
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case fail';
                
                testDiv.innerHTML = `
                    <h3>${testCase.name}</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <pre>${error.stack}</pre>
                `;
                
                resultsDiv.appendChild(testDiv);
            }
        });
    </script>
</body>
</html>
