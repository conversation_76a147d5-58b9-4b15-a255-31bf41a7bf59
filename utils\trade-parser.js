/**
 * Trade Parser Utility
 *
 * This utility parses raw text input into structured trade objects.
 * It handles different formats of trade data and identifies futures vs options.
 */

/**
 * Extracts a potential underlying trading symbol from a trade's ticker.
 * For options and futures, this usually means the leading alphabetic characters,
 * with special handling for futures month codes.
 * E.g., "AAPL241220C00180000" -> "AAPL"
 * E.g., "HSIK5" -> "HSI" (Future: HSI, Month K, Year 5)
 * E.g., "ESM24" -> "ES" (Future: ES, Month M, Year 24)
 * E.g., "HSIK" -> "HSI" (Future: HSI, Month K, year implicit)
 * E.g., "HSI" -> "HSI"
 * @param {string} tradeTicker - The ticker string of the trade.
 * @returns {string} The extracted underlying symbol, or the original ticker if no specific pattern matches.
 */
function getUnderlyingSymbol(tradeTicker) {
  if (!tradeTicker) return '';

  const normalizedTicker = tradeTicker.toUpperCase();

  // Pattern 1: Recognize index option tickers (HSI, HHI, HTI)
  const indexPattern = /^(HSI|HHI|HTI|MHI)\d*[A-Z]?\d*W?\d*$/;
  let match = normalizedTicker.match(indexPattern);
  if (match) {
    return match[1]; // Return the index symbol (HSI, HHI, HTI)
  }

  // Pattern 2: Recognizes futures like HSIK5 (HSI, K, 5), ESM24 (ES, M, 24), CLG24 (CL, G, 24)
  // Assumes symbol, then a single month code letter ([FGHJKMNQUVXZ]), then 1 or 2 year digits.
  // The ([A-Z]+) part is greedy to correctly capture symbols like "GOLD" from "GOLDZ3".
  const futurePatternWithYear = /^([A-Z]+)([FGHJKMNQUVXZ])(\d{1,2})$/;
  match = normalizedTicker.match(futurePatternWithYear);
  if (match) {
    // For index futures, return the exact match
    if (['HSI', 'HHI', 'HTI', 'MHI'].includes(match[1])) {
      return match[1];
    }
    return match[1]; // The symbol part, e.g., "ES" from "ESM24"
  }

  // Pattern 3: Recognizes futures like HSIK (HSI, K), ESM (ES, M) where year is not in this part of the ticker.
  // Assumes symbol, then a single month code letter.
  const futurePatternWithoutYear = /^([A-Z]+)([FGHJKMNQUVXZ])$/;
  match = normalizedTicker.match(futurePatternWithoutYear);
  if (match) {
    // For index futures, return the exact match
    if (['HSI', 'HHI', 'HTI', 'MHI'].includes(match[1])) {
      return match[1];
    }
    return match[1]; // The symbol part, e.g., "ES" from "ESM"
  }

  // Default/Fallback: Extract all leading alphabetic characters.
  // Handles stock/option tickers like AAPL241220C00180000 -> AAPL
  // Or simple tickers like HSI -> HSI, GOOG -> GOOG
  // Or if a ticker didn't match specific future patterns (e.g. "SOMEOTHERKINDSTOCK", "HSI123").
  const baseSymbolMatch = normalizedTicker.match(/^[A-Z]+/);
  if (baseSymbolMatch) {
    const symbol = baseSymbolMatch[0];
    // For index symbols, return the exact match
    if (['HSI', 'HHI', 'HTI', 'MHI'].includes(symbol)) {
      return symbol;
    }
    return symbol;
  }

  // Fallback if no letters are at the start (unlikely for valid tickers)
  return normalizedTicker;
}

/**
 * Parse raw text into trade objects that match the ParsedTrade interface:
 * {
 *   id: string;
 *   ticker: string;
 *   expiryDate: string; // e.g., "2025-06", "2025-05-16"
 *   strike: string; // e.g., "7200", can be "N/A" or empty for futures
 *   type: 'Call' | 'Put' | 'Future' | 'Unknown';
 *   quantity: number; // e.g., 2, -4
 *   cost: number; // e.g., 400.00
 *   originalLine?: string;
 *   uploadedAt?: string | number;
 * }
 *
 * @param {string} rawText - The raw text to parse
 * @returns {Array} - Array of parsed trade objects matching the ParsedTrade interface
 */
function parseTrades(rawText) {
  if (!rawText || !rawText.trim()) {
    return [];
  }

  // Handle potential encoding issues
  try {
    // Split the input by lines and filter out empty lines and header rows
    const lines = rawText.split('\n')
      .filter(line => line.trim().length > 0)
      // Skip header rows (typically contain column names)
      .filter(line => {
        const firstChar = line.trim().charAt(0);
        // Skip lines that don't start with alphanumeric characters (likely headers)
        return /[a-zA-Z0-9]/.test(firstChar);
      });

    console.log(`Processing ${lines.length} lines of trade data`);

    // Parse each line into a trade object
    const trades = lines.map((line, index) => parseTradeLine(line, index));

    // Filter out any null results (lines that couldn't be parsed)
    const validTrades = trades.filter(trade => trade !== null);

    console.log(`Successfully parsed ${validTrades.length} trades out of ${lines.length} lines`);

    return validTrades;
  } catch (error) {
    console.error('Error parsing trades:', error);
    // Return empty array instead of throwing to prevent server crashes
    return [];
  }
}

/**
 * Parse a single line of text into a trade object
 *
 * @param {string} line - The line to parse
 * @param {number} index - The index of the line (used for generating IDs)
 * @returns {Object|null} - The parsed trade object or null if parsing failed
 */
function parseTradeLine(line, index) {
  // Trim the line to remove any leading/trailing whitespace
  const trimmedLine = line.trim();

  // Skip empty lines
  if (!trimmedLine) {
    return null;
  }

  try {
    // Extract the ticker (first column before any tab or multiple spaces)
    const tickerMatch = trimmedLine.match(/^([A-Z0-9]+)/);
    if (!tickerMatch) {
      console.log(`Skipping line with no valid ticker: ${trimmedLine.substring(0, 30)}...`);
      return null;
    }

    const ticker = tickerMatch[1];

    // Extract the underlying symbol
    const stock = getUnderlyingSymbol(ticker);

    // Try different parsing strategies
    const trade =
      parseTabSeparatedFormat(trimmedLine) ||
      parseSpaceSeparatedFormat(trimmedLine) ||
      parseSimpleFormat(trimmedLine);

    if (trade) {
      // Add a unique ID, the original line, and the stock
      return {
        id: `parsed-${index}-${Date.now()}`,
        ...trade,
        stock: stock,
        originalLine: trimmedLine
      };
    }

    // If all parsing strategies fail but we have a ticker, create a minimal trade object
    if (ticker) {
      console.log(`Creating minimal trade object for ticker: ${ticker}`);

      // Check if this is a future using the ticker pattern
      // Futures typically have a pattern like HSIK5, ESM24, etc.
      const futurePatternWithYear = /^([A-Z]+)([FGHJKMNQUVXZ])(\d{1,2})$/;
      const futurePatternWithoutYear = /^([A-Z]+)([FGHJKMNQUVXZ])$/;
      const isFuture = futurePatternWithYear.test(ticker) ||
                       futurePatternWithoutYear.test(ticker) ||
                       ticker.length <= 5;

      // Determine the type based on the ticker and pattern
      let type = 'Unknown';
      if (isFuture) {
        type = 'Future';
      } else if (ticker.includes('Q') || ticker.includes('P')) {
        type = 'Put';
      } else if (ticker.includes('E') || ticker.includes('C')) {
        type = 'Call';
      }

      // Extract strike price - for futures it should be 'N/A', for options it should be a string
      const strike = isFuture ? 'N/A' : String(extractStrikeFromTicker(ticker));

      return {
        id: `parsed-${index}-${Date.now()}`,
        ticker: ticker,
        stock: stock,
        type: type,
        expiryDate: extractExpiryFromTicker(ticker),
        strike: strike,
        quantity: 1,
        cost: 0,
        originalLine: trimmedLine
      };
    }

    return null;
  } catch (error) {
    console.error('Error parsing line:', trimmedLine, error);
    return null;
  }
}

/**
 * Parse a tab-separated line format
 * Example: HHI7200R5\tIndex 2025-06 7200 Put\t2@400.00
 *
 * @param {string} line - The line to parse
 * @returns {Object|null} - The parsed trade object or null if parsing failed
 */
function parseTabSeparatedFormat(line) {
  try {
    const parts = line.split('\t').map(part => part.trim()).filter(Boolean);

    if (parts.length < 2) {
      return null;
    }

    const ticker = parts[0];

    // Extract the underlying symbol
    const stock = getUnderlyingSymbol(ticker);

    // Check if this is a future using the ticker pattern and description
    const futurePatternWithYear = /^([A-Z]+)([FGHJKMNQUVXZ])(\d{1,2})$/;
    const futurePatternWithoutYear = /^([A-Z]+)([FGHJKMNQUVXZ])$/;
    const isFuture = futurePatternWithYear.test(ticker) ||
                     futurePatternWithoutYear.test(ticker) ||
                     ticker.length <= 5 ||
                     (parts[1] && parts[1].toLowerCase().includes('future'));

    let type, expiry, strike, quantity, price;

    if (isFuture) {
      type = 'Future';
      strike = 'N/A';

      // Try to extract expiry from the description
      const expiryMatch = parts[1] ? parts[1].match(/(\d{4}-\d{2}(?:-\d{2})?)/) : null;
      expiry = expiryMatch ? expiryMatch[1] : extractExpiryFromTicker(ticker);

      // Extract quantity and price from the last @ column (net position column)
      const atParts = parts.filter(part => part && part.includes('@'));
      const quantityPriceMatch = atParts.length > 0 ? atParts[atParts.length - 1] : null;
      if (quantityPriceMatch) {
        const [qtyStr, priceStr] = quantityPriceMatch.split('@');
        const parsedQuantity = parseInt(qtyStr.replace(/[^0-9\-]/g, ''), 10);
        quantity = !isNaN(parsedQuantity) ? parsedQuantity : 1;
        price = parseFloat(priceStr.replace(/[^0-9\.\-]/g, '')) || 0;
      } else {
        quantity = 1;
        price = 0;
      }
    } else {
      // This is an option

      // Try to determine if it's a call or put from the description or ticker
      if ((parts[1] && parts[1].toLowerCase().includes('put')) ||
          ticker.includes('Q') || ticker.includes('P')) {
        type = 'Put';
      } else if ((parts[1] && parts[1].toLowerCase().includes('call')) ||
                 ticker.includes('E') || ticker.includes('C')) {
        type = 'Call';
      } else {
        type = 'Unknown';
      }

      // Try to extract expiry from the description
      const expiryMatch = parts[1] ? parts[1].match(/(\d{4}-\d{2}(?:-\d{2})?)/) : null;
      expiry = expiryMatch ? expiryMatch[1] : extractExpiryFromTicker(ticker);

      // First, try to extract strike directly from the ticker for index options
      if (/^(HSI|HHI|HTI|MHI)/.test(ticker)) {
        const indexStrike = extractStrikeFromIndexTicker(ticker);
        if (indexStrike) {
          strike = indexStrike;
          console.log(`Using strike ${strike} extracted directly from index ticker: ${ticker}`);
        }
      }

      // If we couldn't extract from the ticker or it's not an index option, try the description
      if (!strike) {
        // Refined regex to better distinguish strike from potential preceding date numbers
        const strikeRegex = /(?:\d{4}-\d{2}(?:-\d{2})?\s+)?(\d+(?:\.\d+)?)\s+(Put|Call)/i;
        // Explanation of refined regex:
        // (?: \d{4}-\d{2}(?:-\d{2})? \s+ )? : Optionally match a date (YYYY-MM or YYYY-MM-DD) followed by space. This is non-capturing.
        // ( \d+(?:\.\d+)? )                 : Capture the strike price. Allows for decimals in strike if ever needed, though typically whole numbers for these options.
        // \s+(Put|Call)                     : Match "Put" or "Call" preceded by space.
        // i                                 : Case-insensitive matching for Put/Call.

        const strikeMatch = parts[1] ? parts[1].match(strikeRegex) : null;

        if (strikeMatch) {
          // If we found a pattern like "2025-06 7200 Put", use that as the strike
          strike = String(strikeMatch[1]);
          console.log(`Extracted strike ${strike} from description using improved regex: ${parts[1]}`);
        } else {
          // Otherwise, fall back to the general extractStrikeFromTicker function
          const tickerStrike = extractStrikeFromTicker(ticker);
          strike = String(tickerStrike);
          console.log(`Extracted strike ${strike} from ticker using general method: ${ticker}`);
        }
      }

      // For index options, if we have both a ticker strike and a description strike, prefer the ticker strike
      if (/^(HSI|HHI|HTI|MHI)/.test(ticker)) {
        const indexStrike = extractStrikeFromIndexTicker(ticker);
        if (indexStrike) {
          console.log(`Overriding strike with ${indexStrike} from index ticker: ${ticker}`);
          strike = indexStrike;
        }
      }      // Extract quantity and price from the last @ column (net position column)
      const atParts = parts.filter(part => part && part.includes('@'));
      const quantityPriceMatch = atParts.length > 0 ? atParts[atParts.length - 1] : null;
      if (quantityPriceMatch) {
        const [qtyStr, priceStr] = quantityPriceMatch.split('@');
        const parsedQuantity = parseInt(qtyStr.replace(/[^0-9\-]/g, ''), 10);
        // Handle zero quantity explicitly - only default to 1 if parsing failed completely
        quantity = !isNaN(parsedQuantity) ? parsedQuantity : 1;
        price = parseFloat(priceStr.replace(/[^0-9\.\-]/g, '')) || 0;
      } else {
        quantity = 1;
        price = 0;
      }
    }

    return {
      ticker,
      stock,
      type,
      expiryDate: expiry,
      strike,
      quantity,
      cost: price
    };
  } catch (error) {
    console.error('Error in parseTabSeparatedFormat:', error);
    return null;
  }
}

/**
 * Parse a space-separated line format
 * Example: HHI7200R5 2 400.00
 *
 * @param {string} line - The line to parse
 * @returns {Object|null} - The parsed trade object or null if parsing failed
 */
function parseSpaceSeparatedFormat(line) {
  try {
    const parts = line.split(/\s+/).filter(Boolean);

    if (parts.length < 2) {
      return null;
    }

    const ticker = parts[0];

    // Extract the underlying symbol
    const stock = getUnderlyingSymbol(ticker);

    // Check if this is a future using the ticker pattern
    const futurePatternWithYear = /^([A-Z]+)([FGHJKMNQUVXZ])(\d{1,2})$/;
    const futurePatternWithoutYear = /^([A-Z]+)([FGHJKMNQUVXZ])$/;
    const isFuture = futurePatternWithYear.test(ticker) ||
                     futurePatternWithoutYear.test(ticker) ||
                     ticker.length <= 5;

    let type, expiry, strike, quantity, price;

    if (isFuture) {
      type = 'Future';
      strike = 'N/A';
      expiry = extractExpiryFromTicker(ticker);
    } else {
      // Determine if it's a call or put from the ticker
      if (ticker.includes('Q') || ticker.includes('P')) {
        type = 'Put';
      } else if (ticker.includes('E') || ticker.includes('C')) {
        type = 'Call';
      } else {
        type = 'Unknown';
      }
      expiry = extractExpiryFromTicker(ticker);

      // For index options, use the dedicated function to extract strike
      if (/^(HSI|HHI|HTI|MHI)/.test(ticker)) {
        const indexStrike = extractStrikeFromIndexTicker(ticker);
        if (indexStrike) {
          strike = indexStrike;
          console.log(`Using strike ${strike} extracted directly from index ticker in parseSpaceSeparatedFormat: ${ticker}`);
        } else {
          strike = String(extractStrikeFromTicker(ticker));
        }
      } else {
        strike = String(extractStrikeFromTicker(ticker));
      }
    }

    // Try to extract quantity and price
    if (parts.length >= 3) {
      const parsedQuantity = parseInt(parts[1].replace(/[^0-9\-]/g, ''), 10);
      quantity = !isNaN(parsedQuantity) ? parsedQuantity : 1;
      price = parseFloat(parts[2].replace(/[^0-9\.\-]/g, '')) || 0;
    } else if (parts.length === 2) {
      quantity = 1;
      price = parseFloat(parts[1].replace(/[^0-9\.\-]/g, '')) || 0;
    } else {
      quantity = 1;
      price = 0;
    }

    return {
      ticker,
      stock,
      type,
      expiryDate: expiry,
      strike,
      quantity,
      cost: price
    };
  } catch (error) {
    console.error('Error in parseSpaceSeparatedFormat:', error);
    return null;
  }
}

/**
 * Parse a simple format with just a ticker
 * Example: HHI7200R5
 *
 * @param {string} line - The line to parse
 * @returns {Object|null} - The parsed trade object or null if parsing failed
 */
function parseSimpleFormat(line) {
  try {
    const ticker = line.trim();

    if (!ticker) {
      return null;
    }

    // Extract the underlying symbol
    const stock = getUnderlyingSymbol(ticker);

    // Check if this is a future using the ticker pattern
    const futurePatternWithYear = /^([A-Z]+)([FGHJKMNQUVXZ])(\d{1,2})$/;
    const futurePatternWithoutYear = /^([A-Z]+)([FGHJKMNQUVXZ])$/;
    const isFuture = futurePatternWithYear.test(ticker) ||
                     futurePatternWithoutYear.test(ticker) ||
                     ticker.length <= 5;

    let type, expiry, strike;

    if (isFuture) {
      type = 'Future';
      strike = 'N/A';
      expiry = extractExpiryFromTicker(ticker);
    } else {
      // Try to determine if it's a call or put from the ticker
      if (ticker.includes('Q') || ticker.includes('P')) {
        type = 'Put';
      } else if (ticker.includes('E') || ticker.includes('C')) {
        type = 'Call';
      } else {
        type = 'Unknown';
      }
      expiry = extractExpiryFromTicker(ticker);

      // For index options, use the dedicated function to extract strike
      if (/^(HSI|HHI|HTI|MHI)/.test(ticker)) {
        const indexStrike = extractStrikeFromIndexTicker(ticker);
        if (indexStrike) {
          strike = indexStrike;
          console.log(`Using strike ${strike} extracted directly from index ticker in parseSimpleFormat: ${ticker}`);
        } else {
          strike = String(extractStrikeFromTicker(ticker));
        }
      } else {
        strike = String(extractStrikeFromTicker(ticker));
      }
    }

    return {
      ticker,
      stock,
      type,
      expiryDate: expiry,
      strike,
      quantity: 1,
      cost: 0
    };
  } catch (error) {
    console.error('Error in parseSimpleFormat:', error);
    return null;
  }
}

/**
 * Extract expiry date from ticker
 *
 * @param {string} ticker - The ticker to extract from
 * @returns {string} - The extracted expiry date
 */
function extractExpiryFromTicker(ticker) {
  try {
    // Pattern 1: Recognizes futures like HSIK5 (HSI, K, 5), ESM24 (ES, M, 24), CLG24 (CL, G, 24)
    // Assumes symbol, then a single month code letter ([FGHJKMNQUVXZ]), then 1 or 2 year digits.
    const futurePatternWithYear = /^([A-Z]+)([FGHJKMNQUVXZ])(\d{1,2})$/;
    let match = ticker.match(futurePatternWithYear);

    if (match) {
      const monthCode = match[2];
      const yearCode = match[3];

      // Convert month code to month number (F=1, G=2, H=3, J=4, K=5, M=6, N=7, Q=8, U=9, V=10, X=11, Z=12)
      const monthMap = { F: 1, G: 2, H: 3, J: 4, K: 5, M: 6, N: 7, Q: 8, U: 9, V: 10, X: 11, Z: 12 };
      const month = monthMap[monthCode.toUpperCase()] || 1;

      // Convert year code to full year
      // If it's a 2-digit year, assume 20xx
      // If it's a 1-digit year, assume 202x
      const year = yearCode.length === 2 ? 2000 + parseInt(yearCode, 10) : 2020 + parseInt(yearCode, 10);

      // Format as YYYY-MM
      return `${year}-${month.toString().padStart(2, '0')}`;
    }

    // Pattern 2: For options with expiry date in the ticker (e.g., HHI7200R5W16)
    // The W16 at the end indicates the expiry day (16th day of the month)
    const weeklyExpiryMatch = ticker.match(/W(\d{2})$/);
    if (weeklyExpiryMatch) {
      const day = weeklyExpiryMatch[1];

      // Extract the month and year from the rest of the ticker
      const baseTickerWithoutWeekly = ticker.replace(/W\d{2}$/, '');
      const baseExpiry = extractExpiryFromTicker(baseTickerWithoutWeekly);

      // If we successfully extracted a base expiry (YYYY-MM), add the day
      if (baseExpiry && baseExpiry.match(/^\d{4}-\d{2}$/)) {
        return `${baseExpiry}-${day}`;
      }
    }

    // Pattern 3: For options with standard expiry format in the ticker
    const expiryMatch = ticker.match(/(\d{2})(\d{2})([CEQP])/i);
    if (expiryMatch) {
      const month = expiryMatch[1];
      const year = expiryMatch[2];
      // Format as YYYY-MM
      return `20${year}-${month}`;
    }

    // Pattern 4: For options with R5, F5, etc. in the ticker (e.g., HHI7200R5)
    // R5 = June 2025, F5 = January 2025, etc.
    const monthCodeMatch = ticker.match(/([FGHJKMNQUVXZ])(\d{1})(?![FGHJKMNQUVXZ])/i);
    if (monthCodeMatch) {
      const monthCode = monthCodeMatch[1];
      const yearCode = monthCodeMatch[2];

      // Convert month code to month number
      const monthMap = { F: 1, G: 2, H: 3, J: 4, K: 5, M: 6, N: 7, Q: 8, U: 9, V: 10, X: 11, Z: 12 };
      const month = monthMap[monthCode.toUpperCase()] || 1;

      // Convert year code to full year (assuming 202x)
      const year = 2020 + parseInt(yearCode, 10);

      // Format as YYYY-MM
      return `${year}-${month.toString().padStart(2, '0')}`;
    }

    // Default to current month/year if we can't extract
    const now = new Date();
    return `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;
  } catch (error) {
    console.error('Error extracting expiry date from ticker:', ticker, error);
    // Default to current month/year if there's an error
    const now = new Date();
    return `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;
  }
}

/**
 * Extract strike price directly from index option ticker
 *
 * @param {string} ticker - The ticker to extract from (e.g., HHI7200R5, HSI22600Q5)
 * @returns {string} - The extracted strike price as a string
 */
function extractStrikeFromIndexTicker(ticker) {
  if (!ticker) return '';

  // Check if it's an index option ticker
  if (!/^(HSI|HHI|HTI|MHI)/.test(ticker)) {
    return '';
  }

  console.log(`Extracting strike from index ticker: ${ticker}`);

  // Extract the numeric part after the index code (HSI, HHI, etc.)
  // and before the month code (R5, Q5, etc.)
  const match = ticker.match(/^(HSI|HHI|HTI|MHI)(\d+)[A-Z]/);
  if (match && match[2]) {
    const strike = match[2];
    console.log(`Successfully extracted strike ${strike} from ticker ${ticker}`);
    return strike;
  }

  // Try another pattern for index options
  const altMatch = ticker.match(/^(HSI|HHI|HTI|MHI)(\d+)/);
  if (altMatch && altMatch[2]) {
    const strike = altMatch[2];
    console.log(`Successfully extracted strike ${strike} from alternative pattern for ticker ${ticker}`);
    return strike;
  }

  console.log(`Failed to extract strike from ticker ${ticker}`);
  return '';
}

/**
 * Extract strike price from ticker
 *
 * @param {string} ticker - The ticker to extract from
 * @returns {number} - The extracted strike price
 */
function extractStrikeFromTicker(ticker) {
  try {
    // For index options like HHI7200R5, HSI22600Q5, etc.
    // The pattern is: 3-letter index code + strike price + month/year code
    // Examples:
    // - HHI7200R5: HHI (index), 7200 (strike), R5 (month/year)
    // - HSI22600Q5: HSI (index), 22600 (strike), Q5 (month/year)

    // First, check if it's an index option (starts with HSI, HHI, HTI, etc.)
    if (/^(HSI|HHI|HTI|MHI)/.test(ticker)) {
      // Try to extract strike directly using the dedicated function
      const indexStrike = extractStrikeFromIndexTicker(ticker);
      if (indexStrike) {
        return parseInt(indexStrike, 10);
      }
    }

    // Pattern 1: For options with standard format (e.g., HHI7200R5)
    // The number in the middle is the strike price
    const strikeMatch = ticker.match(/(\d+)(?=[FGHJKMNQUVXZ]\d)/);
    if (strikeMatch) {
      return parseInt(strikeMatch[1], 10);
    }

    // Pattern 2: For options with weekly expiry (e.g., HHI7200R5W16, HSI22600Q5W23)
    // The number before the month code that's followed by a W is the strike price
    const weeklyStrikeMatch = ticker.match(/^(HSI|HHI|HTI|MHI)(\d+)[FGHJKMNQUVXZ]\d+W\d+/);
    if (weeklyStrikeMatch) {
      return parseInt(weeklyStrikeMatch[2], 10);
    }

    // Pattern 3: For options with standard format (e.g., AAPL241220C00180000)
    // The number after C or P is the strike price, possibly with decimal places
    const standardStrikeMatch = ticker.match(/[CP](\d{8})/);
    if (standardStrikeMatch) {
      // Convert the 8-digit number to a strike price with 3 decimal places
      // e.g., 00180000 -> 180.000
      const strikeStr = standardStrikeMatch[1];
      return parseInt(strikeStr.substring(0, 5), 10) / Math.pow(10, 3);
    }

    // Fallback: Try to extract any number from the ticker
    const anyNumberMatch = ticker.match(/(\d+)/);
    return anyNumberMatch ? parseInt(anyNumberMatch[1], 10) : 0;
  } catch (error) {
    console.error('Error extracting strike price from ticker:', ticker, error);
    return 0;
  }
}

module.exports = {
  parseTrades,
  getUnderlyingSymbol,
  extractStrikeFromTicker,
  extractStrikeFromIndexTicker,
  extractExpiryFromTicker
};
