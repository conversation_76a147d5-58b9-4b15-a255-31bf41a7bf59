// No imports needed

// Variable to store the setDatabaseOffline function
let setDatabaseOfflineFunc = null;
let isOfflineState = false;

// Function to set the database offline function
export const setDatabaseOfflineFunction = (func) => {
  setDatabaseOfflineFunc = func;
};

// Function to check if database is offline
export const isDatabaseOffline = () => {
  return isOfflineState;
};

// Function to set database offline status
export const setDatabaseOffline = (error) => {
  // If already offline, don't trigger the notification again
  if (isOfflineState) {
    console.log('Database already in offline mode, not triggering notification again');
    return;
  }

  console.log('Setting database to offline mode from db-utils');
  // Set offline state first
  isOfflineState = true;

  // Then call the context function to show notification
  if (setDatabaseOfflineFunc) {
    setDatabaseOfflineFunc(error);
    console.log('Database offline function called with error:', error);
  } else {
    console.error('setDatabaseOfflineFunc is not set!');
  }
};

/**
 * Fetch all positions from the database
 * @returns {Promise<Array>} - Array of positions
 * @throws {Error} - If there's a database connection error
 */
export const fetchPositions = async () => {
  try {
    console.log('Fetching positions from server...');
    const response = await fetch('/api/positions');
    console.log('Fetch response received:', response);

    if (!response.ok) {
      const errorData = await response.json();
      console.log('Error data from server:', errorData);

      // Check if the error is related to database connection
      if (errorData.message && (
          errorData.message.includes('database') ||
          errorData.message.includes('connection') ||
          errorData.message.includes('ECONNREFUSED') ||
          errorData.message.includes('timeout')
        )) {
        // Set database offline status
        const dbError = new Error(`Database connection error: ${errorData.message}`);
        console.log('Database connection error detected in response');
        setDatabaseOffline(dbError);
        throw dbError;
      }
      throw new Error(errorData.message || 'Failed to fetch positions');
    }

    const data = await response.json();
    console.log('Positions data received:', data);

    // The server already maps the database column names to the frontend property names
    // and converts the data types, so we just need to ensure the saved flag is set
    return data.positions.map(position => ({
      ...position,
      saved: true
    }));
  } catch (error) {
    console.error('Error fetching positions:', error);

    // Check if it's a network error (fetch will throw a TypeError for network errors)
    if (error instanceof TypeError || error.name === 'TypeError' ||
        error.message.includes('Failed to fetch') || error.message.includes('Network Error')) {
      console.log('Network error detected, setting database offline');
      const networkError = new Error('Network error: Unable to connect to the server');
      setDatabaseOffline(networkError);
      throw networkError;
    } else if (error.message && (
      error.message.includes('database') ||
      error.message.includes('connection') ||
      error.message.includes('ECONNREFUSED') ||
      error.message.includes('timeout')
    )) {
      // Set database offline for database-related errors
      console.log('Database error detected, setting database offline');
      setDatabaseOffline(error);
    } else {
      // For any other error, also set database offline
      console.log('Other error detected, setting database offline');
      setDatabaseOffline(error);
    }

    // Instead of returning an empty array, throw the error so it can be handled by the component
    // This allows us to display the "Offline" indicator
    throw error;
  }
};

/**
 * Save a position to the database
 * @param {Object} position - The position to save
 * @returns {Promise<Object>} - The result of the database operation
 */
export const savePosition = async (position) => {
  try {
    // Check which fields are missing and log them
    console.log('Attempting to save position:', position);

    const missingFields = [];
    if (!position.quantity && position.quantity !== 0) missingFields.push('quantity');
    if (!position.type) missingFields.push('type');
    if (!position.strike && position.strike !== 0) missingFields.push('strike');
    if (!position.ExpiryDate) missingFields.push('ExpiryDate');
    if (!position.premium && position.premium !== 0) missingFields.push('premium');
    if (position.debitCredit === undefined) missingFields.push('debitCredit');

    if (missingFields.length > 0) {
      console.error('Missing fields in position:', missingFields);
      return {
        success: false,
        message: `Missing required fields: ${missingFields.join(', ')}`
      };
    }

    // Validate data according to schema constraints
    if (position.quantity < -999 || position.quantity > 999) {
      return {
        success: false,
        message: 'Quantity must be between -999 and 999'
      };
    }

    if (position.type !== 'Call' && position.type !== 'Put') {
      return {
        success: false,
        message: 'Type must be either "Call" or "Put"'
      };
    }

    if (position.strike < 0 || position.strike > 99999) {
      return {
        success: false,
        message: 'Strike must be between 0 and 99999'
      };
    }

    // Direct database connection using the WILL9700_DB connection string
    const response = await fetch('/api/savePosition', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        // Send values in the format expected by the server
        quantity: position.quantity,
        type: position.type,
        strike: position.strike,
        ExpiryDate: position.ExpiryDate,
        premium: position.premium,
        debitCredit: position.debitCredit
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Server returned error:', errorData);

      // Check if the error is related to database connection
      if (errorData.message && (
          errorData.message.includes('database') ||
          errorData.message.includes('connection') ||
          errorData.message.includes('ECONNREFUSED') ||
          errorData.message.includes('timeout')
        )) {
        // Set database offline status
        setDatabaseOffline(new Error(`Database connection error: ${errorData.message}`));
        throw new Error(`Database connection error: Unable to connect to the database`);
      }

      throw new Error(errorData.message || 'Failed to save position');
    }

    const data = await response.json();

    return {
      success: true,
      message: 'Position saved successfully',
      id: data.id
    };
  } catch (error) {
    console.error('Error saving position:', error);

    // Check if it's a network error (fetch will throw a TypeError for network errors)
    if (error instanceof TypeError || error.name === 'TypeError' ||
        error.message.includes('Failed to fetch') || error.message.includes('Network Error')) {
      console.log('Network error detected, setting database offline');
      const networkError = new Error('Network error: Unable to connect to the server');
      setDatabaseOffline(networkError);
      return {
        success: false,
        message: 'Network error: Unable to connect to the server',
        error: error.message || 'Network error',
        isOffline: true
      };
    }

    // Check if it's a database connection error
    const errorMessage = error.message || 'Unknown error';
    const isDatabaseError = errorMessage.includes('database') ||
                           errorMessage.includes('connection') ||
                           errorMessage.includes('ECONNREFUSED') ||
                           errorMessage.includes('timeout');

    // Set database offline status if it's a database error
    if (isDatabaseError) {
      console.log('Database error detected in savePosition, setting database offline');
      setDatabaseOffline(error);
    } else {
      // For any other error, also set database offline
      console.log('Other error detected in savePosition, setting database offline');
      setDatabaseOffline(error);
    }

    return {
      success: false,
      message: isDatabaseError ? `Database connection error: Unable to connect to the database` : 'Error saving position',
      error: errorMessage,
      isOffline: isDatabaseError || true // Always treat as offline for now
    };
  }
};
