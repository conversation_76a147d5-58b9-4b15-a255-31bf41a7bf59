import React, { useState, useRef, useMemo } from 'react';

/**
 * FilteredTradesThetaTable Component
 * 
 * Displays theta (daily PnL change) for filtered trades data in a collapsible table format with:
 * - Frozen left columns for trade basic info (same as FilteredTradesDateTable)
 * - Dynamic columns for each date point showing daily PnL change (theta)
 * - Horizontal scrolling with frozen trade info columns
 * - Collapsible functionality via clickable header
 * - Sortable columns for easy viewing
 * - Total row for PnL changes
 */
const FilteredTradesThetaTable = ({
  filteredDateData,
  selectedExpiryDate,
  selectedStockSymbol
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const scrollContainerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });

  // Extract data from filteredDateData
  const { positionData = [] } = filteredDateData;

  // Get date points from the data
  const datePoints = useMemo(() => {
    if (!positionData || positionData.length === 0) return [];
    
    const firstPosition = positionData[0];
    const dates = Object.keys(firstPosition)
      .filter(key => key.startsWith('pnl_'))
      .map(key => key.replace('pnl_', ''))
      .sort();
    
    return dates;
  }, [positionData]);

  // Calculate theta (daily PnL changes) for each trade
  const thetaData = useMemo(() => {
    if (!positionData || positionData.length === 0 || datePoints.length < 2) {
      return [];
    }

    return positionData.map(position => {
      const thetaPosition = {
        id: position.id,
        trade: position.trade
      };

      // Calculate theta for each consecutive date pair
      for (let i = 1; i < datePoints.length; i++) {
        const currentDate = datePoints[i];
        const previousDate = datePoints[i - 1];
        
        const currentPnL = position[`pnl_${currentDate}`] || 0;
        const previousPnL = position[`pnl_${previousDate}`] || 0;
        
        // Theta is the change in PnL from previous day to current day
        const theta = currentPnL - previousPnL;
        
        thetaPosition[`theta_${currentDate}`] = theta;
      }

      return thetaPosition;
    });
  }, [positionData, datePoints]);

  // Calculate totals for theta
  const thetaTotals = useMemo(() => {
    const totals = {};
    
    for (let i = 1; i < datePoints.length; i++) {
      const currentDate = datePoints[i];
      totals[currentDate] = 0;
      
      thetaData.forEach(position => {
        const theta = position[`theta_${currentDate}`] || 0;
        totals[currentDate] += theta;
      });
    }
    
    return totals;
  }, [thetaData, datePoints]);

  // Sorting functionality
  const sortedThetaData = useMemo(() => {
    if (!sortConfig.key) return thetaData;

    return [...thetaData].sort((a, b) => {
      let aValue, bValue;

      if (sortConfig.key.startsWith('theta_')) {
        aValue = a[sortConfig.key] || 0;
        bValue = b[sortConfig.key] || 0;
      } else {
        // Handle trade properties
        aValue = a.trade[sortConfig.key] || '';
        bValue = b.trade[sortConfig.key] || '';
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc' ? aValue - bValue : bValue - aValue;
      }

      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();
      
      if (sortConfig.direction === 'asc') {
        return aStr < bStr ? -1 : aStr > bStr ? 1 : 0;
      } else {
        return aStr > bStr ? -1 : aStr < bStr ? 1 : 0;
      }
    });
  }, [thetaData, sortConfig]);

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const getSortIcon = (key) => {
    if (sortConfig.key !== key) {
      return <span className="text-gray-400">↕</span>;
    }
    return sortConfig.direction === 'asc' ? 
      <span className="text-blue-600">↑</span> : 
      <span className="text-blue-600">↓</span>;
  };

  // Helper functions
  const formatDate = (dateStr) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}-${day}`;
  };

  const getDaysFromToday = (dateStr) => {
    if (!dateStr) return 0;
    const date = new Date(dateStr);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    date.setHours(0, 0, 0, 0);
    return Math.floor((date - today) / (1000 * 60 * 60 * 24));
  };

  // Mouse drag functionality for horizontal scrolling
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setDragStart({
      x: e.pageX - scrollContainerRef.current.offsetLeft,
      scrollLeft: scrollContainerRef.current.scrollLeft
    });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX - scrollContainerRef.current.offsetLeft;
    const walk = (x - dragStart.x) * 2;
    scrollContainerRef.current.scrollLeft = dragStart.scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  if (!positionData || positionData.length === 0) {
    return null;
  }

  const tradesCount = sortedThetaData.length;
  const dateCount = datePoints.length - 1; // Subtract 1 because theta starts from second date

  return (
    <div className="bg-white rounded-lg shadow-md mt-4">
      {/* Collapsible Header */}
      <div 
        className="p-4 cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-bold text-gray-800">
              Theta Analysis (Daily PnL Changes)
              <span className="ml-2 text-xs text-gray-500">
                Expiry: {selectedExpiryDate || 'ALL'} | 
                Symbol: {selectedStockSymbol || 'ALL'} | 
                ({tradesCount} trades, {dateCount} theta points)
              </span>
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              Shows daily change in PnL for each trade due to time decay and other factors
            </p>
          </div>
          <div className="flex items-center">
            <span className="text-xs text-gray-500 mr-2">
              {isExpanded ? 'Click to collapse' : 'Click to expand'}
            </span>
            <svg 
              className={`w-5 h-5 text-gray-500 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      {/* Table Content */}
      {isExpanded && (
        <div className="mt-4">
          <div className="bg-gray-50 p-3 rounded-t-lg border-x border-t border-gray-200">
            <div className="flex items-center justify-between">
              <p className="text-xs text-gray-600">
                <strong>Instructions:</strong> Scroll horizontally or <strong>click and drag</strong> to view all theta points.
                Trade info columns (with blue border) are frozen on the left. Click column headers to sort.
              </p>
              <div className="flex items-center text-xs text-gray-500">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                </svg>
                Drag to scroll
              </div>
            </div>
          </div>

          <div 
            className="overflow-x-auto border border-gray-200 rounded-b-lg"
            style={{ maxHeight: '500px' }}
          >
            <div 
              ref={scrollContainerRef}
              className={`overflow-x-auto ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseLeave}
              style={{ minWidth: 'fit-content' }}
            >
              <table className="w-full text-xs">
                <thead className="bg-gray-100 sticky top-0 z-10">
                  <tr>
                    {/* Frozen columns for trade info */}
                    <th 
                      className="border border-blue-400 p-2 text-left font-medium bg-blue-100 sticky left-0 z-20 cursor-pointer hover:bg-blue-200"
                      onClick={() => handleSort('ticker')}
                    >
                      Ticker {getSortIcon('ticker')}
                    </th>
                    <th 
                      className="border border-blue-400 p-2 text-center font-medium bg-blue-100 sticky left-16 z-20 cursor-pointer hover:bg-blue-200"
                      style={{ left: '64px' }}
                      onClick={() => handleSort('type')}
                    >
                      Type {getSortIcon('type')}
                    </th>
                    <th 
                      className="border border-blue-400 p-2 text-center font-medium bg-blue-100 sticky left-24 z-20 cursor-pointer hover:bg-blue-200"
                      style={{ left: '112px' }}
                      onClick={() => handleSort('strike')}
                    >
                      Strike {getSortIcon('strike')}
                    </th>
                    <th 
                      className="border border-blue-400 p-2 text-center font-medium bg-blue-100 sticky left-32 z-20 cursor-pointer hover:bg-blue-200"
                      style={{ left: '160px' }}
                      onClick={() => handleSort('quantity')}
                    >
                      Qty {getSortIcon('quantity')}
                    </th>
                    <th 
                      className="border border-blue-400 p-2 text-center font-medium bg-blue-100 sticky left-40 z-20 cursor-pointer hover:bg-blue-200"
                      style={{ left: '208px' }}
                      onClick={() => handleSort('ExpiryDate')}
                    >
                      Expiry {getSortIcon('ExpiryDate')}
                    </th>

                    {/* Dynamic columns for each theta point (skip first date since theta starts from second date) */}
                    {datePoints.slice(1).map(dateStr => {
                      const daysFromToday = getDaysFromToday(dateStr);
                      const isToday = daysFromToday === 0;
                      return (
                        <th 
                          key={dateStr}
                          className={`border border-gray-300 p-2 text-center font-medium bg-orange-50 min-w-24 cursor-pointer hover:bg-orange-100 ${isToday ? 'bg-yellow-100' : ''}`}
                          onClick={() => handleSort(`theta_${dateStr}`)}
                        >
                          Theta<br/>
                          {formatDate(dateStr)}<br/>
                          <span className="text-xs text-gray-500">
                            {daysFromToday >= 0 ? `+${daysFromToday}d` : `${daysFromToday}d`}
                          </span>
                          {getSortIcon(`theta_${dateStr}`)}
                        </th>
                      );
                    })}
                  </tr>
                </thead>

                <tbody>
                  {/* Data rows */}
                  {sortedThetaData.map((tradeData, index) => {
                    const trade = tradeData.trade;
                    return (
                      <tr key={tradeData.id || index} className="hover:bg-gray-50">
                        {/* Frozen columns for trade info */}
                        <td className="border border-blue-400 p-2 text-left bg-blue-50 sticky left-0 z-10 font-medium">
                          {trade.ticker || 'N/A'}
                        </td>
                        <td className="border border-blue-400 p-2 text-center bg-blue-50 sticky left-16 z-10" style={{ left: '64px' }}>
                          {trade.type || 'N/A'}
                        </td>
                        <td className="border border-blue-400 p-2 text-center bg-blue-50 sticky left-24 z-10" style={{ left: '112px' }}>
                          {trade.strike || 'N/A'}
                        </td>
                        <td className="border border-blue-400 p-2 text-center bg-blue-50 sticky left-32 z-10" style={{ left: '160px' }}>
                          {trade.quantity || 'N/A'}
                        </td>
                        <td className="border border-blue-400 p-2 text-center bg-blue-50 sticky left-40 z-10" style={{ left: '208px' }}>
                          {trade.ExpiryDate || 'N/A'}
                        </td>

                        {/* Dynamic columns for theta */}
                        {datePoints.slice(1).map(dateStr => {
                          const theta = tradeData[`theta_${dateStr}`];

                          return (
                            <td 
                              key={`${tradeData.id || index}_${dateStr}`}
                              className={`border border-gray-300 p-2 text-right min-w-24 ${
                                typeof theta === 'number' ?
                                  (theta < 0 ? 'text-red-600' : theta > 0 ? 'text-green-700' : 'text-gray-700') :
                                  'text-gray-700'
                              }`}
                            >
                              {typeof theta === 'number' ?
                                `$${theta.toLocaleString()}` :
                                'N/A'
                              }
                            </td>
                          );
                        })}
                      </tr>
                    );
                  })}

                  {/* Totals row */}
                  <tr className="bg-gray-100 font-bold border-t-2 border-gray-400">
                    <td className="border border-blue-400 p-2 text-left bg-blue-100 sticky left-0 z-10">
                      TOTAL
                    </td>
                    <td className="border border-blue-400 p-2 bg-blue-100 sticky left-16 z-10" style={{ left: '64px' }}></td>
                    <td className="border border-blue-400 p-2 bg-blue-100 sticky left-24 z-10" style={{ left: '112px' }}></td>
                    <td className="border border-blue-400 p-2 bg-blue-100 sticky left-32 z-10" style={{ left: '160px' }}></td>
                    <td className="border border-blue-400 p-2 bg-blue-100 sticky left-40 z-10" style={{ left: '208px' }}></td>

                    {/* Totals for each theta point */}
                    {datePoints.slice(1).map(dateStr => {
                      const totalTheta = thetaTotals[dateStr] || 0;
                      return (
                        <td 
                          key={`total_${dateStr}`}
                          className={`border border-gray-300 p-2 text-right min-w-24 bg-gray-200 ${
                            totalTheta < 0 ? 'text-red-600' : totalTheta > 0 ? 'text-green-700' : 'text-gray-700'
                          }`}
                        >
                          ${totalTheta.toLocaleString()}
                        </td>
                      );
                    })}
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilteredTradesThetaTable;
