import React, { useState } from 'react';
import usePnLAtVariousPriceStore from '../store/usePnLAtVariousPriceStore';
import usePnLAtVariousDateStore from '../store/usePnLAtVariousDateStore';
import useAnalysisStore from '../store/useAnalysisStore';
import useAllTradesStore from '../store/useAllTradesStore';
import useDatabaseStore from '../store/useDatabaseStore';
import usePnlChartStore from '../store/usePnlChartStore';

/**
 * Enhanced Store Debugger Component
 *
 * Displays all application stores in a compact, readable format.
 * Only visible when DEBUG environment variable is set to true.
 * Provides read-only view of all application state for debugging purposes.
 */
const StoreDebugger = () => {
  // All hooks must be called unconditionally
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Get data from all stores
  const priceStore = usePnLAtVariousPriceStore();
  const dateStore = usePnLAtVariousDateStore();
  const analysisStore = useAnalysisStore();
  const tradesStore = useAllTradesStore();
  const databaseStore = useDatabaseStore();
  const chartStore = usePnlChartStore();

  // Now check debug mode
  const isDebugMode = process.env.REACT_APP_DEBUG === 'true';
  if (!isDebugMode) {
    return null;
  }

  // Extract key data for summary
  const currentInputs = priceStore.getCurrentInputs();
  const totalTradesCount = currentInputs.totalTradesCount || 0;
  const filteredTradesCount = currentInputs.filteredTradesCount || 0;
  
  if (!isExpanded) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-gray-900 text-white rounded-lg p-2 shadow-lg">
          <div className="flex items-center justify-between">
            <div className="text-xs font-mono">
              <span className="text-green-400">DEBUG</span>
              <span className="mx-1">|</span>
              <span className="text-blue-400">{analysisStore.symbol || 'None'}</span>
              <span className="mx-1">|</span>
              <span className="text-yellow-400">{filteredTradesCount}/{totalTradesCount}</span>
              <span className="mx-1">|</span>
              <span className={priceStore.isCalculating ? 'text-red-400' : 'text-green-400'}>
                {priceStore.isCalculating ? 'CALC' : 'RDY'}
              </span>
            </div>
            <button
              onClick={() => setIsExpanded(true)}
              className="ml-2 px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
            >
              ▼
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-4 z-50 bg-gray-900 text-white rounded-lg shadow-2xl overflow-hidden">
      <div className="flex items-center justify-between p-3 bg-gray-800 border-b border-gray-700">
        <h3 className="text-sm font-bold text-green-400">
          🐛 Store Debugger - All Application State
        </h3>
        <button
          onClick={() => setIsExpanded(false)}
          className="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700"
        >
          ✕ Close
        </button>
      </div>

      <div className="p-3 overflow-auto h-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-3 text-xs font-mono">{/* Analysis Store */}
        <div className="bg-gray-800 rounded p-2 border border-blue-500">
          <div className="text-blue-400 font-bold mb-1">📊 Analysis Store</div>
          <div className="space-y-0.5">
            <div><span className="text-gray-400">symbol:</span> <span className="text-yellow-300">{analysisStore.symbol || 'null'}</span></div>
            <div><span className="text-gray-400">targetPrice:</span> <span className="text-green-300">${analysisStore.targetStockPrice}</span></div>
            <div><span className="text-gray-400">volatility:</span> <span className="text-green-300">{analysisStore.volatility}%</span></div>
            <div><span className="text-gray-400">riskFreeRate:</span> <span className="text-green-300">{analysisStore.riskFreeRate}%</span></div>
            <div><span className="text-gray-400">daysToVisualize:</span> <span className="text-green-300">{analysisStore.daysToVisualize}</span></div>
            <div><span className="text-gray-400">isStockLoading:</span> <span className={analysisStore.isStockLoading ? 'text-red-300' : 'text-green-300'}>{analysisStore.isStockLoading.toString()}</span></div>
            <div><span className="text-gray-400">stockError:</span> <span className="text-red-300">{analysisStore.stockError || 'null'}</span></div>
            {analysisStore.stockInfo?.response && (
              <div className="mt-1 p-1 bg-gray-700 rounded">
                <div className="text-blue-300 text-xs">Stock Info:</div>
                <div><span className="text-gray-400">name:</span> <span className="text-white">{analysisStore.stockInfo.response.shortName || 'N/A'}</span></div>
                <div><span className="text-gray-400">price:</span> <span className="text-green-300">${analysisStore.stockInfo.response.regularMarketPrice || 'N/A'}</span></div>
                <div><span className="text-gray-400">change:</span> <span className={analysisStore.stockInfo.response.regularMarketChange >= 0 ? 'text-green-300' : 'text-red-300'}>{analysisStore.stockInfo.response.regularMarketChange?.toFixed(2) || 'N/A'}</span></div>
              </div>
            )}
          </div>
        </div>

        {/* All Trades Store */}
        <div className="bg-gray-800 rounded p-2 border border-green-500">
          <div className="text-green-400 font-bold mb-1">📈 All Trades Store</div>
          <div className="space-y-0.5">
            <div><span className="text-gray-400">allTrades.length:</span> <span className="text-yellow-300">{tradesStore.allTrades?.length || 0}</span></div>
            <div><span className="text-gray-400">stocks.length:</span> <span className="text-yellow-300">{tradesStore.stocks?.length || 0}</span></div>
            <div><span className="text-gray-400">expiryDates.length:</span> <span className="text-yellow-300">{tradesStore.expiryDates?.length || 0}</span></div>
            <div><span className="text-gray-400">isLoading:</span> <span className={tradesStore.isLoading ? 'text-red-300' : 'text-green-300'}>{tradesStore.isLoading.toString()}</span></div>
            <div><span className="text-gray-400">error:</span> <span className="text-red-300">{tradesStore.error || 'null'}</span></div>
            {tradesStore.stocks?.length > 0 && (
              <div className="mt-1 p-1 bg-gray-700 rounded">
                <div className="text-green-300 text-xs">Stocks:</div>
                <div className="text-white">{tradesStore.stocks.slice(0, 5).join(', ')}{tradesStore.stocks.length > 5 ? '...' : ''}</div>
              </div>
            )}
            {tradesStore.expiryDates?.length > 0 && (
              <div className="mt-1 p-1 bg-gray-700 rounded">
                <div className="text-green-300 text-xs">Recent Expiries:</div>
                <div className="text-white">{tradesStore.expiryDates.slice(0, 3).join(', ')}{tradesStore.expiryDates.length > 3 ? '...' : ''}</div>
              </div>
            )}
          </div>
        </div>

        {/* Database Store */}
        <div className="bg-gray-800 rounded p-2 border border-purple-500">
          <div className="text-purple-400 font-bold mb-1">🗄️ Database Store</div>
          <div className="space-y-0.5">
            <div><span className="text-gray-400">isOffline:</span> <span className={databaseStore.isOffline ? 'text-red-300' : 'text-green-300'}>{databaseStore.isOffline.toString()}</span></div>
            <div><span className="text-gray-400">errorNotification.show:</span> <span className={databaseStore.errorNotification.show ? 'text-red-300' : 'text-green-300'}>{databaseStore.errorNotification.show.toString()}</span></div>
            {databaseStore.errorNotification.show && (
              <div className="mt-1 p-1 bg-gray-700 rounded">
                <div className="text-red-300 text-xs">Error:</div>
                <div className="text-red-300">{databaseStore.errorNotification.title}</div>
                <div className="text-gray-300">{databaseStore.errorNotification.message}</div>
              </div>
            )}
          </div>
        </div>

        {/* PnL Price Store */}
        <div className="bg-gray-800 rounded p-2 border border-yellow-500">
          <div className="text-yellow-400 font-bold mb-1">💰 PnL Price Store</div>
          <div className="space-y-0.5">
            <div><span className="text-gray-400">isCalculating:</span> <span className={priceStore.isCalculating ? 'text-red-300' : 'text-green-300'}>{priceStore.isCalculating.toString()}</span></div>
            <div><span className="text-gray-400">lastCalculationTime:</span> <span className="text-blue-300">{priceStore.lastCalculationTime ? new Date(priceStore.lastCalculationTime).toLocaleTimeString() : 'Never'}</span></div>
            <div><span className="text-gray-400">positionData.length:</span> <span className="text-yellow-300">{priceStore.pnlData.positionData?.length || 0}</span></div>
            <div><span className="text-gray-400">pricePoints.length:</span> <span className="text-yellow-300">{priceStore.pnlData.pricePoints?.length || 0}</span></div>
            <div><span className="text-gray-400">totalTrades:</span> <span className="text-yellow-300">{totalTradesCount}</span></div>
            <div><span className="text-gray-400">filteredTrades:</span> <span className="text-yellow-300">{filteredTradesCount}</span></div>
            {priceStore.pnlData.pricePoints?.length > 0 && (
              <div className="mt-1 p-1 bg-gray-700 rounded">
                <div className="text-yellow-300 text-xs">Price Range:</div>
                <div className="text-white">${priceStore.pnlData.pricePoints[0]} - ${priceStore.pnlData.pricePoints[priceStore.pnlData.pricePoints.length - 1]}</div>
              </div>
            )}
            {priceStore.pnlData.totals[analysisStore.targetStockPrice] && (
              <div className="mt-1 p-1 bg-gray-700 rounded">
                <div className="text-yellow-300 text-xs">At Target Price:</div>
                <div><span className="text-gray-400">PnL:</span> <span className="text-green-300">${priceStore.pnlData.totals[analysisStore.targetStockPrice].pnl?.toLocaleString() || 'N/A'}</span></div>
                <div><span className="text-gray-400">Market Value:</span> <span className="text-blue-300">${priceStore.pnlData.totals[analysisStore.targetStockPrice].marketValue?.toLocaleString() || 'N/A'}</span></div>
              </div>
            )}
          </div>
        </div>

        {/* PnL Date Store */}
        <div className="bg-gray-800 rounded p-2 border border-orange-500">
          <div className="text-orange-400 font-bold mb-1">📅 PnL Date Store</div>
          <div className="space-y-0.5">
            <div><span className="text-gray-400">isCalculating:</span> <span className={dateStore.isCalculating ? 'text-red-300' : 'text-green-300'}>{dateStore.isCalculating.toString()}</span></div>
            <div><span className="text-gray-400">positionData.length:</span> <span className="text-yellow-300">{dateStore.positionData?.length || 0}</span></div>
            <div><span className="text-gray-400">totals keys:</span> <span className="text-yellow-300">{Object.keys(dateStore.totals || {}).length}</span></div>
            <div><span className="text-gray-400">lastCalculationParams:</span> <span className="text-blue-300">{dateStore.lastCalculationParams ? 'Set' : 'null'}</span></div>
            {dateStore.lastCalculationParams && (
              <div className="mt-1 p-1 bg-gray-700 rounded">
                <div className="text-orange-300 text-xs">Last Params:</div>
                <div><span className="text-gray-400">targetPrice:</span> <span className="text-white">${dateStore.lastCalculationParams.targetStockPrice}</span></div>
                <div><span className="text-gray-400">volatility:</span> <span className="text-white">{dateStore.lastCalculationParams.volatility}%</span></div>
                <div><span className="text-gray-400">trades:</span> <span className="text-white">{dateStore.lastCalculationParams.tradesLength}</span></div>
              </div>
            )}
          </div>
        </div>

        {/* Chart Store */}
        <div className="bg-gray-800 rounded p-2 border border-pink-500">
          <div className="text-pink-400 font-bold mb-1">📊 Chart Store</div>
          <div className="space-y-0.5">
            <div><span className="text-gray-400">pnlData.length:</span> <span className="text-yellow-300">{chartStore.pnlData?.length || 0}</span></div>
            <div><span className="text-gray-400">pricePoints.length:</span> <span className="text-yellow-300">{chartStore.pricePoints?.length || 0}</span></div>
            <div><span className="text-gray-400">isLoading:</span> <span className={chartStore.isLoading ? 'text-red-300' : 'text-green-300'}>{chartStore.isLoading.toString()}</span></div>
            <div><span className="text-gray-400">error:</span> <span className="text-red-300">{chartStore.error || 'null'}</span></div>
            {chartStore.pricePoints?.length > 0 && (
              <div className="mt-1 p-1 bg-gray-700 rounded">
                <div className="text-pink-300 text-xs">Price Range:</div>
                <div className="text-white">${chartStore.pricePoints[0]} - ${chartStore.pricePoints[chartStore.pricePoints.length - 1]}</div>
              </div>
            )}
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default StoreDebugger;
