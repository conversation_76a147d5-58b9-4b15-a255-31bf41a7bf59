import React, { useState } from 'react';

/**
 * Statement Data Preview Component
 * 
 * This component displays the parsed statement data in organized sections
 * for user review before saving to the database.
 */
const StatementDataPreview = ({ data, onSave, isSaving }) => {
  const [activeTab, setActiveTab] = useState('header');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [isInternalSaving, setIsInternalSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState(null);
  const [saveError, setSaveError] = useState(null);

  if (!data) {
    return null;
  }

  // Handle saving trade confirmations to Firebase
  const handleSaveToDatabase = async () => {
    if (!data.tradeConfirmation || data.tradeConfirmation.length === 0) {
      setSaveError('No trade confirmation records to save');
      return;
    }

    if (!data.header?.accountNumber || !data.header?.statementDate) {
      setSaveError('Account number and statement date are required for saving');
      return;
    }

    setIsInternalSaving(true);
    setSaveMessage(null);
    setSaveError(null);

    try {
      const response = await fetch('/api/save-trade-confirmation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          header: {
            accountNumber: data.header.accountNumber,
            statementDate: data.header.statementDate
          },
          tradeConfirmations: data.tradeConfirmation
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to save trade confirmations');
      }

      setSaveMessage(`Successfully saved ${result.count} trade confirmation records for account ${data.header.accountNumber} on ${data.header.statementDate}`);
      
      // Optionally call the parent's onSave if it exists
      if (onSave) {
        onSave();
      }

    } catch (err) {
      console.error('Error saving trade confirmations:', err);
      setSaveError(err.message || 'Failed to save trade confirmations to database');
    } finally {
      setIsInternalSaving(false);
    }
  };

  // Determine if we should use internal saving or external
  const shouldSaveToDatabase = isSaving || isInternalSaving;
  const handleSaveClick = onSave || handleSaveToDatabase;

  if (!data) {
    return null;
  }

  const tabs = [
    { id: 'header', label: 'Statement Info', count: 1 },
    { id: 'accountMovement', label: 'Account Movement', count: data.accountMovement?.length || 0 },
    { id: 'tradeConfirmation', label: 'Trade Confirmation', count: data.tradeConfirmation?.length || 0 },
    { id: 'positionClosed', label: 'Position Closed', count: data.positionClosed?.length || 0 },
    { id: 'openPosition', label: 'Open Position', count: data.openPosition?.length || 0 },
    { id: 'financialSummary', label: 'Financial Summary', count: data.financialSummary ? 1 : 0 },
    { id: 'marginSummary', label: 'Margin Summary', count: data.marginSummary ? 1 : 0 }
  ];

  const renderHeader = () => (
    <div className="bg-white p-6 rounded-lg border">
      <h3 className="text-lg font-semibold mb-4">Statement Information</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Account Holder</label>
          <p className="mt-1 text-sm text-gray-900">{data.header?.accountHolder || 'N/A'}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Address</label>
          <div className="mt-1 text-sm text-gray-900">
            {Array.isArray(data.header?.address) && data.header.address.length > 0
              ? data.header.address.map((line, idx) => (
                  <div key={idx}>{line}</div>
                ))
              : 'N/A'}
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Statement Date</label>
          <p className="mt-1 text-sm text-gray-900">{data.header?.statementDate || 'N/A'}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Account Number</label>
          <p className="mt-1 text-sm text-gray-900">{data.header?.accountNumber || 'N/A'}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Branch Code</label>
          <p className="mt-1 text-sm text-gray-900">{data.header?.branchCode || 'N/A'}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Page Number</label>
          <p className="mt-1 text-sm text-gray-900">{data.header?.pageNumber || 'N/A'}</p>
        </div>
      </div>
    </div>
  );

  // Sorting function
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Sort data function
  const sortData = (items, key, direction) => {
    if (!key) return items;
    
    return [...items].sort((a, b) => {
      let aVal = a[key];
      let bVal = b[key];
      
      // Handle null/undefined values
      if (aVal == null) aVal = '';
      if (bVal == null) bVal = '';
      
      // Check if values are numeric
      const aNum = parseFloat(aVal);
      const bNum = parseFloat(bVal);
      const isNumeric = !isNaN(aNum) && !isNaN(bNum);
      
      if (isNumeric) {
        return direction === 'asc' ? aNum - bNum : bNum - aNum;
      }
      
      // String comparison
      const aStr = String(aVal).toLowerCase();
      const bStr = String(bVal).toLowerCase();
      
      if (direction === 'asc') {
        return aStr < bStr ? -1 : aStr > bStr ? 1 : 0;
      } else {
        return aStr > bStr ? -1 : aStr < bStr ? 1 : 0;
      }
    });
  };

  // Sort icon component
  const SortIcon = ({ column, currentSort }) => {
    if (currentSort.key !== column.key) {
      return (
        <svg className="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
        </svg>
      );
    }
    
    if (currentSort.direction === 'asc') {
      return (
        <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
        </svg>
      );
    } else {
      return (
        <svg className="w-4 h-4 ml-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      );
    }
  };

  const renderTable = (items, columns) => {
    if (!items || items.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          No data found in this section
        </div>
      );
    }

    // Sort the data
    const sortedItems = sortData(items, sortConfig.key, sortConfig.direction);

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors duration-150"
                  onClick={() => handleSort(column.key)}
                >
                  <div className="flex items-center justify-between">
                    <span>{column.label}</span>
                    <SortIcon column={column} currentSort={sortConfig} />
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedItems.map((item, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                {columns.map((column) => {
                  const value = item[column.key] || '-';
                  const isQty = column.key === 'qty';
                  const numericValue = isQty ? Number(value) : null;
                  const isPositive = isQty && numericValue > 0;
                  const isNegative = isQty && numericValue < 0;
                  
                  return (
                    <td key={column.key} className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={
                        isQty 
                          ? isPositive 
                            ? 'text-green-600 font-medium' 
                            : isNegative 
                              ? 'text-red-600 font-medium' 
                              : 'text-gray-900'
                          : 'text-gray-900'
                      }>
                        {isQty && numericValue !== null ? 
                          (numericValue > 0 ? `+${numericValue}` : numericValue.toString()) : 
                          value
                        }
                      </span>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const renderAccountMovement = () => {
    const columns = [
      { key: 'date', label: 'Date' },
      { key: 'description', label: 'Description' },
      { key: 'reference', label: 'Reference' },
      { key: 'debit', label: 'Debit' },
      { key: 'credit', label: 'Credit' },
      { key: 'balance', label: 'Balance' },
      { key: 'currency', label: 'Currency' }
    ];
    return renderTable(data.accountMovement, columns);
  };

  const renderTradeConfirmation = () => {
    const columns = [
      { key: 'date', label: 'Trade Date' },
      { key: 'instrument', label: 'Inst' },
      { key: 'expiry', label: 'Expiry' },
      { key: 'strikePrice', label: 'Strike Price' },
      { key: 'optionType', label: 'Type' },
      { key: 'qty', label: 'QTY' },
      { key: 'premium', label: 'Premium' },
      { key: 'orderNo', label: 'Order No' },
      { key: 'status', label: 'Status' },
      { key: 'extendedHours', label: 'Extended Hours' },
      { key: 'exchangeFee', label: 'Exchange Fee' },
      { key: 'commission', label: 'Commission' }
    ];
    
    // Use the data directly since qty field already contains the quantity with proper sign
    const transformedData = data.tradeConfirmation || [];
    
    return renderTable(transformedData, columns);
  };

  const renderPositionClosed = () => {
    const columns = [
      { key: 'instrument', label: 'Instrument' },
      { key: 'openDate', label: 'Open Date' },
      { key: 'closeDate', label: 'Close Date' },
      { key: 'quantity', label: 'Quantity' },
      { key: 'openPrice', label: 'Open Price' },
      { key: 'closePrice', label: 'Close Price' },
      { key: 'pnl', label: 'P&L' },
      { key: 'currency', label: 'Currency' }
    ];
    return renderTable(data.positionClosed, columns);
  };

  const renderOpenPosition = () => {
    const columns = [
      { key: 'instrument', label: 'Instrument' },
      { key: 'position', label: 'Position' },
      { key: 'quantity', label: 'Quantity' },
      { key: 'averagePrice', label: 'Average Price' },
      { key: 'marketPrice', label: 'Market Price' },
      { key: 'marketValue', label: 'Market Value' },
      { key: 'unrealizedPnl', label: 'Unrealized P&L' },
      { key: 'currency', label: 'Currency' }
    ];
    return renderTable(data.openPosition, columns);
  };

  const renderFinancialSummary = () => {
    if (!data.financialSummary) {
      return <div className="text-center py-8 text-gray-500">No financial summary data found</div>;
    }

    return (
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Financial Position Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Opening Balance</label>
            <p className="mt-1 text-sm text-gray-900">{data.financialSummary.openingBalance || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Closing Balance</label>
            <p className="mt-1 text-sm text-gray-900">{data.financialSummary.closingBalance || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Net Change</label>
            <p className="mt-1 text-sm text-gray-900">{data.financialSummary.netChange || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Total Deposits</label>
            <p className="mt-1 text-sm text-gray-900">{data.financialSummary.totalDeposits || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Total Withdrawals</label>
            <p className="mt-1 text-sm text-gray-900">{data.financialSummary.totalWithdrawals || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Currency</label>
            <p className="mt-1 text-sm text-gray-900">{data.financialSummary.currency || 'N/A'}</p>
          </div>
        </div>
      </div>
    );
  };

  const renderMarginSummary = () => {
    if (!data.marginSummary) {
      return <div className="text-center py-8 text-gray-500">No margin summary data found</div>;
    }

    return (
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Margin Position Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Initial Margin</label>
            <p className="mt-1 text-sm text-gray-900">{data.marginSummary.initialMargin || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Maintenance Margin</label>
            <p className="mt-1 text-sm text-gray-900">{data.marginSummary.maintenanceMargin || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Available Margin</label>
            <p className="mt-1 text-sm text-gray-900">{data.marginSummary.availableMargin || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Margin Utilization</label>
            <p className="mt-1 text-sm text-gray-900">{data.marginSummary.marginUtilization || 'N/A'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Currency</label>
            <p className="mt-1 text-sm text-gray-900">{data.marginSummary.currency || 'N/A'}</p>
          </div>
        </div>
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'header':
        return renderHeader();
      case 'accountMovement':
        return renderAccountMovement();
      case 'tradeConfirmation':
        return renderTradeConfirmation();
      case 'positionClosed':
        return renderPositionClosed();
      case 'openPosition':
        return renderOpenPosition();
      case 'financialSummary':
        return renderFinancialSummary();
      case 'marginSummary':
        return renderMarginSummary();
      default:
        return <div>Select a tab to view data</div>;
    }
  };

  return (
    <div className="bg-white shadow-md rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Statement Data Preview</h2>
        <button
          onClick={handleSaveClick}
          disabled={shouldSaveToDatabase}
          className={`px-6 py-2 rounded-md text-white font-medium ${
            shouldSaveToDatabase
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
          }`}
        >
          {shouldSaveToDatabase ? (
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving Trade Confirmations...
            </div>
          ) : (
            'Save Trade Confirmations'
          )}
        </button>
      </div>

      {/* Success/Error Messages */}
      {saveMessage && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{saveMessage}</p>
            </div>
          </div>
        </div>
      )}

      {saveError && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{saveError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default StatementDataPreview;
