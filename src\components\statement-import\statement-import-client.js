import React, { useState } from 'react';
import PdfUploadForm from './pdf-upload-form';
import StatementDataPreview from './statement-data-preview';
import ErrorNotification from '../error-notification';

/**
 * Statement Import Client Component
 * 
 * Handles the statement import workflow:
 * 1. PDF file upload with password handling
 * 2. PDF parsing and data extraction
 * 3. Data preview and validation
 * 
 * Note: Database save operations are handled by the StatementDataPreview component
 */
const StatementImportClient = () => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [parsedData, setParsedData] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');

  const handleFileUpload = async (file, filePassword) => {
    setUploadedFile(file);
    setError(null);
    setSuccessMessage('');
    setParsedData(null);

    if (!file) {
      setError('Please select a PDF file to upload.');
      return;
    }

    setIsProcessing(true);

    try {
      const formData = new FormData();
      formData.append('statement', file);
      if (filePassword) {
        formData.append('password', filePassword);
      }

      const response = await fetch('/api/parse-statement', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to parse statement');
      }

      setParsedData(result.data);
      setSuccessMessage(`Successfully parsed statement. Found ${getTotalRecords(result.data)} records.`);
    } catch (err) {
      console.error('Error parsing statement:', err);
      setError(err.message || 'Failed to parse the PDF statement. Please check the file and password.');
    } finally {
      setIsProcessing(false);
    }
  };

  const getTotalRecords = (data) => {
    if (!data) return 0;
    
    let total = 0;
    if (data.accountMovement) total += data.accountMovement.length;
    if (data.tradeConfirmation) total += data.tradeConfirmation.length;
    if (data.positionClosed) total += data.positionClosed.length;
    if (data.openPosition) total += data.openPosition.length;
    
    return total;
  };

  const clearError = () => setError(null);
  const clearSuccess = () => setSuccessMessage('');

  return (
    <div className="space-y-6">
      {/* Error Notification */}
      {error && (
        <ErrorNotification
          message={error}
          onClose={clearError}
        />
      )}

      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex justify-between items-start">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-700">{successMessage}</p>
              </div>
            </div>
            <button
              onClick={clearSuccess}
              className="text-green-400 hover:text-green-600"
            >
              <span className="sr-only">Close</span>
              <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* PDF Upload Form */}
      <PdfUploadForm
        onFileUpload={handleFileUpload}
        isProcessing={isProcessing}
        uploadedFile={uploadedFile}
      />

      {/* Statement Data Preview */}
      {parsedData && (
        <StatementDataPreview
          data={parsedData}
        />
      )}
    </div>
  );
};

export default StatementImportClient;
