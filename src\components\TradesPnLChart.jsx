import React, { useMemo, useRef, useEffect } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import { Line } from 'react-chartjs-2';
import annotationPlugin from 'chartjs-plugin-annotation';
import zoomPlugin from 'chartjs-plugin-zoom';
import { Crosshair } from '../utils/crosshair-plugin';
import './chart-styles.css'; // Assuming this contains relevant styles

// Register Chart.js components and plugins
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  annotationPlugin,
  zoomPlugin,
  Crosshair
);

/**
 * TradesPnLChart - A passive, self-contained P&L chart component
 * 
 * @param {Array} pnlData - Array of trade P&L objects with pnl_${price} properties
 * @param {Array} pricePoints - Array of price points for the x-axis
 * @param {boolean} isLoading - Loading state
 * @param {string|null} error - Error message if any
 * @param {string} selectedStockSymbol - Stock symbol for display
 * @param {number} marketPrice - Current market price for annotation line
 * @param {Object} yAxisBounds - Optional fixed y-axis bounds {min, max} for consistent scaling
 */
const TradesPnLChart = ({ 
  pnlData = [], 
  pricePoints = [], 
  isLoading = false, 
  error = null, 
  selectedStockSymbol = '', 
  marketPrice = null,
  yAxisBounds = null,
  targetPrice = null // <-- Add targetPrice prop
}) => {
  const chartRef = useRef(null);

  const aggregatedPnlData = useMemo(() => {
    if (!pnlData || pnlData.length === 0 || !pricePoints || pricePoints.length === 0) {
      return [];
    }

    // Aggregate P&L for each price point across all trades
    return pricePoints.map(price => {
      let totalPnlAtPrice = 0;
      pnlData.forEach(tradePnl => {
        const pnlValue = tradePnl[`pnl_${price}`];
        if (typeof pnlValue === 'number') {
          totalPnlAtPrice += pnlValue;
        }
      });
      return {
        x: price, // Price point
        y: totalPnlAtPrice, // Aggregated P&L
      };
    });
  }, [pnlData, pricePoints]);

  const chartData = useMemo(() => ({
    datasets: [
      {
        label: `Aggregated P&L for ${selectedStockSymbol || 'Selected Stock'}`,
        data: aggregatedPnlData,
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        tension: 0.1,
        pointRadius: 3,
        pointHoverRadius: 5,
      },
    ],
  }), [aggregatedPnlData, selectedStockSymbol]);

  const chartOptions = useMemo(() => {
    const annotations = {};
    if (typeof marketPrice === 'number') {
      annotations.marketPriceLine = {
        type: 'line',
        xMin: marketPrice,
        xMax: marketPrice,
        borderColor: 'rgb(255, 99, 132)',
        borderWidth: 2,
        borderDash: [6, 6],
        label: {
          content: `Mkt: ${marketPrice.toFixed(2)}`,
          enabled: true,
          position: 'start',
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          color: 'white',
          font: { size: 10 },
          padding: 2,
          yAdjust: -10,
        },
      };
    }
    if (typeof targetPrice === 'number') {
      annotations.targetPriceLine = {
        type: 'line',
        xMin: targetPrice,
        xMax: targetPrice,
        borderColor: 'rgb(6, 182, 212)', // cyan-500
        borderWidth: 3,
        borderDash: [3, 3],
        label: {
          content: `Target: ${targetPrice}`,
          enabled: true,
          position: 'end',
          backgroundColor: 'rgba(6, 182, 212, 0.7)',
          color: 'white',
          font: { size: 10, weight: 'bold' },
          padding: 2,
          yAdjust: -10,
        },
      };
    }

    return {
      responsive: true,
      maintainAspectRatio: false,
      animation: false,
      interaction: {
        mode: 'index',
        intersect: false,
      },
      scales: {
        x: {
          type: 'linear',
          title: {
            display: true,
            text: 'Stock Price',
            font: { size: 12, weight: 'bold' },
          },
          ticks: {
            font: { size: 10 },
            // Consider adding a callback for formatting if needed
          },
          grid: {
            color: 'rgba(200, 200, 200, 0.2)',
          },
        },        y: {
          title: {
            display: true,
            text: 'Aggregated P&L',
            font: { size: 12, weight: 'bold' },
          },
          ticks: {
            font: { size: 10 },
            callback: function (value) {
              return '$' + value.toLocaleString();
            },
          },          grid: {
            color: 'rgba(200, 200, 200, 0.2)',
          },          // Use fixed bounds if provided for consistent scaling with safety checks
          ...(yAxisBounds && 
              yAxisBounds.min !== undefined && 
              yAxisBounds.max !== undefined && 
              typeof yAxisBounds.min === 'number' && 
              typeof yAxisBounds.max === 'number' &&
              yAxisBounds.min < yAxisBounds.max && {
            min: yAxisBounds.min,
            max: yAxisBounds.max
          })
        },
      },
      plugins: {
        legend: {
          position: 'top',
          labels: { font: { size: 12 } },
        },
        title: {
          display: true,
          text: `Profit/Loss Chart for ${selectedStockSymbol || 'Trades'}`,
          font: { size: 16, weight: 'bold' },
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: function (context) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                label += new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(context.parsed.y);
              }
              return label;
            },
          },
        },        annotation: {
          annotations: annotations,
        },
        crosshair: {
          line: {
            color: 'rgba(100, 100, 100, 0.7)',
            width: 1,
            dashPattern: [5, 5]
          },
          sync: {
            enabled: true,
            group: 1,
            suppressTooltips: false
          },
          snap: {
            enabled: true
          }
        },
        zoom: {
          pan: {
            enabled: true,
            mode: 'xy',
          },
          zoom: {
            wheel: {
              enabled: true,
              speed: 0.1
            },
            pinch: {
              enabled: true
            },
            mode: 'xy',
            onZoomComplete: function({chart: _chart}) {
              // Optional: Add any zoom complete logic here
            }
          },
          limits: {
            x: {
              min: 'original',
              max: 'original',
              minRange: 1
            },
            y: {
              min: 'original',
              max: 'original',
              minRange: 100
            }
          }
        },      }
    };
  }, [selectedStockSymbol, marketPrice, yAxisBounds, targetPrice]);

  // Function to reset zoom
  const resetZoom = () => {
    if (chartRef.current) {
      chartRef.current.resetZoom();
    }
  };

  // Add double-click event listener to chart canvas
  useEffect(() => {
    const chart = chartRef.current;
    if (!chart) return;

    const handleDoubleClick = () => {
      resetZoom();
    };

    // Add double-click event listener to the canvas
    chart.canvas.addEventListener('dblclick', handleDoubleClick);

    // Cleanup event listener
    return () => {
      if (chart.canvas) {
        chart.canvas.removeEventListener('dblclick', handleDoubleClick);
      }
    };
  }, []);

  if (isLoading) {
    return <div className="chart-container" style={{ height: '400px' }}><p className="text-center py-4">Loading chart data...</p></div>;
  }

  if (error) {
    return <div className="chart-container" style={{ height: '400px' }}><p className="text-center py-4 text-red-500">Error loading chart data: {error}</p></div>;
  }

  if (aggregatedPnlData.length === 0) {
    return (
      <div className="chart-container" style={{ height: '400px' }}>
        <p className="text-center py-4 text-gray-500">
          No P&L data available to display. Please ensure trades are loaded and analysis parameters are set.
        </p>
      </div>
    );
  }

  return (
    <div className="chart-container bg-white p-4 rounded-lg shadow-md relative" style={{ height: '400px' }}>
      {/* Reset Zoom Button */}
      <div className="absolute top-2 right-2 z-10">
        <button
          onClick={resetZoom}
          className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded border border-blue-600 shadow-sm hover:shadow-md transition-colors"
          title="Reset zoom and pan to original view"
        >
          Reset Zoom
        </button>
      </div>

      {/* Chart Instructions */}
      <div className="absolute bottom-2 left-2 z-10">
        <div className="text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded">
          Mouse wheel: zoom • Drag: pan • Double-click: reset
        </div>
      </div>

      <Line ref={chartRef} options={chartOptions} data={chartData} />
    </div>
  );
};

export default TradesPnLChart;
