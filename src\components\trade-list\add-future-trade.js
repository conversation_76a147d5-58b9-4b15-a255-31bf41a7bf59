import React, { useState } from 'react';

/**
 * Add Future Trade Component
 * 
 * This component allows adding a future trade directly to Firestore.
 */
const AddFutureTrade = () => {
  // State for form data
  const [formData, setFormData] = useState({
    ticker: 'HTIM5',
    type: 'Future',
    expiry: '2025-06',
    strike: 'N/A',
    quantity: 2,
    price: 5600.00
  });
  
  // State for submission status
  const [status, setStatus] = useState({
    submitting: false,
    success: false,
    error: null
  });
  
  // State for response data
  const [responseData, setResponseData] = useState(null);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    setStatus({
      submitting: true,
      success: false,
      error: null
    });
    
    try {
      // Prepare the trade data
      const tradeData = {
        ticker: formData.ticker,
        type: formData.type,
        expiry: formData.expiry,
        strike: formData.strike,
        quantity: parseInt(formData.quantity),
        price: parseFloat(formData.price)
      };
      
      console.log('Submitting trade data:', tradeData);
      
      // Submit the trade data to the server
      const response = await fetch('/api/firebase-add-trade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(tradeData)
      });
      
      const data = await response.json();
      console.log('Server response:', data);
      
      setResponseData(data);
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to add trade');
      }
      
      setStatus({
        submitting: false,
        success: true,
        error: null
      });
    } catch (error) {
      console.error('Error adding trade:', error);
      
      setStatus({
        submitting: false,
        success: false,
        error: error.message
      });
    }
  };

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden p-4">
      <h2 className="text-lg font-bold text-gray-800 mb-4">Add Future Trade to Firestore</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Ticker</label>
            <input
              type="text"
              name="ticker"
              value={formData.ticker}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <select
              name="type"
              value={formData.type}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              required
            >
              <option value="Future">Future</option>
              <option value="Call">Call</option>
              <option value="Put">Put</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Expiry</label>
            <input
              type="text"
              name="expiry"
              value={formData.expiry}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Strike</label>
            <input
              type="text"
              name="strike"
              value={formData.strike}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
            <input
              type="number"
              name="quantity"
              value={formData.quantity}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
            <input
              type="number"
              name="price"
              value={formData.price}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
              step="0.01"
              required
            />
          </div>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300"
            disabled={status.submitting}
          >
            {status.submitting ? 'Adding...' : 'Add Future Trade'}
          </button>
        </div>
      </form>
      
      {status.success && (
        <div className="mt-4 p-3 bg-green-100 text-green-800 rounded">
          <p className="font-semibold">Trade added successfully!</p>
          <p className="text-sm">Refresh the page to see the updated trade list.</p>
        </div>
      )}
      
      {status.error && (
        <div className="mt-4 p-3 bg-red-100 text-red-800 rounded">
          <p className="font-semibold">Error adding trade:</p>
          <p className="text-sm">{status.error}</p>
        </div>
      )}
      
      {responseData && (
        <div className="mt-4">
          <h3 className="text-md font-semibold mb-2">Server Response:</h3>
          <div className="bg-gray-100 p-4 rounded mb-4 h-[100px] overflow-y-auto">
            <pre className="text-xs">{JSON.stringify(responseData, null, 2)}</pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddFutureTrade;
