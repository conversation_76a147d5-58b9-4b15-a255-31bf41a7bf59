import { create } from 'zustand';
import useAllTradesStore from './useAllTradesStore';
import { calculatePositionPremium, calculateDaysToExpiry, parseExpiryDate, formatDateString } from '../utils/position-utils';

/**
 * Zustand store for calculating and storing PnL and market value (Calc Prem)
 * for all trades at various dates (from today to max expiry date).
 *
 * This state is the single source of truth for the trades time decay chart component.
 */

// Generate date points from today to maxExpiryDate
const getDatePoints = (maxExpiryDate) => {
  const today = new Date();
  const maxDate = new Date(maxExpiryDate);
  const points = [];
  
  // Normalize dates to midnight to avoid time comparison issues
  const normalizedToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const normalizedMaxDate = new Date(maxDate.getFullYear(), maxDate.getMonth(), maxDate.getDate());
  
  // Start from today
  let currentDate = new Date(normalizedToday);
  
  // Generate daily points up to and including max expiry
  while (currentDate <= normalizedMaxDate) {
    points.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return points;
};

// Calculate PnL and market value for all trades at all date points
const calculatePnLAtVariousDates = ({ trades, targetStockPrice, volatility, riskFreeRate }) => {
  if (!Array.isArray(trades) || trades.length === 0) return { positionData: [], totals: {} };
    // Find the maximum expiry date from all trades
  const maxExpiryDate = trades.reduce((max, trade) => {
    if (!trade.ExpiryDate) return max;
    // Parse the expiry date to handle monthly options (YY-MM, YYYY-MM) properly
    const parsedExpiryDate = parseExpiryDate(trade.ExpiryDate);
    const expiryDate = new Date(parsedExpiryDate);
    return expiryDate > max ? expiryDate : max;
  }, new Date());
  
  const datePoints = getDatePoints(maxExpiryDate);
  const positionData = [];
  const totals = {};
    // Initialize totals for each date
  datePoints.forEach(date => {
    const dateStr = formatDateString(date);
    totals[dateStr] = { marketValue: 0, pnl: 0 };
  });

  trades.forEach(trade => {
    const positionValues = { id: trade.id, trade };
      datePoints.forEach(date => {
      const dateStr = formatDateString(date);
      
      // Calculate days to expiry using the proper utility function
      // This correctly handles monthly options (YYYY-MM) vs weekly options (YYYY-MM-DD)
      const daysToExpiry = calculateDaysToExpiry(trade.ExpiryDate, date);
        // Create a copy of the position with the adjusted days to expiry
      const positionWithDaysToExpiry = {
        ...trade,
        daysToExpiry: daysToExpiry // Use days remaining at target date
      };

      // Calculate premium using the correct calculatePositionPremium function
      // This matches the working PnL table implementation
      const premium = calculatePositionPremium(
        positionWithDaysToExpiry,
        targetStockPrice,
        riskFreeRate,
        volatility
      );

      // Calculate market value
      const marketValue = Math.round(trade.quantity * premium);

      // Calculate PnL as market value minus debit/credit
      const pnl = marketValue - (trade.debitCredit || trade.premium || 0);

      positionValues[dateStr] = { premium, marketValue, pnl };
      totals[dateStr].marketValue += marketValue;
      totals[dateStr].pnl += pnl;
    });
    
    positionData.push(positionValues);
  });

  return { positionData, totals };
};

const usePnLAtVariousDateStore = create((set, get) => ({
  // Core data
  positionData: [],
  totals: {},
  isCalculating: false,
  lastCalculationParams: null,

  // Actions
  setPositionData: (data) => set({ positionData: data }),
  setTotals: (totals) => set({ totals }),
  setIsCalculating: (calculating) => set({ isCalculating: calculating }),
  // Main calculation function
  calculatePnLAtVariousDates: async ({ trades, targetStockPrice, volatility, riskFreeRate }) => {
    // Use provided trades parameter, fallback to store if not provided (for backward compatibility)
    const tradesToUse = trades || useAllTradesStore.getState().allTrades;
    
    // Check if we need to recalculate
    const currentParams = { targetStockPrice, volatility, riskFreeRate, tradesLength: tradesToUse.length };
    const lastParams = get().lastCalculationParams;
    
    if (lastParams && 
        lastParams.targetStockPrice === currentParams.targetStockPrice &&
        lastParams.volatility === currentParams.volatility &&
        lastParams.riskFreeRate === currentParams.riskFreeRate &&
        lastParams.tradesLength === currentParams.tradesLength) {
      // No need to recalculate
      return;
    }

    set({ isCalculating: true });

    try {
      const { positionData, totals } = calculatePnLAtVariousDates({
        trades: tradesToUse,
        targetStockPrice,
        volatility,
        riskFreeRate
      });

      set({
        positionData,
        totals,
        lastCalculationParams: currentParams,
        isCalculating: false
      });
    } catch (error) {
      console.error('Error calculating PnL at various dates:', error);
      set({ isCalculating: false });
    }
  },

  // Helper function to get chart data for a specific date range
  getChartData: () => {
    const { totals } = get();
    if (!totals || Object.keys(totals).length === 0) return [];

    return Object.keys(totals)
      .sort()
      .map(date => ({
        date,
        total: totals[date].pnl,
        marketValue: totals[date].marketValue
      }));
  },

  // Helper function to get date points from the data
  getDatePoints: () => {
    const { totals } = get();
    if (!totals || Object.keys(totals).length === 0) return [];

    return Object.keys(totals).sort();
  },

  // Selector for filtered/sorted data (by expiry, symbol, etc)
  getFilteredData: (filters = {}) => {
    const { positionData, totals } = get();
    const datePoints = get().getDatePoints();
    let filtered = positionData;

    if (filters.expiry) {
      filtered = filtered.filter(d => d.trade.ExpiryDate === filters.expiry);
    }

    if (filters.stockSymbol) {
      const symbol = filters.stockSymbol.toUpperCase();
      filtered = filtered.filter(d => {
        const ticker = (d.trade.ticker || '').toUpperCase();
        return ticker.startsWith(symbol);
      });
    }

    // Transform the data structure to match what the table expects
    // Convert from nested date objects to flat properties like the price store
    const transformedPositionData = filtered.map(position => {
      const transformedPosition = {
        id: position.id,
        trade: position.trade
      };

      // Add flattened date data
      datePoints.forEach(dateStr => {
        const dateData = position[dateStr];
        if (dateData) {
          transformedPosition[`premium_${dateStr}`] = dateData.premium;
          transformedPosition[`marketValue_${dateStr}`] = dateData.marketValue;
          transformedPosition[`pnl_${dateStr}`] = dateData.pnl;
        } else {
          transformedPosition[`premium_${dateStr}`] = 0;
          transformedPosition[`marketValue_${dateStr}`] = 0;
          transformedPosition[`pnl_${dateStr}`] = 0;
        }
      });

      return transformedPosition;
    });

    return {
      positionData: transformedPositionData,
      totals,
      datePoints
    };  },  // Calculate global P&L bounds for consistent y-axis scaling
  getGlobalPnLBounds: () => {
    const { totals, positionData } = get();
      if (!totals || Object.keys(totals).length === 0) {
      console.log('No date data available for bounds calculation');
      return null; // Return null instead of {min: 0, max: 0}
    }

    // Ensure positionData is available for individual position checks
    if (!positionData || !Array.isArray(positionData)) {
      console.log('No position data available for individual checks');
    }

    let minPnL = Infinity;
    let maxPnL = -Infinity;

    // Check aggregated totals at each date point
    Object.values(totals).forEach(dateTotal => {
      const totalPnL = dateTotal?.pnl;
      if (typeof totalPnL === 'number') {
        minPnL = Math.min(minPnL, totalPnL);
        maxPnL = Math.max(maxPnL, totalPnL);
      }
    });

    // If no valid values found, return null
    if (minPnL === Infinity || maxPnL === -Infinity) {
      console.log('No valid P&L values found for date bounds calculation');
      return null;
    }    // Add 20% padding to ensure data fits comfortably and round to major intervals
    const range = maxPnL - minPnL;
    const basePadding = Math.max(range * 0.2, 100); // At least $100 padding, now 20%
    
    // Calculate preliminary bounds
    const prelimMin = minPnL - basePadding;
    const prelimMax = maxPnL + basePadding;
    
    // Determine appropriate rounding interval based on range
    const totalRange = prelimMax - prelimMin;
    let roundingInterval;
    
    if (totalRange < 1000) {
      roundingInterval = 100; // Round to nearest $100 for small ranges
    } else if (totalRange < 5000) {
      roundingInterval = 500; // Round to nearest $500 for medium ranges
    } else if (totalRange < 20000) {
      roundingInterval = 1000; // Round to nearest $1000 for large ranges
    } else {
      roundingInterval = 2500; // Round to nearest $2500 for very large ranges
    }
    
    // Round bounds to major intervals
    let roundedMin = Math.floor(prelimMin / roundingInterval) * roundingInterval;
    let roundedMax = Math.ceil(prelimMax / roundingInterval) * roundingInterval;

    // DYNAMIC RESCALING: Check if any data points exceed the calculated bounds
    // This ensures all data is always visible even if calculations change
    let needsRescaling = false;
    let actualMin = minPnL;
    let actualMax = maxPnL;
      // Check all individual position data points as well
    if (positionData && Array.isArray(positionData)) {
      positionData.forEach(position => {
        Object.keys(position).forEach(key => {
          if (key.includes('pnl_')) {
            const pnlValue = position[key];
            if (typeof pnlValue === 'number') {
              if (pnlValue < roundedMin || pnlValue > roundedMax) {
                needsRescaling = true;
                actualMin = Math.min(actualMin, pnlValue);
                actualMax = Math.max(actualMax, pnlValue);
              }
            }
          }
        });
      });
    }
    
    // If rescaling is needed, recalculate bounds with expanded range
    if (needsRescaling) {
      console.log('Date data exceeds bounds, rescaling...', { 
        originalBounds: { min: roundedMin, max: roundedMax },
        actualRange: { min: actualMin, max: actualMax }
      });
      
      // Recalculate with expanded range and additional padding
      const expandedRange = actualMax - actualMin;
      const expandedPadding = Math.max(expandedRange * 0.25, 200); // 25% padding for expanded range
      
      const expandedMin = actualMin - expandedPadding;
      const expandedMax = actualMax + expandedPadding;
      
      // Determine new rounding interval for expanded range
      const expandedTotalRange = expandedMax - expandedMin;
      if (expandedTotalRange < 1000) {
        roundingInterval = 100;
      } else if (expandedTotalRange < 5000) {
        roundingInterval = 500;
      } else if (expandedTotalRange < 20000) {
        roundingInterval = 1000;
      } else {
        roundingInterval = 2500;
      }
      
      // Apply new rounded bounds
      roundedMin = Math.floor(expandedMin / roundingInterval) * roundingInterval;
      roundedMax = Math.ceil(expandedMax / roundingInterval) * roundingInterval;
    }

    const bounds = {
      min: roundedMin,
      max: roundedMax
    };

    console.log('Calculated date P&L bounds:', bounds);
    return bounds;
  },

  // Reset state
  reset: () => set({
    positionData: [],
    totals: {},
    isCalculating: false,
    lastCalculationParams: null
  })
}));

export default usePnLAtVariousDateStore;
